<template>
  <div>
    <!-- 国外  -->
    <template v-if="isAbroad">
      <Abroad />
    </template>
    <!-- 国内 -->
    <template v-else>
      <Domestic />
    </template>
  </div>
</template>

<script>
import Domestic from '@/views/MyClient/UserManagement/Components/Domestic.vue'
import Abroad from '@/views/MyClient/UserManagement/Components/Abroad.vue'
// import { isAbroadHref } from '@/api/client'
import { isAbroad } from '@/utils/judge'
export default {
  components: {
    Domestic,
    Abroad
  },
  computed: {
    // 国外链接
    isAbroad() {
      return isAbroad()
    }
  }
}
</script>

<style lang="scss"></style>
