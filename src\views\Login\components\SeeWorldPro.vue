<template>
  <div class="login">
    <!-- 头部 -->
    <div class="Chead">
      <div class="Chead-main">
        <div class="Chead-info login_bg">
          <div class="logo-container">
            <img class="logo" src="@/assets/img/login/seeworldpro/header-footer-logo.png" alt="logo" />
            <!-- 这里是标语 -->
            <div class="sologan">
              <span>PetSafe® GPS Pet Tracker</span>
            </div>
          </div>
          <div id="language_box" style="display:block;float: right;padding-right:20px;">
            <el-dropdown @command="selectLanguage">
              <div class="language">
                <img class="earthIcon" :src="resizeImg('yLinkEarth.png')" alt="earthIcon" />
                <span class="el-dropdown-link">{{ language[loginForm.lang] }}</span>
                <i class="el-icon-caret-bottom"></i>
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, index) in languageMenu" :key="index" :command="item.lang">{{ item.text }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
    <!-- 登录表单 -->
    <div class="loginBk">
      <div class="form-main">
        <div class="form-warp">
          <div class="form-logo">
            <img src="@/assets/img/login/seeworldpro/logo.png" alt="formlog" />
          </div>
          <div class="account-input-container">
            <div class="input-label">
              <img src="@/assets/img/login/carebodagps/account.png" alt="account" />
            </div>
            <input class="account-input" v-model="loginForm.name" type="text" :placeholder="$t('Cyllha7ZgGJqvuwHsEqaD')" @keyup.enter="goLogin" />
          </div>
          <div class="pw-input-container">
            <div class="input-label">
              <img class="pw" src="@/assets/img/login/carebodagps/password.png" alt="trackwise" />
            </div>
            <input class="pw-input" v-model="loginForm.password" type="password" :placeholder="$t('_Ir0v6itPxvLoVX8jyypM')" @keyup.enter="goLogin" />
          </div>
          <div class="experience">
            <div class="remember-checkbox">
              <input type="checkbox" id="remPwd" v-model="remPwd" @click="remeberPwd" />
              <span class="label">{{ $t('hjYHD87gdSZHBXCH8FJ9H') }}</span>
            </div>
            <a href="#" id="fogPwd" @click="toFindPwd">{{ $t('cbuuwxgh7TX67XFGGCHCH') }}</a>
          </div>
          <input type="button" class="loginEvent" :value="loginText" @click="goLogin" />
          <!--   <center>
            <a href="#" class="demoBtn" @click="loginTest">{{ $t('HX8UYguGBXCUYGWYGFHYU') }}>></a>
          </center> -->
        </div>
      </div>
    </div>
    <div class="loginfooter">
      <!--<div class="footer-qrcode">
        <div class="android">
          <img class="icon noactive" src="@/assets/img/login/trackwise/android.png" alt="android" />
          <img class="icon active" src="@/assets/img/login/mitrackgps/android-active.png" alt="android" />
          <img class="qrcode_img" src="@/assets/img/login/carebodagps/android-qrcode.png" alt="" />
        </div>
        <div class="ios">
          <img class="icon noactive" src="@/assets/img/login/trackwise/ios.png" alt="ios" />
          <img class="icon active" src="@/assets/img/login/mitrackgps/ios-active.png" alt="ios" />
          <img class="qrcode_img" src="@/assets/img/login/ios.png" alt="" />
        </div>
      </div>-->
      <div class="footer-logo">
        <img class="logo" src="@/assets/img/login/seeworldpro/header-footer-logo.png" alt="logo" />
        <div class="sologan">
          <span>PetSafe® GPS Pet Tracker</span>
        </div>
      </div>
      <div class="footer-link">
        <div class="copyright">
          <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" style="display:inline-block;">Copyright©2018-2025 All Rights Reserved</a>
          <span class="divider">|</span>
          <a href="#" style="display:inline-block;">{{ $t('0-v98dDMuY2bdr08YIehg') }}</a>
          <span class="divider">|</span>
          <a href="#" style="display:inline-block;">{{ $t('jD1yQ2o2qqCboJHfrm7uS') }}</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LoginMixin from '@/mixins/login.js'
export default {
  name: 'Login',
  mixins: [LoginMixin]
}
</script>
<style lang="scss" scoped>
.login {
  .Chead {
    background-color: #ffffff;
    height: 60px;
    width: auto;
    min-width: 1257px;
    a {
      color: #666;
      font-size: 14px;
    }
    .Chead-main {
      margin: 0 auto;
      height: 200px;
      width: 1180px;

      .login_bg {
        height: 60px;
        line-height: 60px;
        width: 1180px;
        z-index: 101;
        overflow: hidden;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .language {
          display: flex;
          align-items: center;
          .earthIcon {
            width: 17px;
            height: 17px;
            margin-right: 3px;
          }
        }
      }
    }
  }
  .footer-logo,
  .logo-container {
    height: 100%;
    display: flex;
    align-items: center;
    .logo {
      margin-right: 14px;
    }
    .sologan {
      border-left: 1px solid #d8d8d8;
      width: 177px;
      height: 14px;
      text-align: center;
      justify-content: center;
      color: rgba(99, 99, 99, 1);
      font-size: 14px;
      line-height: 14px;
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      font-weight: 400;
    }
    .divider {
      width: 1px;
      height: 22px;
      background-color: #d8d8d8;
      margin: 0 14px;
    }
    .text {
      color: #a9a9a9;
      font-size: 24px;
      line-height: 33px;
    }
  }
  .loginBk {
    height: 587px;
    padding-top: 114px;
    min-width: 1280px;
    box-sizing: border-box;
    background-image: url('~@/assets/img/login/seeworldpro/background.png');
    background-size: cover;
    .form-main {
      width: 1180px;
      margin: 0 auto;
      display: flex;
      justify-content: center;
    }
    .form-warp {
      font-size: 16px;
      padding: 37px 24px;
      width: 406px;
      height: 392px;
      /*text-align: center;*/
      position: relative;
      box-sizing: border-box;
      border-radius: 11px;
      background-color: #fff;
      .form-logo {
        display: flex;
        justify-content: center;
        position: relative;
        height: 46px;
        img {
          position: absolute;
          top: -91px;
          left: 124px;
          width: 114px;
          height: 115px;
        }
        div {
          position: absolute;
          left: 2px;
          bottom: 22px;
          color: #516283;
          font-size: 18px;
          line-height: 25px;
        }
      }
      .account-input-container,
      .pw-input-container {
        position: relative;
        width: 358px;
        height: 43px;
        border-radius: 10px;
        border: 1px solid #f1f1f1;
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        margin-top: 20px;
        font-size: 22px;

        .input-label {
          flex-shrink: 0;
          width: 82px;
          padding-left: 10px;
          height: 37px;
          border-right: 1px solid #f1f1f1;
          display: flex;
          justify-content: center;
          align-items: center;
          box-sizing: border-box;
          img {
            width: 20px;
            height: 20px;
            &.pw {
              width: 20px;
              height: 20px;
            }
          }
        }
        .label {
          color: #3e3e3e;
        }
      }
      input {
        outline: none;
        border: none;
        background-color: transparent;
        border-radius: 0;
        &:-webkit-autofill {
          font-size: 16px;
          &::first-line {
            font-size: 16px;
          }
        }
      }
      .account-input,
      .pw-input {
        flex-shrink: 0;
        font-size: 16px;
        color: #3e3e3e;
        margin-left: 10px;
        width: 250px;
        height: 35px;
        line-height: 35px;
        // -webkit-text-fill-color: #fff;
        &::placeholder {
          color: #c2c2c2;
        }
      }

      .experience {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        color: #3e3e3e;
        margin-top: 35px;
        padding-left: 3px;
        height: 18px;
        .remember-checkbox {
          display: flex;
          align-items: center;
          color: #3e3e3e;
          input {
            cursor: pointer;
            position: relative;
            margin-right: 5px;
            width: 12px;
            height: 12px;
            &::before {
              background-color: #fff;
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 12px;
              height: 12px;
              background-image: url('~@/assets/img/login/carebodagps/pc-checkbox.png');
              background-size: 12px 12px;
            }
            &:checked::before {
              background-image: url('~@/assets/img/login/carebodagps/pc-checkbox-active.png');
              background-size: 12px 12px;
            }
          }
        }
        a {
          font-size: 16px;
          font-weight: 400;
          color: #3e3e3e;
        }
      }
      .loginEvent {
        color: white;
        font-size: 20px;
        text-align: center;
        cursor: pointer;
        border: 0;
        margin-top: 26px;
        margin-bottom: 10px;
        width: 357px;
        height: 44px;
        border-radius: 30px;
        background: rgba(#1263f7, 1);
        box-shadow: 0 2px 5px 0 rgba(135, 177, 59, 0.26);
        &:hover {
          background: rgba(#1263f7, 0.8);
        }
      }
      .demoBtn {
        font-size: 14px;
        color: #3e3e3e;
      }
    }
  }
  .loginfooter {
    height: 66px;
    background-color: #fff;
    padding-top: 38px;
    .footer-logo {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .footer-qrcode {
      width: 1180px;
      margin: 0 auto 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      .icon {
        width: 101px;
        height: 26px;
        position: absolute;
      }
      .qrcode_img {
        width: 106px;
        height: 106px;
        position: absolute;
        top: -111px;
        left: -3px;
        visibility: hidden;
      }
      .android {
        width: 101px;
        height: 26px;
        margin-right: 50px;
        position: relative;
        cursor: pointer;
        .active {
          visibility: hidden;
        }
        &:hover {
          .active {
            visibility: visible;
          }
          .noactive {
            visibility: hidden;
          }
          .qrcode_img {
            visibility: visible;
          }
        }
      }
      .ios {
        width: 101px;
        height: 26px;
        position: relative;
        cursor: pointer;
        .active {
          visibility: hidden;
        }
        &:hover {
          .active {
            visibility: visible;
          }
          .noactive {
            visibility: hidden;
          }
          .qrcode_img {
            visibility: visible;
          }
        }
      }
    }

    .footer-link {
      width: 1180px;
      margin: 0 auto;
      .copyright {
        color: #646464;
        font-size: 15px;
        text-align: center;
      }
      .divider {
        margin: 0 8px;
      }
    }
  }
}
.form-warp__title {
  font-size: 18px;
  font-weight: 400;
  color: #020202;
  margin-bottom: 20px;
}
</style>
