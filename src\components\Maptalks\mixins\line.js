import * as maptalks from 'maptalks'

import { loadScript, IcondirationToText, DateToFormatString, parseSeconds, TimeUTCToLocal, toTimeChange } from '@/utils/common.js'
import { loadAddress } from '@/utils/address.js'
import { add, subtract, multiply, divide } from '@/utils/float.js'
import { MeterToKmMi } from '@/utils/convert.js'

var $
const LineMixin = {
  data() {
    return {
      //线路管理---start
      lineManageDialogVisible: false, //线路管理弹窗
      lineVecLayer: null, //线路图层
      // 线路起始 终点图片Maker
      startAndEndMarker: {
        startMaker: null,
        endMaker: null
      },
      //线路管理----end
      // 轨迹回放-----------------------------------------
      currentCar: {}, //轨迹回放选中的车辆信息
      historyPlayMarker: null, //轨迹回放车辆marker
      currentIndex: 0, // 当前所在帧
      allMileage: 0, //回放总里程
      currentDir: '',
      historyData: [], // 存储所有轨迹数据
      historyMarkers: [], // 轨迹产生的标记
      playIntervalValue: 4, //回放速度间隔
      markerPlayPause: false,
      slidePlayMarkerTimer: null,
      replayImgs: [
        '/images/monitor/speed.png', // 速度
        '/images/monitor/date.png', // 日期
        '/images/monitor/mileage.png', // 里程
        '/images/monitor/pass.png', // 停留
        '/images/monitor/jingweidu.png' // 经纬度
      ]
    }
  },
  methods: {
    //线路管理---------------------------------start
    switchLineManageDialog() {
      this.lineManageDialogVisible = !this.lineManageDialogVisible
      if (this.lineManageDialogVisible) {
        this.fenceDialogVisible = false
        this.fenceDialogActive = false
        this.cleanAllGeo()
        this.PoiDialogVisible = false
        this.PoiDialogActive = false
        this.removeAllPOIPoints()
      } else {
        this.clearLineVecLayer()
      }
    },
    closeLineManageDialog() {
      this.lineManageDialogVisible = false
      this.clearLineVecLayer()
    },
    drawTrackLines(lines) {
      if (!$) {
        loadScript('/mapjs/jquery.min.js', () => {
          $ = window.$
        })
      }
      if (!this.lineVecLayer) {
        this.lineVecLayer = new maptalks.VectorLayer('lineVector').addTo(this.swdMapTalk)
      }
      lines.forEach(item => {
        this.drawTrackLine(item)
      })
    },
    // 绘制线路管理下的 轨迹
    drawTrackLine(data) {
      if (!this.lineVecLayer) {
        this.lineVecLayer = new maptalks.VectorLayer('lineVector').addTo(this.swdMapTalk)
      }
      let points = []
      data.pointsC.split(';').forEach(item1 => {
        points.push({ x: item1.split(',')[0], y: item1.split(',')[1] })
      })
      // 有起始点，则先绘制起始点
      if (points.length > 0) {
        let first = points[0] //第一个点
        let icon = ''
        let label = this.setCustomWindow(data.startAddress)
        this.lang === 'cn' ? (icon = require('@/assets/img/map/startAddress_cn.png')) : (icon = require('@/assets/img/map/startAddress_en.png'))
        let opt = {
          lng: first.x, // 位置
          lat: first.y, // 位置
          icon: icon,
          markerWidth: 26,
          markerHeight: 33,
          label: label,
          id: first.x + first.y.toString()
        }
        let infoWindow = {
          title: '',
          content: label,
          single: false, //是否只显示一个信息框
          autoPan: false,
          custom: true,
          dx: 0,
          dy: -8
        }
        this.startAndEndMarker.startMaker = this.newStopMarker(opt, 'lineVecLayer')
      }
      // 有超过1个点
      if (points.length > 1) {
        // 生成结束点
        let last = points[points.length - 1]
        let icon = ''
        let label = this.setCustomWindow(data.endAddress)
        this.lang === 'cn' ? (icon = require('@/assets/img/map/endAddress_cn.png')) : (icon = require('@/assets/img/map/endAddress_en.png'))
        let opt = {
          lng: last.x, // 位置
          lat: last.y, // 位置
          icon: icon,
          markerWidth: 26,
          markerHeight: 33,
          label: label,
          id: last.x + last.y.toString()
        }
        let infoWindow = {
          title: '',
          content: label,
          single: false, //是否只显示一个信息框
          autoPan: false,
          custom: true,
          dx: 0,
          dy: -8
        }
        this.startAndEndMarker.endMaker = this.newStopMarker(opt, 'lineVecLayer')
        this.swdMapTalk.setCenterAndZoom([parseFloat(points[0].x), parseFloat(points[0].y)], 15)
        new maptalks.LineString(points, {
          symbol: {
            lineWidth: data.lineWidth,
            lineJoin: 'round', //miter, round, bevel
            lineCap: 'round', //butt, round, square
            lineDasharray: null, //dasharray, e.g. [10, 5, 5]
            'lineOpacity ': 1,
            lineColor: data.lineColor
          }
        }).addTo(this.lineVecLayer)
      }
      const self = this
      this.startAndEndMarker.startMaker.on('click', function() {
        this.timer = setTimeout(() => {
          if ($ && $('#customWindowModal .close').length) {
            $('#customWindowModal .close').click(function() {
              self.startAndEndMarker.startMaker.closeInfoWindow()
            })
          }
          clearTimeout(self.timer)
        }, 500)
      })
      this.startAndEndMarker.endMaker.on('click', function() {
        this.timer = setTimeout(() => {
          if ($ && $('#customWindowModal .close').length) {
            $('#customWindowModal .close').click(function() {
              self.startAndEndMarker.endMaker.closeInfoWindow()
            })
          }
          clearTimeout(self.timer)
        }, 500)
      })
    },
    clearLineVecLayer() {
      // 清空路线层
      this.swdMapTalk.removeLayer(this.lineVecLayer)
      this.lineVecLayer = null
    },
    //线路管理-------------------------------------end
    //轨迹回放-------------------------------------start
    /* 绘制轨迹
    greenSpeed: 安全速度
    redSpeed：  超速速度
     */
    drawHistoryLine(car, historys, setStopTime, isShowPointIcon, greenSpeed = 100, redSpeed = 120) {
      const self = this
      // 如果已经有轨迹，先清除
      if (self.historyMarkers.length > 0) {
        self.cleanHistoryLine()
      }
      if (!self.vecLayer) {
        self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
      }
      // 保存下轨迹数据先
      if (historys.length == 0) return
      // 谷歌地图需要判断设备是在国内还是在国外
      /* eslint-disable */
      // let isInChina = ptInPolygon(historys[0].lonc, historys[0].latc) //坐标点是否在中国的判断算法
      /* eslint-enable */
      // 判断坐标点是否在中国,不在中国则采用原始的国际标准坐标点
      /* if (isInChina != 'cn') {
        for (let i = 0; i < historys.length; i++) {
          historys[i].lonc = historys[i].lon
          historys[i].latc = historys[i].lat
        }
      } */
      let pArr = historys
      self.historyData = pArr // 保存轨迹
      // 有起始点，则先绘制起始点
      if (pArr.length > 0) {
        let first = pArr[0] //第一个点
        let opt = {
          lng: first.lonc, // 位置
          lat: first.latc, // 位置
          //icon: self.lang == 'cn' ? '/images/map/history/startpos.png' : '/images/map/history/startpos_en.png' // 图标
          icon: self.lang == 'cn' ? '/images/map/history/p_start_cn.png' : '/images/map/history/p_start_en.png',
          markerWidth: 26,
          markerHeight: 33,
          zIndex: 99
        }
        // if (!isDev) {
        //   opt.icon = `${packagePath}` + opt.icon
        // }
        let firstMarker = self.newStopMarker(opt)
        self.historyMarkers.push(firstMarker)
      }
      // 有超过1个点
      if (pArr.length > 1) {
        // 生成结束点
        let last = pArr[pArr.length - 1]
        let opt = {
          lng: last.lonc, // 位置
          lat: last.latc, // 位置
          //icon: self.lang == 'cn' ? '/images/map/history/endpos.png' : '/images/map/history/endpos_en.png' // 图标
          icon: self.lang == 'cn' ? '/images/map/history/p_end_cn.png' : '/images/map/history/p_end_en.png',
          markerWidth: 26,
          markerHeight: 33,
          zIndex: 99
        }
        // if (!isDev) {
        //   opt.icon = `${packagePath}` + opt.icon
        // }
        let lastMarker = self.newStopMarker(opt)
        self.historyMarkers.push(lastMarker)
        // 遍历所有轨迹，生成需要的标签
        let lngLats = []
        let color = null
        // 绘制点
        for (let i = 0; i < pArr.length; i++) {
          let item = pArr[i]
          lngLats.push(self.newLngLat(item.lonc, item.latc)) // 添加到数组构成线
          let col = self.getColorBySpeed(item.speed, greenSpeed, redSpeed) // 根据速度取出当前颜色
          if (color) {
            if (col !== color) {
              // 如果颜色变化
              let polyline = self.newLine(lngLats, color)
              polyline.addTo(self.vecLayer)
              self.historyMarkers.push(polyline)
              lngLats = [] // 清空轨迹点
              lngLats.push(self.newLngLat(item.lonc, item.latc)) // 添加当前点到数组
              color = col // 更新当前颜色
            }
          } else {
            // 如果没有初始颜色，则取个起点颜色
            color = col
          }
          if (isShowPointIcon) {
            //  三年待机无线设备有此功能
            let dir = IcondirationToText(item.dir)
            let pointOpt = {
              lng: item.lonc,
              lat: item.latc,
              icon: '/images/map/history/dir/' + dir + '.png' // 图标
            }
            // if (!isDev) {
            //   opt.icon = `${packagePath}` + opt.icon
            // }
            let pointDir = self.newStopMarker(pointOpt)
            self.historyMarkers.push(pointDir)
          }
        }

        // 绘制轨迹
        if (lngLats.length >= 2) {
          //画线
          let polyline = self.newLine(lngLats, color)
          polyline.addTo(self.vecLayer)
          self.historyMarkers.push(polyline)
        }
        for (let i = 0; i < pArr.length; i++) {
          let item = pArr[i]
          // 后端更正 realStopTime 才是停留时间
          // if (item.isStop && item.stopTime / 60 >= setStopTime) {
          // 后端更正 realStopTime 才是停留时间
          if (item.isStop && item.realStopTime / 60 >= setStopTime) {
            // 判断停车点停车时间大于或等于停留标识的值才显示
            // 画停车点
            let opt = {
              lng: item.lonc, // 位置
              lat: item.latc, // 位置
              icon: '/images/map/history/stopicon.png', // 图标
              markerWidth: 26,
              markerHeight: 33
            }
            // if (!isDev) {
            //   opt.icon = `${packagePath}` + opt.icon
            // }
            let stopMarker = self.newStopMarker(opt)
            //IE兼容模式 把 (xx-xx-xx xx:xx:xx)  格式改为 (xx/xx/xx/ xx:xx:xx) 所以匹配replace(/-/g,  "/") 把-替换为/
            let stamp = Date.parse(new Date(item.pointDt.replace(/-/g, '/'))) / 1000 - (new Date().getTimezoneOffset() / 60) * 60 * 60
            item.pointDt = DateToFormatString(stamp * 1000) //13位时间戳转为标准时间格式
            // 12月28日-停留点的开始时间改为 上一个item的pointDt,结束时间改为下一个item的ponitDt,
            // 第一个item不可能为停留点 ，最后一个item若为停留点，结束时间为该item的pointDt
            let preItem = null
            let nextItem = null
            if (i !== pArr.length - 1) {
              preItem = JSON.parse(JSON.stringify(pArr[i - 1]))
              nextItem = JSON.parse(JSON.stringify(pArr[i + 1]))
            } else {
              preItem = JSON.parse(JSON.stringify(pArr[i - 1]))
              nextItem = JSON.parse(JSON.stringify(item))
            }
            let preStamp = Date.parse(new Date(preItem.pointDt.replace(/-/g, '/'))) / 1000 - (new Date().getTimezoneOffset() / 60) * 60 * 60
            preItem.pointDt = DateToFormatString(preStamp * 1000)
            if (i !== pArr.length - 1) {
              let nextStamp = Date.parse(new Date(nextItem.pointDt.replace(/-/g, '/'))) / 1000 - (new Date().getTimezoneOffset() / 60) * 60 * 60
              nextItem.pointDt = DateToFormatString(nextStamp * 1000)
            }
            self.showStopInfoBox(stopMarker, item, car, preItem, nextItem)
            // 12月28日-停留点更改
            self.historyMarkers.push(stopMarker)
          }
        }
      }
    },
    // 历史回放-停车信息窗 car 主要是备用解析地址的时候用到carId，目前无用
    showStopInfoBox(marker, data, car, preItem, nextItem) {
      const self = this
      // 后端更正 realStopTime 才是停留时间
      let startTime = Date.parse(new Date(data.pointDt.replace(/-/g, '/'))) / 1000 - data.realStopTime
      let _stopTime = parseSeconds(data.realStopTime)
      let _startTime = new Date(startTime * 1000).Format('yyyy-MM-dd hh:mm:ss')
      // 12月26日-停留时间规则更改
      let html = `<div>
          <p>${self.$t('XbOlArDdJLP3YlKnbKEQ0')}：${_stopTime} </p>
          <p>${self.$t('Zbkw-Z-GkLdk92q2GqQb9')}：${preItem.pointDt} </p>
          <p>${self.$t('tjh3v6c9w4iWKRhEsxCel')}：${nextItem.pointDt} </p>
          <p>${self.$t('lg.address')}：<span>stopAddress</span> </p>
        </div>`
      let infoWindow = {
        title: '',
        content: '<div class="stopMarkerTip">' + html + '</div>',
        width: 300,
        minHeight: 50,
        single: false, //是否只显示一个信息框
        autoPan: false,
        custom: false, //自定义信息框
        animation: 'fade'
      }
      marker.setInfoWindow(infoWindow)
      // self.newInfoWindow(marker, infoWindow)
      marker.on('click', function() {
        let infoWindow = marker.getInfoWindow()
        if (infoWindow.getContent().indexOf('stopAddress') == -1) {
          marker.openInfoWindow()
          return
        }
        loadAddress({ lon: data.lon, lat: data.lat, lonC: data.lonc, latC: data.latc, businessType: 31, carId: car.carId }).then(address => {
          let html = infoWindow.getContent().replace(/stopAddress/, address)
          infoWindow.setContent(html)
          marker.openInfoWindow()
        })
      })
    },
    moveTo1(car, index, intervalValue) {
      this.markerPlayPause = false
      let historys = this.historyData
      if (!historys[index + 1]) {
        return
      }
      let carType
      let icon = ''
      let changeDir = false
      if (car.carType) {
        carType = car.carType
      } else {
        carType = 3 //默认小汽车类型
      }
      if ([1, 2, 16, 22, 31, 32, 33, 34, 35].includes(Number(carType))) {
        //无方向类型图标
        icon = `/images/map/machine/${carType}/green.png`
      } else {
        //有方向类型图标
        changeDir = true
        icon = `/images/map/machine/${carType}/green_0.png`
      }
      // if (!isDev) {
      //   icon = `${packagePath}` + icon
      // }
      let opt = {
        lng: historys[index].lonc,
        lat: historys[index].latc, // 位置
        icon: icon, // 获取图标, // 图标
        // icon: icon // 获取图标, // 图标
        //h5页面轨迹跟踪不需要一直在汽车图标头顶顶一个信息窗口，如果有需要可以把这个方法反注释回来
        label: '' + this.getSimpleCarTip(historys[index], index, car) + ''
      }
      // 如果还没创建播放点，则创建
      if (this.historyPlayMarker == null) {
        this.historyPlayMarker = this.newDirMarker(opt)
        let symbol = this.historyPlayMarker.getSymbol()
        symbol.markerDx = 0
        symbol.markerDy = 10
        this.historyPlayMarker.setSymbol(symbol)
        this.historyPlayMarker.openInfoWindow()
        // self.currentIndex = index // 存储当前所在帧
      }
      // if (index === 0) {
      //   this.historyPlayMarker.setCoordinates([historys[index].lonc, historys[index].latc])
      // }
      this.currentCar = car
      this.currentIndex = index
      this.playIntervalValue = intervalValue
      this.markerAnimate(changeDir)
    },
    markerAnimate(changeDir) {
      this.currentIndex++
      this.$emit('updatePlayIndex', this.currentIndex)
      let historys = this.historyData
      let current = historys[this.currentIndex]
      let next = historys[this.currentIndex + 1]
      if (!next) {
        this.allMileage = historys[this.currentIndex].mileage
        return
      }
      // 调整窗口位置
      this.lockWindowByLngLat(current.lonc, current.latc)
      let angle = this.getAngle(current.lonc, current.latc, next.lonc, next.latc)
      let dx = subtract(next.lonc, current.lonc)
      let dy = subtract(next.latc, current.latc)
      this.historyPlayMarker.setCoordinates([current.lonc, current.latc])

      let infoWindow = this.historyPlayMarker.getInfoWindow()
      let label = '' + this.getSimpleCarTip(current, this.currentIndex, this.currentCar) + ''
      if (infoWindow) {
        infoWindow.setContent(label)
      }
      if (changeDir) {
        this.historyPlayMarker.updateSymbol({
          markerRotation: 360 - angle.toFixed(4) // marker rotation in degree, clock-wise
        })
      }

      const intervalMaps = {
        1: { stepCount: 170, duration: 3500 },
        2: { stepCount: 140, duration: 3000 },
        3: { stepCount: 70, duration: 1400 },
        4: { stepCount: 45, duration: 1100 },
        5: { stepCount: 20, duration: 480 },
        6: { stepCount: 6, duration: 120 },
        7: { stepCount: 3, duration: 50 },
        8: { stepCount: 1, duration: 10 }
      }
      let duration = intervalMaps[this.playIntervalValue]?.duration || 480

      this.historyPlayMarker.animate(
        {
          translate: [dx, dy]
        },
        {
          duration: duration,
          easing: 'linear',
          //let map focus on the marker
          focus: false
        },
        frame => {
          // console.log(frame.state.playState)
          if (frame.state.playState === 'finished' && !this.markerPlayPause && this.currentIndex < this.historyData.length - 1) {
            this.markerAnimate(changeDir)
          }
        }
      )
    },
    slidePlayMarker(car, index, intervalValue, autoPlay) {
      this.markerPlayPause = true //暂停播放animate
      if (this.historyPlayMarker) {
        this.historyPlayMarker.remove()
      }
      let historys = this.historyData
      let carType
      let icon = ''
      let changeDir = false
      if (car.carType) {
        carType = car.carType
      } else {
        carType = 3 //默认小汽车类型
      }
      if ([1, 2, 16, 22].includes(Number(carType))) {
        //无方向类型图标
        icon = `/images/map/machine/${carType}/green.png`
      } else {
        //有方向类型图标
        changeDir = true
        icon = `/images/map/machine/${carType}/green_0.png`
      }
      // if (!isDev) {
      //   icon = `${packagePath}` + icon
      // }
      let opt = {
        lng: historys[index].lonc,
        lat: historys[index].latc, // 位置
        icon: icon, // 获取图标, // 图标
        // icon: icon // 获取图标, // 图标
        //h5页面轨迹跟踪不需要一直在汽车图标头顶顶一个信息窗口，如果有需要可以把这个方法反注释回来
        label: '' + this.getSimpleCarTip(historys[index], index, car) + ''
      }
      this.historyPlayMarker = new maptalks.Marker([opt.lng, opt.lat], {
        visible: true,
        editable: true,
        cursor: 'pointer',
        shadowBlur: 0,
        shadowColor: 'black',
        draggable: false,
        dragShadow: false, // display a shadow during dragging
        drawOnAxis: null, // force dragging stick on a axis, can be: x, y
        zIndex: 100,
        symbol: {
          markerFile: opt.icon,
          markerDx: 0,
          markerDy: 10
        }
      }).addTo(this.vecLayer)

      if (changeDir) {
        let angle
        if (index < historys.length - 1) {
          //非最后的点，方向由当前点和下一个点构成
          angle = this.getAngle(historys[index].lonc, historys[index].latc, historys[index + 1].lonc, historys[index + 1].latc)
        } else {
          //滑到最后一个点了，方向由前面的点和当前点构成
          angle = this.getAngle(historys[index - 1].lonc, historys[index - 1].latc, historys[index].lonc, historys[index].latc)
        }
        let symbol = this.historyPlayMarker.getSymbol()
        symbol.markerRotation = 360 - angle.toFixed(4) // marker rotation in degree, clock-wise
        this.historyPlayMarker.setSymbol(symbol)
      }
      let infoWindow = {
        title: '',
        content: opt.label,
        // 'width': 300,
        // 'minHeight': 50,
        single: true, //是否只显示一个信息框
        autoPan: false,
        custom: false, //自定义信息框
        dx: 0,
        dy: -10
      }
      this.historyPlayMarker.setInfoWindow(infoWindow)
      this.historyPlayMarker.openInfoWindow()
      // 调整窗口位置
      this.lockWindowByLngLat(historys[index].lonc, historys[index].latc)

      if (this.slidePlayMarkerTimer) {
        clearTimeout(this.slidePlayMarkerTimer)
      }
      if (autoPlay && historys[index + 1]) {
        //拖拽后自动播放？
        this.slidePlayMarkerTimer = setTimeout(() => {
          this.moveTo1(car, index, intervalValue)
        }, 300)
      }
    },
    getSimpleCarTip(pos, index, car) {
      // console.log('pos---', pos)
      const self = this
      let historys = self.historyData
      let isWire = car.wiretype

      let html = ''
      let showTime = 'hide'
      let stopTime = ''
      // 后端更正 realStopTime 才是停留时间
      // if (pos.stopTime) {
      //   showTime = ''
      //   stopTime = parseSeconds(pos.stopTime)
      // }
      if (pos.realStopTime) {
        showTime = ''
        stopTime = parseSeconds(pos.realStopTime)
      }
      let time = ''
      if (pos.isStop) {
        // 当是停留点时时间会默认变为当地时区时间，就不需转为当地时间了
        time = toTimeChange(pos.pointDt, true)
      } else {
        time = TimeUTCToLocal(pos.pointDt)
      }

      // 基站定位，不列入统计
      // if (pos.pointType !== 3) {
      //   if (index > self.currentIndex) {
      //     self.allMileage = historys[index].mileage
      //   } else if (index < self.currentIndex) {
      //     self.allMileage = historys[index].mileage
      //   }
      // }
      this.allMileage = historys[index].mileage
      let imgArr = self.replayImgs
      // if (!isDev) {
      //   imgArr = self.replayImgs.map(item => {
      //     item = `${packagePath}` + item
      //     return item
      //   })
      // }
      if (self.lang === 'cn' || self.lang === 'tw') {
        html = `
        <div class="simpleCarTip">
          <div class="carname">${car.machineName}</div>
          <div class="row">
            <div class="col-45">${self.$t('dlW7RH6fkSItw_Mtz2Ykw') + ' '}${MeterToKmMi(pos.speed * 1000)}${self.miledgeUnitEN}/h</div>
            <div class="col-55">${self.$t('lg.time') + ' '}${time}</div>
          </div>
          <div class="row">
            <div class="col-45">${self.$t('D-3CnkxFwEGtcITqH7fbB') + ' '}${MeterToKmMi(parseFloat(self.allMileage)) + self.miledgeUnitCN}</div>
            <div class="col-55 col-4">${self.$t('asduiijoh_jkakjsdui15') + ' '}${pos.latc + ',' + pos.lonc}</div>
          </div>
        </div>
      `
      } else {
        html = `
          <div class="simpleCarTip">
            <div class="carname">${car.machineName}</div>
            <div class="row">
              <div class="col"><img class="img1" src="${imgArr[0]}"/>${MeterToKmMi(pos.speed * 1000)}${self.miledgeUnitEN}/h</div>
              <div class="col"><img class="img2" src="${imgArr[1]}"/>${time}</div>
            </div>
            <div class="row">
              <div class="col"><img class="img3" src="${imgArr[2]}"/>${
          pos.pointType === 3 ? self.$t('HGjTtiFHYcRbFl8yOYVox') : MeterToKmMi(parseFloat(self.allMileage)) + self.miledgeUnitCN
        }</div>
              <div class="col"><img class="img4" src="${imgArr[4]}"/>${pos.latc + ',' + pos.lonc}</div>
            </div>
          </div>
        `
      }
      return html
    },
    // 创建方向marker
    newDirMarker(opt) {
      let marker = new maptalks.Marker([opt.lng, opt.lat], {
        visible: true,
        editable: true,
        cursor: 'pointer',
        shadowBlur: 0,
        shadowColor: 'black',
        draggable: false,
        dragShadow: false, // display a shadow during dragging
        drawOnAxis: null, // force dragging stick on a axis, can be: x, y
        zIndex: 100,
        symbol: {
          markerFile: opt.icon
          // 'textFaceName' : 'sans-serif',
          // 'textName' : 'MapTalks',
          // 'textFill' : '#34495e',
          // 'textHorizontalAlignment' : 'right',
          // 'textSize' : 40
          // 'markerDx': 0,
          // 'markerDy': 10
        }
      }).addTo(this.vecLayer)
      marker.on('click', async function() {
        console.log('方向')
      })
      if (opt.label) {
        let infoWindow = {
          title: '',
          content: opt.label,
          // 'width': 300,
          // 'minHeight': 50,
          single: false, //是否只显示一个信息框
          autoPan: false,
          custom: false, //自定义信息框
          dx: 0,
          dy: -10
        }
        marker.setInfoWindow(infoWindow)
      }
      return marker
    },
    // 获取角度
    getAngle(x1, y1, x2, y2) {
      let dx = x2 - x1
      let dy = y2 - y1
      let rad = Math.atan(Math.abs(dx / dy))
      let angle = (rad * 180.0) / Math.PI
      if (dx > 0 && dy <= 0)
        // 二象限
        return 180 - angle
      else if (dx <= 0 && dy < 0)
        // 三象限
        return 180 + angle
      else if (dx < 0 && dy >= 0)
        // 四象限
        return 360 - angle
      else return angle
    },
    // 绘制线
    newLine(lineArr, color) {
      let fileName = null
      color === '#3ff419' ? (fileName = 'green') : (fileName = color)
      let lineUrl = '/images/map/history/' + fileName + '_road.png'
      // if (!isDev) {
      //   lineUrl = `${packagePath}/images/map/history/` + fileName + '_road.png'
      // }
      let line = new maptalks.LineString(lineArr, {
        symbol: {
          linePatternFile: lineUrl,
          lineWidth: 9,
          lineJoin: 'round', //miter, round, bevel
          lineCap: 'round', //butt, round, square
          lineDasharray: null, //dasharray, e.g. [10, 5, 5]
          'lineOpacity ': 1
        }
      })
      return line
    },
    // 清空轨迹图层
    cleanHistoryLine() {
      let overlays = this.historyMarkers
      for (let i = 0; i < overlays.length; i++) {
        this.removeMarker(overlays[i])
      }
      // 清除历史轨迹
      this.historyMarkers = []
      this.historyData = []
      // 如果创建了播放标记
      if (this.historyPlayMarker) {
        this.removeMarker(this.historyPlayMarker)
        this.historyPlayMarker = null
      }
    },
    pauseRemoveMarker() {
      this.markerPlayPause = true
    },
    // 移除marker
    removeMarker(point) {
      point.remove()
    },
    // 根据经度，纬度创建位置点
    newLngLat(lng, lat) {
      return {
        x: lng,
        y: lat
      }
    }
  }
}

export default LineMixin
