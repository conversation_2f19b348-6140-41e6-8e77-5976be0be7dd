<template>
  <div class="container">
    <el-form :inline="true">
      <el-form-item :label="$t('lg.targetUser')">
        <search-user ref="userTree" :placeholder="$t('lg.targetUser')" :isCur="true" :size="'small'" @autoSelectUser="getSelectOperateUser"></search-user>
      </el-form-item>
      <el-form-item :label="$t('QBfoK7OejhnKA31m5cUb_')">
        <el-input :placeholder="$t('dBc3sWmATHCVdJB5RtzRA')" clearable v-model="searchParams.targetDevice"></el-input>
      </el-form-item>
      <el-form-item :label="$t('lg.time')">
        <base-date-picker
          class="data-picker-wdth"
          v-model="pickDateArr"
          type="datetimerange"
          range-separator="~"
          :start-placeholder="$t('XbFegIO6XdtwVCLj3tHjn')"
          :end-placeholder="$t('k3rb7GIYeArL-6QUB2jYR')"
          :default-time="['00:00:00', '23:59:59']"
          :format="systemDateFormat"
          prefix-icon="el-icon-date"
          @change="handleDateChange"
          :picker-options="pickerOptions"
        >
        </base-date-picker>
      </el-form-item>
      <el-form-item>
        <base-button type="primary" @click="queryDataList">{{ $t('lg.query') }}</base-button>
        <base-button color="#606266FF" type="default" icon="reset" class="point-table-button__reset" @click="resetParams">{{ $t('lg.reset') }}</base-button>
      </el-form-item>
    </el-form>
    <el-table
      fit
      empty-text="-"
      ref="deviceTable"
      v-loading="carloading"
      :data="tableData.data"
      class="swd-table"
      :height="tableHeight"
      @filter-change="filterChange"
    >
      <el-table-column show-overflow-tooltip :label="$t('lg.serial')" width="70" class-name="serial" align="left">
        <template slot-scope="scope">
          {{ (searchParams.pageNO - 1) * searchParams.rowCount + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        :label="$t('lg.cardtype')"
        :title="$t('lg.cardtype')"
        prop="sharpturnCount"
        :width="showLeftBox ? 124 : 152"
        :filter-multiple="false"
        column-key="cardType"
        :filters="cardTypeOptions"
      >
        <template slot="header">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div>{{ $t('lg.cardtype') }}</div>
            </div>
            <div class="align-items-center" :title="$t('lg.cardtype')">
              <span :style="{ width: showLeftBox && lang !== 'cn' && lang !== 'tw' ? '90px' : 'auto' }" class="text-overflow-ellipsis"
                >{{ $t('lg.cardtype') }}
              </span>
              <el-link :underline="false" :class="{ 'absolute-right-11': showLeftBox && lang !== 'cn' && lang !== 'tw' }">
                <div class="svg-div">
                  <svg-icon icon-class="down_full" className="device_status_filter"></svg-icon>
                </div>
              </el-link>
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            {{ scope.row.cardType | cardType }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        :width="showLeftBox ? 124 : 152"
        show-overflow-tooltip
        :label="$t('lg.operateType')"
        :filter-multiple="false"
        column-key="operaType"
        prop="operaType"
        :filters="operaTypeOptions"
      >
        <template slot="header">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div>{{ $t('lg.operateType') }}</div>
            </div>
            <div class="align-items-center">
              <span :style="{ width: showLeftBox && lang !== 'cn' && lang !== 'tw' ? '90px' : 'auto' }" class="text-overflow-ellipsis"
                >{{ $t('lg.operateType') }}
              </span>
              <el-link :underline="false" :class="{ 'absolute-right-11': showLeftBox && lang !== 'cn' && lang !== 'tw' }">
                <div class="svg-div">
                  <svg-icon icon-class="down_full" className="device_status_filter"></svg-icon>
                </div>
              </el-link>
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            {{ scope.row.operaType | operaType }}
          </div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip :width="showLeftBox ? 124 : 152" :label="$t('9Z5P7gAyBPa7F9zacoYIW')" prop="operaUserName" min-width="60">
        <template slot="header">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div>{{ $t('9Z5P7gAyBPa7F9zacoYIW') }}</div>
            </div>
            <span>{{ $t('9Z5P7gAyBPa7F9zacoYIW') }} </span>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            {{ scope.row.operaUserName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip :width="showLeftBox ? 124 : 152" :label="$t('Hi_cEuIxsZ92-zskwssT7')" prop="targetUserName">
        <template slot="header">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div>{{ $t('Hi_cEuIxsZ92-zskwssT7') }}</div>
            </div>
            <span class="text-overflow-ellipsis">{{ $t('Hi_cEuIxsZ92-zskwssT7') }} </span>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div class="swd-default-a text-overflow-ellipsis" @click="showTargetUserDetail(scope.row)">
            {{ scope.row.targetUserName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip :label="$t('QBfoK7OejhnKA31m5cUb_')" prop="imei" :width="showLeftBox ? 124 : 152">
        <template slot="header">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div>{{ $t('QBfoK7OejhnKA31m5cUb_') }}</div>
            </div>
            <span class="text-overflow-ellipsis">{{ $t('QBfoK7OejhnKA31m5cUb_') }} </span>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div class="swd-default-a text-overflow-ellipsis" @click="showTargetDeviceDetail(scope.row)">
            {{ scope.row.imei }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :width="showLeftBox ? 124 : 152"
        show-overflow-tooltip
        :label="$t('lg.inandouttype')"
        :filter-multiple="false"
        column-key="type"
        prop="type"
        :filters="typeOptions"
      >
        <template slot="header">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div>{{ $t('lg.inandouttype') }}</div>
            </div>
            <div class="align-items-center">
              <span :style="{ width: showLeftBox && lang !== 'cn' && lang !== 'tw' ? '90px' : 'auto' }" class="text-overflow-ellipsis"
                >{{ $t('lg.inandouttype') }}
              </span>
              <el-link :underline="false" :class="{ 'absolute-right-11': showLeftBox && lang !== 'cn' && lang !== 'tw' }">
                <div class="svg-div">
                  <svg-icon icon-class="down_full" className="device_status_filter"></svg-icon>
                </div>
              </el-link>
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div v-if="scope.row.type === 1">{{ $t('lg.income') }}</div>
          <div v-else-if="scope.row.type === 0">{{ $t('lg.pay') }}</div>
          <div v-else>-</div>
        </template>
      </el-table-column>

      <el-table-column show-overflow-tooltip :label="$t('lg.incomeexpend')" prop="useCount" :width="showLeftBox ? 124 : 152">
        <template slot="header">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div>{{ $t('lg.incomeexpend') }}</div>
            </div>
            <span class="text-overflow-ellipsis">{{ $t('lg.incomeexpend') }} </span>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            {{ scope.row.useCount }}
          </div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip :label="$t('lg.balance')" prop="newCardCount" :width="showLeftBox ? 124 : 152">
        <template slot="header">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div>{{ $t('lg.balance') }}</div>
            </div>
            <span class="text-overflow-ellipsis">{{ $t('lg.balance') }} </span>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            {{ scope.row.newCardCount > -1 ? scope.row.newCardCount : '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip :label="$t('lg.date')" :width="showLeftBox ? 150 : 190">
        <template slot="header">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div>{{ $t('lg.date') }}</div>
            </div>
            <span class="text-overflow-ellipsis">{{ $t('lg.date') }} </span>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            {{ scope.row.operaTime | UTCTimeToLocalTime }}
          </div>
        </template>
      </el-table-column>

      <el-table-column show-overflow-tooltip :label="$t('lg.remark')">
        <template slot-scope="scope">
          <div>{{ scope.row.remark | EmptyIntoLine }}</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-button type="primary" style="float:right;" @click="downloadTableData">
        <svg-icon icon-class="client_download" class="download-icon"></svg-icon>
      </el-button>
      <base-pagination
        :current-page.sync="searchParams.pageNO"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="searchParams.rowCount"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableData.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </base-pagination>
    </div>
    <device-detail v-if="showDeviceDialog" :dialogVisible.sync="showDeviceDetail" :info.sync="deviceInfo" :userId="deviceUserId" />
    <client-detail
      key="tableClient"
      v-if="showUserDialog"
      ref="clientDetailHandle"
      :dialogVisible.sync="showClientDetail"
      :clientInfo="clientInfo"
      command="detail"
      @showUser="showClientUser"
    />
  </div>
</template>

<script>
import '@/filter/type.js'
import pickerOptionMixinx from '@/mixins/pickerOptions.js'
import SearchUser from '@/components/Bussiness/SearchUser'
import { downloadAssetTableData, getUserPointLog } from '@/api/asset.js'
import { querySearchDevice } from '@/api/device.js'
import { getUser } from '@/api/user'
import { blobDownload, DateFromLocalToUTC } from '@/utils/common.js'
import DeviceDetail from '@/components/Device/DeviceDetail' // 设备高级搜索
import ClientDetail from '@/components/Bussiness/ClientDetailDialog'
import { mapState } from 'vuex'

export default {
  name: 'Table',
  components: { SearchUser, DeviceDetail, ClientDetail },
  mixins: [pickerOptionMixinx],
  props: {
    showEcharts: {
      type: Boolean,
      default: true
    },
    showLeftBox: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showDeviceDialog: false,
      showUserDialog: false,
      deviceUserId: null,
      clientInfo: null,
      deviceInfo: null,
      showClientDetail: false,
      showDeviceDetail: false,
      carloading: false,
      pickDateArr: [new Date(new Date().Format('yyyy-MM-01 00:00:00')), new Date()],
      searchParams: {
        targetUserId: '',
        targetDevice: '',
        cardType: null,
        operaType: null,
        // 收支类型
        type: null,
        pageNO: 1,
        rowCount: 15,
        userId: this.$cookies.get('userId'),
        startTime: DateFromLocalToUTC(new Date(new Date().getFullYear(), new Date().getMonth() - 11)),
        endTime: DateFromLocalToUTC(new Date())
      },
      cardTypeOptions: [
        {
          text: this.$t('lg.commonImportPoint'),
          value: 1
        },
        {
          text: this.$t('lg.lifetimeImportPoint'),
          value: 2
        },
        {
          text: this.$t('lg.yearCard'),
          value: 3
        },
        {
          text: this.$t('lg.lifetimeOfCard'),
          value: 4
        }
      ],
      operaTypeOptions: [
        {
          value: 1,
          text: this.$t('lg.newGeneration')
        },
        {
          value: 2,
          text: this.$t('lg.consume')
        },
        {
          value: 3,
          text: this.$t('lg.transfer')
        },
        {
          value: 4,
          text: this.$t('elr13plxYiqSiRBQuS4As')
        }
      ],
      typeOptions: [
        {
          value: 0,
          text: this.$t('lg.pay')
        },
        {
          value: 1,
          text: this.$t('lg.income')
        }
      ],
      tableData: {
        data: [],
        total: 0
      },
      lang: null
    }
  },
  computed: {
    ...mapState({
      systemDateFormat: state => state.user.systemDateFormat
    }),
    tableHeight() {
      return 'calc(100% - 91px)'
    }
  },
  methods: {
    // 设置点卡类型并筛选查询
    setCardTypeAndFilter(filterObject) {
      let columns = this.$refs.deviceTable.columns
      columns.forEach(item => {
        if (item.columnKey === 'cardType') {
          item.filteredValue[0] = filterObject.cardType[0] // 表格筛选选择
        }
      })

      this.filterChange(filterObject)
    },
    showClientUser(userId) {
      this.$router.push({
        name: 'MyClient',
        query: {
          userId,
          t: new Date().getTime()
        }
      })
    },
    downloadTableData() {
      let params = JSON.parse(JSON.stringify(this.searchParams))
      params.pageNO = 1
      params.rowCount = 15
      downloadAssetTableData(this.searchParams).then(res => {
        if (res) {
          blobDownload(res)
        }
      })
    },
    async showTargetDeviceDetail(row) {
      this.showDeviceDialog = true
      console.log(row)
      this.deviceUserId = row.targetUserId
      let res = await querySearchDevice({ rowCount: 10, pageNO: 1, condition: row.imei })
      if (res.ret && res.data.length > 0) {
        this.deviceInfo = res.data[0]
      }
      this.showDeviceDetail = true
      console.log(this.deviceInfo)
    },
    async showTargetUserDetail(row) {
      this.showUserDialog = true
      let res = await getUser({ userId: row.targetUserId })
      if (res.ret) {
        this.clientInfo = res.data
      }
      this.showClientDetail = true
    },
    queryDataList() {
      this.getCarDataList()
    },
    resetParams(userId) {
      this.searchParams = {
        targetUserId: null,
        targetDevice: '',
        cardType: null,
        operaType: null,
        // 收支类型
        type: null,
        pageNO: 1,
        rowCount: 15,
        userId: userId ? userId : this.$cookies.get('userId'),
        startTime: DateFromLocalToUTC(new Date(new Date().getFullYear(), new Date().getMonth() - 11)),
        endTime: DateFromLocalToUTC(new Date())
      }
      this.pickDateArr = [new Date(new Date().getFullYear(), new Date().getMonth() - 11), new Date()]
      this.$refs.userTree.inputClear()
      this.$refs.deviceTable.clearFilter()
      this.getCarDataList()
    },
    getCarDataList() {
      this.carloading = true
      getUserPointLog(this.searchParams).then(res => {
        this.carloading = false
        if (res.ret === 1) {
          if (res.data) {
            this.tableData.data = res.data
            this.tableData.total = res.total
          }
        } else {
          this.tableData.data = res.data
          this.tableData.total = res.total
        }
        // this.$nextTick(() => {
        //   this.$refs.deviceTable.doLayout()
        // })
      })
    },
    getSelectOperateUser(user) {
      this.searchParams.targetUserId = user.userId
    },
    // 处理日期选择
    handleDateChange(e) {
      if (!e) {
        this.searchParams.startTime = ''
        this.searchParams.endTime = ''
      } else {
        this.searchParams.startTime = DateFromLocalToUTC(this.pickDateArr[0])
        this.searchParams.endTime = DateFromLocalToUTC(this.pickDateArr[1])
      }
    },
    // 点击页码pageNO
    handleCurrentChange(page) {
      this.searchParams.pageNO = page
      this.getCarDataList()
    },
    // 切换行数
    handleSizeChange(size) {
      this.searchParams.rowCount = size
      this.getCarDataList()
    },
    //  类型改变时
    handleTypeChange(e, type) {
      //  先判断是什么类型
      switch (type) {
        case 'cardType':
          this.searchParams.cardType = e
          break
        case 'operaType':
          this.searchParams.operaType = e
          break
        case 'type':
          this.searchParams.type = e
          break
      }
      this.searchParams.pageNO = 1
      this.getCarDataList()
    },
    filterChange(filters) {
      console.log(filters)
      if (filters.cardType) {
        filters.cardType.length > 0 ? (this.searchParams.cardType = filters.cardType[0]) : (this.searchParams.cardType = null)
      } else if (filters.operaType) {
        filters.operaType.length > 0 ? (this.searchParams.operaType = filters.operaType[0]) : (this.searchParams.operaType = null)
      } else {
        filters.type.length > 0 ? (this.searchParams.type = filters.type[0]) : (this.searchParams.type = null)
      }
      this.searchParams.pageNO = 1
      this.searchParams.rowCount = 15
      this.getCarDataList()
    }
  },
  created() {
    this.lang = this.$cookies.get('lang')
  },
  mounted() {
    if (this.$route.params.cardType) {
      this.setCardTypeAndFilter({ cardType: [this.$route.params.cardType] })
    }
    if (this.$route.params.dateArr) {
      this.pickDateArr = this.$route.params.dateArr
    }
    this.getCarDataList()
  }
}
</script>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  padding: 20px 20px 0 20px;
  margin-top: 16px;
  background-color: #fff;
  border-radius: 6px;
}
.data-picker-wdth {
  width: 210px;
}
.el-form .el-form-item {
  margin-bottom: 10px;
}
.pagination {
  height: 32px;
  padding-top: 10px;
  padding-bottom: 10px;
  text-align: center;
}
::v-deep .el-table__column-filter-trigger {
  margin-left: 6px;
}
.swd-table {
  height: calc(100% - 91px);
  ::v-deep .el-table__header-wrapper {
    .el-table__column-filter-trigger {
      display: none;
    }
    .highlight {
      color: $primary;
      svg {
        cursor: pointer;
        filter: drop-shadow($primary 300px 0px);
        transform: translateX(-300px);
      }
    }
  }
  .svg-div {
    display: flex;
    width: 14px;
    height: 14px;
    overflow: hidden;
    margin-left: 6px;
    &:hover {
      svg {
        filter: drop-shadow($primary 15px 0px);
        transform: translateX(-15px);
      }
    }
  }
}
::v-deep .el-table {
  tr .serial {
    padding-left: 24px;
  }
  tbody {
    .serial {
      padding-left: 14px;
    }
  }
  th:first-child > .cell {
    padding-left: 0px;
  }
}
.align-items-center {
  display: inline-flex;
}
</style>

<style lang="scss">
.point-table-button__reset:hover {
  path:last-child {
    fill: $primary;
  }
}
</style>
