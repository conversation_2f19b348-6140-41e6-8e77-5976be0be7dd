var carIconMaps = null

/**
 * 获取车标marker图片
 * @param {String} status
 * @param {String | Number} carType
 * @returns {Element}
 */
export function getMapCarIcon(status, carType) {
  if (!carIconMaps) {
    carIconMaps = {}
    let folderName = '',
      statusName = ''
    const files = require.context('@/assets/maptalks/car-marker/', true, /\.png$/)
    for (const key of files.keys()) {
      folderName = key.split('/')[1]
      statusName = key.split('/')[2].split('.')[0]
      carIconMaps[statusName + folderName] = files(key)
    }
  }

  return carIconMaps[status + carType]
}
// 车辆图标
export function getIconByDir(dir) {
  // let dir = 0
  // if (!dir && car.carStatus) {
  //   dir = car.carStatus.dir
  // }
  if (!dir || isNaN(dir) || dir < 0 || dir > 360) {
    dir = 0
  } else {
    // dir = Math.round(dir / 90) % 4;
    //角度0-360，0表示正北，顺时针增加
    if (dir == 0 || dir == 360) {
      //正北
      dir = 0
    } else if (dir > 0 && dir <= 30) {
      dir = 15
    } else if (dir > 30 && dir <= 60) {
      //东北
      dir = 45
    } else if (dir > 60 && dir <= 89) {
      dir = 75
    } else if (dir == 90) {
      //正东
      dir = 90
    } else if (dir > 90 && dir <= 120) {
      dir = 105
    } else if (dir > 120 && dir <= 150) {
      //东南
      dir = 135
    } else if (dir > 150 && dir <= 179) {
      dir = 165
    } else if (dir == 180) {
      //正南
      dir = 180
    } else if (dir > 180 && dir <= 210) {
      dir = 195
    } else if (dir > 210 && dir <= 240) {
      //西南
      dir = 225
    } else if (dir > 240 && dir <= 269) {
      dir = 255
    } else if (dir == 270) {
      //正西
      dir = 270
    } else if (dir > 270 && dir <= 300) {
      dir = 285
    } else if (dir > 300 && dir <= 330) {
      //西北
      dir = 315
    } else if (dir > 330 && dir <= 359) {
      dir = 345
    }
  }
  return '/images/map/machine/arrow_circle/arrow_' + dir + '.png'
}
