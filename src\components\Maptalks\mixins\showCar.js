import * as maptalks from 'maptalks'
import { ClusterLayer } from 'maptalks.markercluster'

import { getMileageStaById } from '@/api/monitor.js'
import { _getCarImg, getSubTagInfo } from '@/api/device.js'
import { loadAddress } from '@/utils/address.js'
import SubTagTooltip from '@/components/Maptalks/components/SubTagTooltip.vue'

import { loadScript, parseExData, DiffTimeSampleFormateUtil } from '@/utils/common.js'
import { getNormalOrderList } from '@/api/device.js'
import { timeConvert } from '@/utils/date.js'
import { MeterToKmMi } from '@/utils/convert.js'
import dayjs from 'dayjs'
import { getHtml } from '@/assets/icon/buildIcon'
import { PcOutage } from '@/assets/icon'
import { mapState } from 'vuex'

var $ = null
// 记录当前型号是否支持子标签
const subTagRecord = {}
//车辆显示相关逻辑
const ShowCarMixin = {
  props: {
    isShowLock: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      isVirtualUser: state => state.user.isVirtualUser
    })
  },
  data() {
    return {
      //车辆marker显示逻辑---------------------------start
      carsLayer: null, //车辆marker绘制图层
      carOnMapPoints: [],
      carOnMapMarkers: [], //显示在地图上的车辆markers集合
      nowSelectedCarObj: null, //点击选中展开弹窗的车辆
      updateAddressTimer: null,
      updateAddressCount: 10,
      infoImages: [
        // 注意顺序不能随意改动
        '/images/monitor/elec.png', // 电量
        '/images/monitor/sign.png', // 信号
        '/images/monitor/wireline.png', // 是否有线
        '/images/monitor/location.png', // 定位
        '/images/monitor/mileage.png', // 里程
        '/images/monitor/acc.png', // acc
        '/images/monitor/oil.png', // 油量
        '/images/monitor/temp.png', // 温度
        '/images/monitor/voltage.png', // 电压
        '/images/monitor/addr.png', // 解析地址
        '/images/monitor/humidity.png', // 湿度
        '/images/monitor/oilAndEle.png', // 油电
        '/images/monitor/totalMileage.png', // 总里程
        '/images/monitor/enduranceMileage.png', // 续航里程
        '/images/monitor/gear.png', // 档位
        '/images/monitor/chargingStatus.png', // 充电状态
        '/images/monitor/video.png', // 视频监控
        '/images/monitor/tfCard.png', // TF卡
        '/images/monitor/tf_cartd_tip.png', // TF卡
        '/images/monitor/Charging.png', // 充电中
        '/images/monitor/icon-intercom-small-blue.png', // 对讲
        '/images/monitor/icon-warning-blue.png', // 对讲popover提示图标1
        '/images/monitor/icon-warning-yellow.png', // 对讲popover提示图标2
        '/images/monitor/ac.png', // ac
        '/images/monitor/car_door.png' // 车门
      ],
      carMarkersClustered: false,
      carTrailMap: {},
      carTrailLineMap: {},
      mapViewExtent: null,
      carImageInfo: { url: '', hasLoaded: false }, //车辆图片显示
      carImageUrl: '', //车辆图片
      exhibitionArea: {}
      //车辆marker显示逻辑---------------------------end
    }
  },
  watch: {
    showOnMapCarObj(carObj) {
      const { cars, initView, reload, targetCarId } = carObj
      if (initView || reload) {
        this.carOnMapMarkers.forEach(carObj => {
          carObj.marker.remove()
        })
        this.carOnMapPoints = []
        this.carOnMapMarkers = []
      }
      if (cars.length) {
        let carOnMapPoints = cars.map(car => {
          return { ...car, lonc: car.monitorStatusVo.lonc, latc: car.monitorStatusVo.latc }
        })
        this.carOnMapPoints = carOnMapPoints
        this.showCars()
        if (this.carMarkersClustered && (initView || reload)) {
          this.handlePolymerization(true)
        }
        // const extent = new maptalks.Extent(100, 10, 120, 20)
        if (targetCarId) {
          let car = cars.find(item => item.carId === targetCarId)
          this.openTargetCarInfoWindow(car)
        } else if (initView) {
          let shows = cars.filter(car => {
            return (this.isVirtualUser && car.monitorStatusVo) || (car.monitorStatusVo && car.monitorStatusVo.show === 1)
          })
          if (shows.length > 1) {
            let showCoordinates = cars.map(item => {
              return [item.monitorStatusVo.lonc, item.monitorStatusVo.latc]
            })
            // console.log(showCoordinates)
            // 初始化范围的四个边界值
            var minX = Number.POSITIVE_INFINITY
            var minY = Number.POSITIVE_INFINITY
            var maxX = Number.NEGATIVE_INFINITY
            var maxY = Number.NEGATIVE_INFINITY
            // console.log(coordinates)
            showCoordinates.forEach(point => {
              minX = Math.min(minX, point[0]) // 经度
              minY = Math.min(minY, point[1]) // 纬度
              maxX = Math.max(maxX, point[0]) // 经度
              maxY = Math.max(maxY, point[1]) // 纬度
            })
            this.exhibitionArea = { minX, minY, maxX, maxY }
            this.mapViewExtent = new maptalks.Extent(
              minX,
              minY, // 左下角经纬度坐标
              maxX,
              maxY // 右上角经纬度坐标
            )
            // console.log(this.mapViewExtent)
            this.swdMapTalk.fitExtent(this.mapViewExtent, 0, {
              //这里padding全部无效，升级到maptalks1.0版本就有效，但是会出现滚轮缩放地图动画卡顿问题暂时无法修复
              paddingTop: 200,
              paddingBottom: 200,
              padding: [200, 200, 200, 200]
            })
          } else if (shows.length === 1) {
            let car = shows[0]
            let lonc = car.monitorStatusVo.lonc
            let latc = car.monitorStatusVo.latc
            this.exhibitionArea = { lonc, latc, zoom: 17 }
            this.setCenter(lonc, latc, 17)
          }
        }
      }
    }
  },
  methods: {
    // 显示车标逻辑
    async showCars() {
      if (!this.carOnMapPoints.length) {
        return
      }
      let carOnMapPoints = this.carOnMapPoints
      if (!this.carsLayer) {
        this.carsLayer = new maptalks.VectorLayer('carsLayer').addTo(this.swdMapTalk)
      }
      for (let i = 0; i < carOnMapPoints.length; i++) {
        let car = carOnMapPoints[i]
        let dir = car.monitorStatusVo.dir || 0
        // let _car = JSON.parse(JSON.stringify(self.carsMap[a[2]])) || []
        const displayStatusIconMaps = {
          1: 'white', //过期
          3: 'white', //离线
          7: 'blue', //静止
          6: 'orange', //怠速
          5: 'red', //超速
          8: 'green' //正常速度
        }
        let { icon, changeDir } = this.getCarIcon(car.carType, displayStatusIconMaps[car.displayStatus] || 'blue')
        let carMarker = null
        let carOnMap = this.carOnMapMarkers.find(item => item.carId === car.carId)
        if (carOnMap) {
          //已渲染过车标
          // 更新车辆图标和位置
          carMarker = carOnMap.marker
          this.updateMarkerPosition(carMarker, car.lonc, car.latc, icon, car, changeDir)
          // console.log(this.nowSelectedCarObj)
          if (this.nowSelectedCarObj && this.nowSelectedCarObj.carId === car.carId) {
            let selectedMarker = this.nowSelectedCarObj.marker
            let infoWindow = selectedMarker.getInfoWindow()
            if (infoWindow && infoWindow.isVisible()) {
              // console.log(infoWindow.isVisible())
              //更新显示逻辑
              let htmlStr = await this.getDetailCarTip(car)
              infoWindow.setContent(htmlStr)
              this.bindEventInfo(car, selectedMarker) // 绑定弹窗里的相关点击事件
              this.lockWindowByLngLat(car.lonc, car.latc) //判断打开弹窗窗口是否移动出屏幕外，重新设置中心点
            }
          }
        } else {
          carMarker = new maptalks.Marker([car.lonc, car.latc], {
            visible: true,
            editable: true,
            cursor: 'pointer',
            shadowBlur: 0,
            shadowColor: 'black',
            draggable: false,
            dragShadow: false, // display a shadow during dragging
            drawOnAxis: null, // force dragging stick on a axis, can be: x, y
            zIndex: 9998,
            symbol: {
              markerFile: icon,
              carId: car.carId,
              markerDx: 0,
              markerDy: 16
            }
          })
          if (changeDir) {
            carMarker.updateSymbol({
              markerRotation: 360 - dir
            })
          }
          carMarker.setInfoWindow({
            content: '<div class="map_machineNameBox" style="position:relative">' + car.machineName + '</div>',
            single: false,
            autoPan: false,
            custom: true,
            dx: 0,
            dy: -8
          })
          if (!this.carMarkersClustered) {
            carMarker.addTo(this.carsLayer)
          }
          this.bindInfoWindow(carMarker, car)
          this.carOnMapMarkers.push({ carId: car.carId, marker: carMarker })
          let showMachineName = localStorage.getItem('MachineName') === 'true' ? true : false
          if (showMachineName) {
            carMarker.openInfoWindow()
          }
        }
        //锁定车辆，显示轨迹
        if (this.isShowLock && car.monitorStatusVo && car.monitorStatusVo.online) {
          // 显示锁定的状态的轨迹
          if (car.locked) {
            let point = {
              x: car.monitorStatusVo.lonc,
              y: car.monitorStatusVo.latc,
              color: this.getColorBySpeed(car.monitorStatusVo.speed)
            }
            if (!this.carTrailMap[car.carId]) {
              let arr = []
              arr.push(point)
              this.carTrailMap[car.carId] = arr
              let line = this.drawCarLine(arr, point.color)
              // console.log(line)
              this.carTrailLineMap[car.carId] = [line]
            } else {
              let lineArr = this.carTrailMap[car.carId]
              let lastPoint = lineArr[lineArr.length - 1] // 取出最后一条
              if (lastPoint && lastPoint.color !== point.color) {
                // 如果颜色发生变化
                lineArr = [] // 先重置数组
                lineArr.push(lastPoint) // 然后把上一个加进去作为绘制新颜色的起点
              }
              lineArr.push(point) // 新点加入到数组中
              this.carTrailMap[car.carId] = lineArr
              let line = this.drawCarLine(lineArr, point.color) // 生成新颜色的轨迹
              let arr = this.carTrailLineMap[car.carId]
              arr.push(line)
              this.carTrailLineMap[car.carId] = arr
            }
          }
        }
      }
      // console.timeEnd('showCars')
    },
    bindInfoWindow(marker, car) {
      if (!$) {
        loadScript('/mapjs/jquery.min.js', () => {
          $ = window.$
        })
      }
      marker.on('click', async () => {
        //设备过期
        this.$emit('selectCarMarker', car)
        if (car.serviceState !== 1) {
          this.showCarInfoBox(marker, car) //显示设备信息框
        }
      })
    },
    openTargetCarInfoWindow(car) {
      if (this.nowSelectedCarObj && this.nowSelectedCarObj.carId === car.carId) {
        this.setCenter(car.monitorStatusVo.lonc, car.monitorStatusVo.latc)
        return
      }
      this.carImageInfo = { url: '', hasLoaded: false }
      let carOnMapMarker = this.carOnMapMarkers.find(item => item.carId === car.carId)
      if (carOnMapMarker) {
        this.setCenter(car.monitorStatusVo.lonc, car.monitorStatusVo.latc, 17)
        this.openInfoWindow(carOnMapMarker.marker, car)
      } else {
        // let car = carOnMapPoints[i]
        let dir = car.monitorStatusVo.dir || 0
        // let _car = JSON.parse(JSON.stringify(self.carsMap[a[2]])) || []
        const displayStatusIconMaps = {
          1: 'white', //过期
          3: 'white', //离线
          7: 'blue', //静止
          6: 'orange', //怠速
          5: 'red', //超速
          8: 'green' //正常速度
        }
        let { icon, changeDir } = this.getCarIcon(car.carType, displayStatusIconMaps[car.displayStatus] || 'blue')
        let carMarker = new maptalks.Marker([car.lonc, car.latc], {
          visible: true,
          editable: true,
          cursor: 'pointer',
          shadowBlur: 0,
          shadowColor: 'black',
          draggable: false,
          dragShadow: false, // display a shadow during dragging
          drawOnAxis: null, // force dragging stick on a axis, can be: x, y
          zIndex: 9998,
          symbol: {
            markerFile: icon,
            carId: car.carId,
            markerDx: 0,
            markerDy: 16
          }
        }).addTo(this.carsLayer)
        if (changeDir) {
          carMarker.updateSymbol({
            markerRotation: 360 - dir
          })
        }
        this.carOnMapMarkers.push({ carId: car.carId, marker: carMarker })
        this.setCenter(car.monitorStatusVo.lonc, car.monitorStatusVo.latc, 17)
        this.openInfoWindow(carMarker, car)
      }
    },
    //画框查车显示车辆
    regionalShowCar(data) {
      let carStatus = data.carStatus
      if (carStatus && carStatus.online === 0) {
        //设备离线
        data.displayStatus = 3
        //离线xxx天xx时/分:当前时间戳-heartTime时间戳
        if (carStatus.heartTime) {
          data.durationTime = dayjs().unix() * 1000 - dayjs(carStatus.heartTime).unix() * 1000
        }
      } else if (carStatus && carStatus.speed >= 120) {
        //运动(超速)
        data.displayStatus = 5
      } else if (carStatus && carStatus.speed === 0) {
        //静止
        data.displayStatus = 7
        let idleTime = parseExData(carStatus.exData, 'idleTime') // 怠速时间
        if (idleTime) {
          //怠速
          //怠速xxx天xx时/分:当前时间戳-idleTime时间戳
          data.displayStatus = 6
          data.durationTime = dayjs().unix() * 1000 - dayjs(idleTime).unix() * 1000
        } else if (carStatus.staticTime) {
          //静止xxx天xx时/分:当前时间戳-staticTime时间戳
          data.durationTime = dayjs().unix() * 1000 - dayjs(carStatus.staticTime).unix() * 1000
        }
      } else {
        //运动(正常)
        data.displayStatus = 8
      }
      //特殊的状态显示，比如断油电，ACC开关？....
      if (carStatus && carStatus.status == 12) {
        data.exDisplayStatus = 21
      }
      data.monitorStatusVo = carStatus
      data.lonc = carStatus.lonc
      data.latc = carStatus.latc
      this.openTargetCarInfoWindow(data)
    },
    async openInfoWindow(carOnMapMarker, car) {
      if (!$) {
        loadScript('/mapjs/jquery.min.js', () => {
          $ = window.$
        })
      }
      this.showCarInfoBox(carOnMapMarker, car) //显示设备信息框
    },
    async showCarInfoBox(marker, carData) {
      this.updateAddressCount = 10
      let infoBoxWidth = 410 //监控页面 宽度300
      let htmlStr = await this.getDetailCarTip(carData)
      marker.setInfoWindow({
        width: infoBoxWidth,
        content: htmlStr,
        single: true,
        autoPan: false,
        custom: false,
        dx: 0,
        dy: 4,
        zIndex: 999
      })
      this.nowSelectedCarObj = {
        marker: marker,
        carId: carData.carId,
        lonc: carData.monitorStatusVo.lonc,
        latc: carData.monitorStatusVo.latc,
        lon: carData.monitorStatusVo.lon,
        lat: carData.monitorStatusVo.lat
      }
      marker.openInfoWindow()
      // marker.openInfoWindow()
      this.bindEventInfo(carData, marker) // 绑定弹窗里的相关点击事件
      // 先生成弹框再地址解析
      this.carInfoLoadAddress(this.nowSelectedCarObj) //立刻获取一次地址
      this.updateAddressInterval() //启动地址轮询计时器
    },
    // 获取子标签配置
    async getSubTagConfig({ carId, machineName }) {
      // eslint-disable-next-line no-prototype-builtins
      if (subTagRecord.hasOwnProperty(machineName)) return subTagRecord[machineName]
      const result = await getNormalOrderList({ carId, clientType: 'PC' })
      if (!result.ret || !result.data.some(item => item.orderType === 999)) {
        subTagRecord[machineName] = false
        return false
      } else {
        subTagRecord[machineName] = true
        return true
      }
    },

    // 提取设备基础信息--用于生成卡片头部
    getCarBasicInfo(carData) {
      const _carStatus = carData.monitorStatusVo || {}
      const imei = carData.imei

      // 设备状态信息
      let _color = ''
      let _statusText = ''
      let _statusExText = ''

      if (carData.serviceState === 1) {
        _statusText = this.$t('Jet1u7xx2NupyskORg_e-')
        _color = 'red'
        _statusExText = carData.expireDay
      } else if (carData.displayStatus === 3) {
        _statusText = this.$t('dtFn22xfEx789uFKvaG_n')
        _statusExText = DiffTimeSampleFormateUtil(carData.durationTime)
      } else if (carData.displayStatus === 6) {
        _statusText = this.$t('Bad9L51R3bTOzbZK96v4M')
        _statusExText = DiffTimeSampleFormateUtil(carData.durationTime)
        _color = 'orange'
      } else if (carData.displayStatus === 7) {
        _statusText = this.$t('goF8T8dHU6gao6PljrtNi')
        _statusExText = DiffTimeSampleFormateUtil(carData.durationTime)
        _color = 'blue'
      } else if (carData.displayStatus === 5 || carData.displayStatus === 8) {
        _statusText = this.$t('OCvfOWyVHgRIOLEA021Kl')
        _statusExText = MeterToKmMi(_carStatus.speed * 1000) + this.miledgeUnitEN + '/h'
        _color = 'green'
        if (carData.displayStatus === 5) {
          _color = 'red'
        }
      }

      return {
        imei,
        machineName: carData.machineName,
        statusColor: _color,
        statusText: _statusText,
        statusExText: _statusExText,
        carStatus: _carStatus
      }
    },
    // 获取信号信息--用于生成卡片内容区域
    getSignalInfo(carStatus) {
      const _pointTime = timeConvert(carStatus.pointTime, 'local')
      const _heartTime = timeConvert(carStatus.heartTime, 'local')

      let _pointstr = ''
      let _line = ''
      if (this.lang === 'cn' || this.lang === 'tw') {
        _pointstr = carStatus.pointTypeDes ? carStatus.pointTypeDes : this.$t('lg.pointedarray.' + carStatus.pointType)
        _line = 'line1'
      } else {
        _pointstr = 'GSM Update：' + _heartTime + '<br/>' + 'GPS Update：' + _pointTime
        _line = 'line2'
      }

      return {
        heartTime: _heartTime,
        pointTime: _pointTime,
        pointString: _pointstr,
        lineClass: _line
      }
    },
    // 获取电量信息--用于生成卡片头部
    getPowerInfo(carData, carStatus) {
      let showPower = 'hide'
      let _power

      if ([130, 177].indexOf(+carData.machineType) !== -1) {
        _power = parseExData(carStatus.exData, 'vehiclePowerPercent')
      } else {
        _power = parseExData(carStatus.exData, 'power')
      }
      _power = _power === '0%' ? '1%' : _power

      if (carStatus.wireless || carData.machineType === 35 || +carData.machineType === 130 || +carData.machineType === 138 || +carData.machineType === 199) {
        showPower = ''
      }

      if ([130, 208, 215].indexOf(+carData.machineType) !== -1 && !_power) {
        showPower = 'hide'
      }

      // 电量样式计算
      let powerNumber = _power ? Number(_power.substring(0, _power.length - 1)) : 0
      let batteryClass = ''
      let batteryBorderColor = '#42b041'
      let powerNumberOffset = 7

      switch (true) {
        case powerNumber > 100:
          powerNumber = 100
          batteryClass = 'green'
          batteryBorderColor = '#42b041'
          break
        case powerNumber > 50:
          batteryClass = 'green'
          batteryBorderColor = '#42b041'
          break
        case powerNumber > 20:
          batteryClass = 'yellow'
          batteryBorderColor = '#F79C00'
          break
        case powerNumber >= 0:
          batteryClass = 'red'
          batteryBorderColor = '#FF6648'
          break
      }

      switch (true) {
        case powerNumber >= 100:
          powerNumberOffset = 4
          break
        case powerNumber >= 10:
          powerNumberOffset = 7
          break
        case powerNumber >= 0:
          powerNumberOffset = 9
          break
      }

      let batteryMask = Math.ceil((powerNumber / 100) * 23 + 1)

      return {
        showPower,
        power: _power,
        powerNumber,
        batteryClass,
        batteryBorderColor,
        powerNumberOffset,
        batteryMask
      }
    },
    // 获取设备类型信息--用于生成卡片内容区域
    getDeviceTypeInfo(carStatus) {
      const _wireType = carStatus.wireless ? this.$t('K_s5NFaeLyxrujarrzfMk') : this.$t('XoaUHxf1yCHUm8G-ttmOA')
      return {
        wireType: _wireType
      }
    },
    // 获取里程信息--用于生成卡片内容区域
    async getMileageInfo(carData) {
      const mileageObj = await getMileageStaById({ carId: carData.carId })
      let _mileage = 0
      let totalMileages = 0

      if (mileageObj.ret === 1 && mileageObj.data) {
        if (mileageObj.data.todayMileages) {
          _mileage = MeterToKmMi(parseFloat(mileageObj.data.todayMileages))
        }
        if (mileageObj.data.totalMileages) {
          totalMileages = MeterToKmMi(parseFloat(mileageObj.data.totalMileages))
        }
      }

      const _mileageTitle =
        this.$t('XsXoMT3CDfSMMxm_H8UGU') +
        ':  ' +
        _mileage +
        this.miledgeUnitEN +
        ',' +
        this.$t('wegasqASDE32446sgwe39') +
        ':  ' +
        totalMileages +
        this.miledgeUnitEN

      return {
        todayMileage: _mileage,
        totalMileage: totalMileages,
        mileageTitle: _mileageTitle
      }
    },
    // 获取地址信息--用于生成卡片内容区域
    getAddressInfo(carData, carStatus) {
      let _address = this.$t('3DYXD-6GdyKqIdA4QkIPq')

      if (this.nowSelectedCarObj && this.nowSelectedCarObj.carId === carData.carId && this.nowSelectedCarObj.address) {
        _address = this.nowSelectedCarObj.address
      }

      if (carStatus.lat && carStatus.lon) {
        this.lat = carStatus.lat
        this.lon = carStatus.lon
      }

      const _second = this.updateAddressCount

      return {
        address: _address,
        second: _second
      }
    },
    // 生成电池HTML
    generateBatteryHtml(powerInfo) {
      const { batteryClass, batteryMask, batteryBorderColor, powerNumberOffset, powerNumber } = powerInfo

      return `<div class="${batteryClass}-battery">
      <div class="battery-bg"></div>
      <div class="battery-mask" style="left:${batteryMask}px;"></div>
        <svg class="battery-border" width="25px" height="14px" viewBox="0 0 25 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <g fill="none" x="5">
          <g transform="translate(-407.000000, -31.000000)" fill-rule="nonzero" stroke="${batteryBorderColor}">
            <g transform="translate(408.000000, 32.000000)">
                <path d="M21.9732143,2.56034483 L20.6127232,2.56034483 C20.5524783,2.56041067 20.4923542,2.56577587 20.4330357,2.57637931 L20.4330357,2.06896552 C20.4330357,0.926307414 19.5136205,0 18.3794643,0 L2.05357143,0 C0.919415246,0 0,0.926307414 0,2.06896552 L0,9.93103448 C0,11.0736926 0.919415246,12 2.05357143,12 L18.3794643,12 C19.5136205,12 20.4330357,11.0736926 20.4330357,9.93103448 L20.4330357,9.42362069 C20.4923542,9.43422413 20.5524783,9.43958933 20.6127232,9.43965517 L21.9732143,9.43965517 C22.5402924,9.43965517 23,8.97650147 23,8.40517241 L23,3.59482759 C23,3.02349853 22.5402924,2.56034483 21.9732143,2.56034483 Z" id="形状"></path>
            </g>
          </g>
        </g>
        <text class="battery-value" x="${powerNumberOffset}" y="10" fill="black">${powerNumber}</text>
      </svg>
    </div>`
    },

    // 生成卡片头部
    generateCardHeader(basicInfo, powerInfo, imgs) {
      const { imei, machineName, statusColor, statusText, statusExText } = basicInfo
      const battery = this.generateBatteryHtml(powerInfo)

      return (
        `<div id="carInfoPanel" class="${this.lang}">` +
        `<div class="topRow">` +
        `<div>` +
        `<span class="name" title="${imei}">${machineName}</span>` +
        `<span class="txt1 ${statusColor}">${statusText}</span>` +
        `<span class="txt2 ${statusColor}">(${statusExText})</span>` +
        `</div>` +
        `<div class="topRowIcons">` +
        `<div class="wrapelec ${powerInfo.showPower}">${battery}</div>` +
        `</div>` +
        `</div>`
      )
    },

    // 生成卡片内容区域
    generateCardContent(signalInfo, deviceInfo, mileageInfo, imgs) {
      const { heartTime, pointTime, pointString, lineClass } = signalInfo
      const { wireType } = deviceInfo
      const { todayMileage, mileageTitle } = mileageInfo

      return (
        `<div class="carInfo-container">` +
        `<div class="wrapcol">` +
        `<div class="col help">` +
        `<div class="labeltxt">${this.$t('Ii_xMJ-aK06I6WjE_7RvK')}：</div>` +
        `<div class="icon"><img class="sign" src="${imgs[1]}"/></div>${heartTime}` +
        `<div class="hoverinfoTips">` +
        `${this.$t('LoX_Y5XcmGFXPrKocd3Vy')}<br>` +
        `${this.$t('uHD_UGEfA88QbFYjH40Sk')}<br>` +
        `${this.$t('CfXyIfQ7YrDKYGs6IcEjE')}` +
        `</div>` +
        `</div>` +
        `<div class="col help" title="${this.$t('lg.type')}">` +
        `<div class="labeltxt">${this.$t('lg.type')}：</div>` +
        `<div class="icon"><img class="wireline" src="${imgs[2]}"/></div>` +
        `${wireType}` +
        `</div>` +
        `<div class="col help">` +
        `<div class="labeltxt">${this.$t('SKow9wO-zFrJ-bTY4iiNk')}：</div>` +
        `<div class="icon"><img class="location" src="${imgs[3]}"/></div>` +
        `${pointTime}` +
        `<div class="hoverinfoTips h60 ${lineClass}">${pointString}</div>` +
        `</div>` +
        `<div class="col help" title="${mileageTitle}">` +
        `<div class="labeltxt">${this.$t('D-3CnkxFwEGtcITqH7fbB')}：</div>` +
        `<div class="icon"><img class="mileage" src="${imgs[4]}"/></div>` +
        `${todayMileage}${this.miledgeUnitCN}` +
        `</div>` +
        `</div>` +
        `</div>`
      )
    },

    // 获取权限信息--用于生成操作按钮区域
    getPermissionsInfo() {
      const isVirtualUser = this.isVirtualUser ? 'hide' : ''
      let _showTrack = 'hide'
      let _showReplay = 'hide'
      let _showCom = 'hide'

      this.$store.state.permissions.forEach(item => {
        if (item.perms === 'device:monitor:track') {
          _showTrack = ''
        }
        if (item.perms === 'device:monitor:playback') {
          _showReplay = ''
        }
        if (item.perms === 'device:remote:control') {
          _showCom = ''
        }
      })

      return {
        isVirtualUser,
        showTrack: _showTrack,
        showReplay: _showReplay,
        showCommand: _showCom
      }
    },

    // 生成地址区域--用于生成卡片内容区域
    generateAddressSection(addressInfo, imgs) {
      const { address, second } = addressInfo

      return (
        `<div class="address">` +
        `<img class="addr" src="${imgs[9]}"/>` +
        `<span id="monitor_address">${address}</span>` +
        `<span class="second">(<span id="monitor_second">${second}</span>S)</span>` +
        `</div>`
      )
    },

    // 生成操作按钮区域--用于生成卡片内容区域
    generateOperationSection(permissionsInfo) {
      const { isVirtualUser, showTrack, showReplay, showCommand } = permissionsInfo

      return (
        `<div class="opt">` +
        `<div class="item detail ${isVirtualUser}" title="${this.lang === 'cn' ? '' : this.$t('d2rfxOdgIK8sx-aXn_UjT')}">` +
        `<span class="spantxt">${this.$t('d2rfxOdgIK8sx-aXn_UjT')}</span>` +
        `</div>` +
        `<div class="item location ${showTrack}" title="${this.lang === 'cn' ? '' : this.$t('2_GOBlwzPbyr5HQDEsUOK')}">` +
        `<span class="spantxt">${this.$t('2_GOBlwzPbyr5HQDEsUOK')}</span>` +
        `</div>` +
        `<div class="item play ${showReplay}" title="${this.lang === 'cn' ? '' : this.$t('FNZ9KgOe3_nZPHLr-gemV')}">` +
        `<span class="spantxt">${this.$t('FNZ9KgOe3_nZPHLr-gemV')}</span>` +
        `</div>` +
        `<div class="item command ${isVirtualUser || showCommand}" title="${this.lang === 'cn' ? '' : this.$t('nG52x-1HCtQNlZdzdGke_')}">` +
        `<span class="spantxt">${this.$t('nG52x-1HCtQNlZdzdGke_')}</span>` +
        `</div>` +
        `</div>`
      )
    },
    // 生成完整卡片--用于生成卡片内容区域
    async generateSimpleCarCard(carData) {
      const imgs = this.infoImages
      // 获取各部分信息
      const basicInfo = this.getCarBasicInfo(carData)
      const signalInfo = this.getSignalInfo(basicInfo.carStatus)
      const powerInfo = this.getPowerInfo(carData, basicInfo.carStatus)
      const deviceInfo = this.getDeviceTypeInfo(basicInfo.carStatus)
      const mileageInfo = await this.getMileageInfo(carData)
      const addressInfo = this.getAddressInfo(carData, basicInfo.carStatus)
      const permissionsInfo = this.getPermissionsInfo()

      // 组装完整HTML
      const cardHeader = this.generateCardHeader(basicInfo, powerInfo, imgs)
      const cardContent = this.generateCardContent(signalInfo, deviceInfo, mileageInfo, imgs)
      const addressSection = this.generateAddressSection(addressInfo, imgs)
      const operationSection = this.generateOperationSection(permissionsInfo)
      const cardEnd = '</div>'

      return cardHeader + cardContent + addressSection + operationSection + cardEnd
    },
    // 主入口方法
    async getDetailCarTip(carData) {
      return await this.generateSimpleCarCard(carData)
    },
    bindEventInfo(carData, marker) {
      const self = this
      let car = carData
      let _carStatus = carData.monitorStatusVo || {}
      $('#carInfoPanel .serviceName span') // 显示经销商
        .unbind()
        .click(function() {
          self.showParentService = true
        })
      $('#carInfoPanel .opt .share') // 分享
        .unbind()
        .click(function() {
          self.shareData = JSON.parse(JSON.stringify(car))
          self.shareHistoryFlag = true
        })
      $('#carInfoPanel .opt .location') // 跟踪
        .unbind()
        .click(function() {
          // window.open('../../page/map.html?page=tailAfter&carId=' + car.carId + '&lang=' + self.lang, '_blank')
          const { href } = self.$router.resolve({
            name: 'Track',
            query: {
              carId: car.carId
            }
          })
          window.open(href, '_blank')
        })
      $('#carInfoPanel .drownFence .fence1') // 圆形
        .unbind()
        .click(function() {
          // 同时关闭信息弹窗
          marker.removeInfoWindow()
          self.bindFenCarId = car.carId
          self.drawFence('Circle')
        })
      $('#carInfoPanel .drownFence .fence2') // 多边形
        .unbind()
        .click(function() {
          // 同时关闭信息弹窗
          marker.removeInfoWindow()
          self.bindFenCarId = car.carId
          self.drawFence('Polygon')
        })
      $('#carInfoPanel .drownFence .fence3') // 行政区域
        .unbind()
        .click(function() {
          self.bindFenCarId = car.carId
          //显示区域选择框
          self.areaSelcetDialogVisible = true
        })
      $('#carInfoPanel .drownFence .fence4') // 查看围栏
        .unbind()
        .click(function() {
          self.fenceDialogCarObj = {
            carId: car.carId,
            imei: car.imei,
            machineName: car.machineName
          }
          self.fenceDialogVisible = false
          setTimeout(() => {
            self.fenceDialogVisible = true
          }, 200)
        })
      $('#carInfoPanel .opt .play') // 回放
        .unbind()
        .click(function() {
          const { href } = self.$router.resolve({
            name: 'History',
            query: {
              carId: car.carId,
              machineType: car.machineType,
              userId: car.userId,
              lat: _carStatus.latc,
              lon: _carStatus.lonc,
              lang: self.lang,
              wiretype: car.wireless,
              carType: car.carType
            }
          })
          window.open(href, '_blank')
        })

      $('#carInfoPanel .opt .command') // 指令
        .unbind()
        .click(function() {
          self.$emit('deviceDetailFromMap', {
            carId: car.carId,
            activeName: 'command'
          })
        })
      $('#carInfoPanel .carInfo-container .c-sub-tooltip__icon')
        .unbind()
        .click(function() {
          self.$emit('deviceDetailFromMap', {
            carId: car.carId,
            activeName: 'subtag'
          })
        })
      $('#carInfoPanel .opt .street') // 街景
        .unbind()
        .click(function() {
          let zoom = self.nowZoom
          if (self.mapType === 1) {
            window.open('../../page/baiduStreetView.html?lng=' + _carStatus.lonc + '&lat=' + _carStatus.latc, '_blank')
          } else {
            window.open(
              'https://www.google.com/maps?q=' + _carStatus.latc + ',' + _carStatus.lonc + '&ll=' + _carStatus.latc + ',' + _carStatus.lonc + '&z=' + zoom,
              '_blank'
            )
          }
        })
      $('#carInfoPanel .opt .detail') // 详细
        .unbind()
        .click(function() {
          self.$emit('deviceDetailFromMap', {
            carId: car.carId,
            activeName: 'detail'
          })
          // self.$parent.chooseMenuIitem(6)
          /* if (window.frames['OldSystemFrame'] && window.frames['OldSystemFrame'].contentWindow) {
            self.setIframeIndex(1003)
            document.getElementsByTagName('body')[0].setAttribute('class', 'el-popup-parent--hidden')
            // 调用旧系统dialog.js里的方法
            window.frames['OldSystemFrame'].contentWindow.g_Main.dialog.machineInfoDialog(car.carId, car.userId, 'vuepage')
          } */
        })
      $('#carInfoPanel .opt .analysis') // 统计
        .unbind()
        .click(function() {
          self.$cookies.set('carId', car.carId)
          self.$router.push({ name: 'Statistics' })
        })
      window.set_hide_iframe = function() {
        self.setIframeIndex(-1)
        document.getElementsByTagName('body')[0].removeAttribute('class')
      }
      $('#carInfoPanel .optRew') // 续费
        .unbind()
        .click(function() {
          if (window.frames['OldSystemFrame'] && window.frames['OldSystemFrame'].contentWindow) {
            self.setIframeIndex(1003)
            document.getElementsByTagName('body')[0].setAttribute('class', 'el-popup-parent--hidden')
            // 调用旧系统dialog.js里的方法
            window.frames['OldSystemFrame'].contentWindow.g_Main.dialog.searchResultDialog(self.carsMap[self.nowSelectedCarObj.carId].machineName, 0, 2)
          }
        })
      $('.maptalks-msgBox .maptalks-close') // 关闭回显设备名称
        .unbind()
        .click(function() {
          if (localStorage.getItem('MachineName') === 'true') {
            // 设备名称在车辆信息框关闭时回显
            marker.removeInfoWindow()
            marker.setInfoWindow({
              content: '<div class="map_machineNameBox" style="position:relative">' + car.machineName + '</div>',
              single: false,
              autoPan: false,
              custom: true,
              dx: 0,
              dy: -8
            })
            marker.openInfoWindow()
          }
          self.nowSelectedCarObj = null
          self.carImageInfo = { url: '', hasLoaded: false }
          if (self.updateAddressTimer) {
            clearInterval(self.updateAddressTimer)
            self.updateAddressTimer = null
          }
        })
      $('#carInfoPanel .opt .diagnosis')
        .unbind()
        .click(function() {
          self.$emit('deviceDetailFromMap', {
            carId: car.carId,
            activeName: 'diagnosis'
          })
        })

      $('#carInfoPanel .topRow .videoIcon')
        .unbind()
        .click(function() {
          const { href } = self.$router.resolve({
            name: 'TimeVideo',
            query: {
              carId: car.carId,
              userId: car.userId,
              imei: car.imei
            }
          })
          window.open(href, '_blank')
        })
      $('#carInfoPanel .topRow .intercomIcon')
        .unbind()
        .click(() => {
          if (this.videoType === 1) {
            //对讲弹窗中，直接返回
            return
          }
          if (this.videoType === 0) {
            //监听弹窗中
            this.$message({
              message: this.$t('IKa5v1S4C9m3JqKSNCGDm'),
              type: 'warning'
            })
          }
          this.showIntercomPop = true
          $('#carInfoPanel .intercom-popover').removeClass('hide')
          $('#carInfoPanel .intercom-popover-corner').removeClass('hide')
        })
      $('#carInfoPanel .intercom-popover .popover-btn-text')
        .unbind()
        .click(() => {
          this.showIntercomPop = false
          $('#carInfoPanel .intercom-popover').addClass('hide')
          $('#carInfoPanel .intercom-popover-corner').addClass('hide')
        })
      $('#carInfoPanel .intercom-popover .popover-btn-primary')
        .unbind()
        .click(() => {
          this.showIntercomPop = false
          $('#carInfoPanel .intercom-popover').addClass('hide')
          $('#carInfoPanel .intercom-popover-corner').addClass('hide')
          this.handleShowIntercom(car)
        })
    },
    async carInfoLoadAddress(carObj) {
      $('#monitor_address').html(this.$t('3DYXD-6GdyKqIdA4QkIPq'))
      let { carId, lon, lat, lonc, latc } = carObj
      // let _carStatus = car.monitorStatusVo
      // 进行地址解析--替换内容
      try {
        const address = await loadAddress({
          carId,
          lon,
          lat,
          lonC: lonc,
          latC: latc,
          businessType: 12
        })
        $('#monitor_address').html(address)
        //获取成功储存这次的记录,用作判断上次获取地址的lonc和latc是否变化决定重新解析地址
        if (this.nowSelectedCarObj) {
          this.nowSelectedCarObj.address = address
          this.nowSelectedCarObj.lonc = lonc
          this.nowSelectedCarObj.latc = latc
        }
      } catch (error) {
        // console.log(error)
      }
    },
    async carInfoLoadImage(car) {
      const { ret, data } = await _getCarImg({ carId: car.carId })
      if (ret === 1 && data) {
        this.carImageInfo.url = data.url
        this.carImageInfo.hasLoaded = true
        let htmlStr = `<img src="${data.url}"/>`
        let container = document.getElementById('carImageContainer')
        let carInfo = document.getElementById('carInfoPanel')
        if (container) {
          carInfo.classList.add('hasImage')
          container.innerHTML = htmlStr
          container.classList.remove('hide')
        }
        // $('#carImageContainer').html(htmlStr)
      } else {
        let container = document.getElementById('carImageContainer')
        if (container) {
          container.classList.add('hide')
        }
      }
    },
    updateAddressInterval() {
      this.updateAddressCount = 10
      if (this.updateAddressTimer) {
        clearInterval(this.updateAddressTimer)
      }
      this.updateAddressTimer = setInterval(() => {
        if (!this.nowSelectedCarObj) {
          //当前已经没有选择的车辆marker
          clearInterval(this.updateAddressTimer)
          this.updateAddressTimer = null
          this.updateAddressCount = 10
          return
        }
        $('#monitor_second').html(this.updateAddressCount)
        this.updateAddressCount--
        if (this.updateAddressCount < 0) {
          let { carId, address, lonc, latc } = this.nowSelectedCarObj
          let car = this.carOnMapPoints.find(item => item.carId === carId)
          if (car) {
            if (!address || car.monitorStatusVo.lonc !== lonc || car.monitorStatusVo.latc !== latc) {
              //当前还没有获取过地址，或者选中的车辆里上一次获取地址时保存的经纬度坐标已变化
              let carObj = {
                //新的获取地址入参
                carId: car.carId,
                lon: car.monitorStatusVo.lon,
                lat: car.monitorStatusVo.lat,
                lonc: car.monitorStatusVo.lonc,
                latc: car.monitorStatusVo.latc
              }
              this.carInfoLoadAddress(carObj)
            }
          }
          this.updateAddressCount = 10
        }
      }, 1000)
    },
    //更新标记点位置
    updateMarkerPosition: function(marker, lon, lat, icon, car, changeDir) {
      let carId = car.carId
      let dir = car.monitorStatusVo.dir || 0
      marker.setCoordinates({
        x: lon,
        y: lat
      })
      let obj = {
        markerFile: icon,
        carId: carId,
        markerDx: 0,
        markerDy: 16
      }
      if (changeDir) {
        obj.markerRotation = 360 - dir
      }
      marker.setSymbol(obj)
    },

    getCarIcon(carType, color) {
      let carTypeMap = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 22, 23, 24, 31, 32, 33, 34, 35] //目前只配置了这些图标，如果carType不在包含里，使用默认图标
      let iconType = Number(carType)
      let url = ''
      let changeDir = true
      if (!carTypeMap.includes(iconType)) {
        url = `/images/map/machine/3/${color}_0.png`
      } else if ([1, 2, 16, 22, 31, 32, 33, 34, 35].includes(iconType)) {
        //车辆类型为1，则是没有方向的水滴icon, 类型为2和16是个人，22是宠物，这些图标都没有方向
        url = `/images/map/machine/${iconType}/${color}.png`
        changeDir = false
      } else {
        url = `/images/map/machine/${iconType}/${color}_0.png`
      }
      return { icon: url, changeDir }
    },
    toggleMachineName(flag) {
      for (let i of this.carOnMapMarkers) {
        if (!this.nowSelectedCarObj || this.nowSelectedCarObj.carId !== i.carId) {
          if (flag) {
            i.marker.openInfoWindow()
          } else {
            i.marker.closeInfoWindow()
          }
        }
      }
    },
    handlePolymerization(flag) {
      if (this.swdMapTalk && this.swdMapTalk.getLayer('cluster')) {
        this.swdMapTalk.removeLayer('cluster')
      }
      let markers = this.carOnMapMarkers.map(markerObj => {
        return markerObj.marker
      })
      if (flag) {
        this.carMarkersClustered = true
        localStorage.setItem('carMarkersClustered', true)
        markers.forEach(marker => {
          marker.remove()
        })
        let clusterLayer = new ClusterLayer('cluster', markers, {
          noClusterWithOneMarker: true,
          maxClusterZoom: 12,
          //"count" is an internal variable: marker count in the cluster.
          symbol: {
            markerType: 'ellipse',
            markerFill: {
              property: 'count',
              type: 'interval',
              stops: [
                [0, '#3F7EFD'],
                [20, '#3F7EFD'],
                [40, '#3F7EFD']
              ]
            },
            markerFillOpacity: 1,
            markerLineOpacity: 0.6,
            markerLineWidth: 10,
            markerLineColor: '#3F7EFD',
            markerWidth: {
              property: 'count',
              type: 'interval',
              stops: [
                [0, 40],
                [18, 60],
                [60, 80]
              ]
            },
            markerHeight: {
              property: 'count',
              type: 'interval',
              stops: [
                [0, 40],
                [18, 60],
                [60, 80]
              ]
            }
          },
          textSymbol: {
            textFaceName: '"microsoft yahei"',
            textFill: '#FFFFFF',
            textSize: 16,
            textDx: 0,
            textDy: 0
          },
          drawClusterText: true,
          geometryEvents: true,
          single: true
        })
        // if (!this.vecLayer) {
        //   this.vecLayer = new maptalks.VectorLayer('vector').addTo(this.swdMapTalk)
        // }
        this.swdMapTalk.addLayer(clusterLayer)
        this.swdMapTalk.on('click', e => {
          let res = clusterLayer.identify(e.coordinate)
          if (res && res.children && res.children.length) {
            let _center = res.children[0]._coordinates
            // console.log('res', res)
            this.nowZoom += 4
            if (this.nowZoom > 20) {
              this.nowZoom = 20
            }
            if (res.children.length) {
              let points = []
              res.children.forEach(item => {
                points.push({ lonc: item._coordinates.x, latc: item._coordinates.y })
              })
              let center = this.getCenterPoint(points)
              _center = { x: center.lon, y: center.lat }
              // 50 台设备放到最大
              /* if (res.children.length <= 50) {
              self.nowZoom = 13
            } */
            }
            this.swdMapTalk.setCenter(_center)
            this.swdMapTalk.setZoom(this.nowZoom)
          }
        })
      } else {
        this.carMarkersClustered = false
        localStorage.removeItem('carMarkersClustered')
        // 移除聚合图层
        if (this.swdMapTalk.getLayer('cluster')) {
          this.swdMapTalk.removeLayer('cluster')
        }
        markers.forEach(marker => {
          marker.remove()
          marker.addTo(this.carsLayer)
        })
      }
    },
    // 清除车辆的轨迹线
    clearLockedCarLine(carIds) {
      carIds.forEach(carId => {
        let lineArr = this.carTrailLineMap[carId]
        if (lineArr && lineArr.length > 0) {
          lineArr.forEach(item => {
            item.remove()
          })
          delete this.carTrailMap[carId]
        }
      })
    },
    // 绘制车辆的轨迹线
    drawCarLine(lineArr, color) {
      if (!this.vecLayer) {
        this.vecLayer = new maptalks.VectorLayer('vector').addTo(this.swdMapTalk)
      }
      let line = new maptalks.LineString(lineArr, {
        symbol: {
          lineColor: color,
          lineWidth: 6,
          lineJoin: 'round', //miter, round, bevel
          lineCap: 'round', //butt, round, square
          lineDasharray: null, //dasharray, e.g. [10, 5, 5]
          lineOpacity: 1
        }
      }).addTo(this.vecLayer)
      return line
    }
  }
}

export default ShowCarMixin
