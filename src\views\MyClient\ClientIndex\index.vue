<template>
  <!-- 页面 -->
  <div class="page">
    <!-- 容器 -->
    <div class="content">
      <!-- 控制用户树模块折叠 -->
      <div class="expand_arrow_left" :class="{ fold: !collapseTree }" @click="collapseTree = !collapseTree">
        <img src="@/assets/img/icon/icon_fold_left.png" alt="icon_fold_left" />
      </div>
      <!-- 左侧用户树 -->
      <ClientTree ref="clientThree" :collapseTree.sync="collapseTree" @goSearch="goSearch" @treeMeunClick="treeMeunClick" />

      <!-- <div v-if="collapseTree" class="page-line"></div> -->

      <div class="right" :style="collapseTree ? 'width:100%' : 'width:calc(100% - 280px)'">
        <!-- <svg-icon v-show="!collapseTree" icon-class="tree_expand" class="expand_arrow_right" @click="collapseTree = true" /> -->
        <!-- 用户资产信息 -->
        <ClientAssets
          ref="ClientAssets"
          :treeType="treeType"
          :permissionArr="permissionArr"
          :deviceSearchParam="deviceSearchParam"
          :targetCustomer="currentNodeData"
          @edit-user="editUserInfo"
          @open-push="openPushRule"
          @filter-change="filterChange"
        />

        <!-- 表格 -->
        <div class="client-table_content">
          <el-tabs class="devlice-client-tab" v-model="activeName" ref="tabs" @tab-click="handleTableTabClick">
            <!-- 我的设备 -->
            <el-tab-pane :label="$t('lOnLduR3l_8WRTpPLViDr')" name="device" v-if="tablePermissionArr.indexOf('Device_List') >= 0">
              <Device
                ref="Device"
                :treeType="treeType"
                :expireTime="expireTime"
                :expireType="expireType"
                :permissionArr="permissionArr"
                :searchParam.sync="deviceSearchParam"
                @device-operate="deviceOperate"
                @open-device-detail="openDeviceDetail"
                @open-copy="openCopyDiaolg"
                @service-renew="serviceRenew"
                @openPackageDetail="openPackageDetail"
              />
            </el-tab-pane>
            <!-- 我的客户 -->
            <!-- <el-tab-pane :label="$t('lg.limits.sub_customers')" name="client" v-if="tablePermissionArr.indexOf('sub_user') >= 0 && userInfo.userType !== 2">
              <Customers ref="ClientTable" :permissionArr="permissionArr" @ClientTableMoreOporateClick="handleClientTableMoreOporateClick" />
            </el-tab-pane> -->
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 编辑用户信息 -->
    <base-dialog
      :title="this.$t('lg.logDict.update')"
      custom-class="swd-dialog"
      :visible.sync="showEditDialog"
      :close-on-click-modal="false"
      width="515px"
      @cancel="handleEditCancel"
      @ok="handleEditConfirm"
    >
      <EditClient ref="editClient" :ButtonShow="false" :editData="currentUser" @closeEditClientDialog="closeEditClientDialog" type="edit"></EditClient>
    </base-dialog>

    <!-- 推送规则弹窗 -->
    <PushRules :id="userInfo.userId" :visible.sync="showPushRule" v-if="showPushRule" />

    <!-- 用户树右键菜单弹窗相关 开始 -->

    <!-- 销售设备 -->
    <base-dialog
      :title="this.$t('lg.limits.Bulk_sales')"
      :visible.sync="showBatchSale"
      :close-on-click-modal="false"
      append-to-body
      custom-class="swd-dialog"
      :before-close="handleSaleClose"
      width="733px"
      @ok="dialogOk('deviceSale', 'confirmSale')"
      @cancel="dialogCancel('deviceSale', 'resetForm')"
    >
      <batch-sale
        ref="deviceSale"
        @cancel="showBatchSale = false"
        @sale-succss="refreshTableAndTree('device')"
        :targetCustomer="currentNodeData"
        :showBatchSale="showBatchSale"
        :MenuNode="meunCurrentCxt"
      />
    </base-dialog>

    <!-- 导入设备 -->
    <base-dialog
      :title="$t('lg.limits.Batch_Import')"
      :visible.sync="showBatchImport"
      :close-on-click-modal="false"
      append-to-body
      custom-class="swd-dialog"
      :before-close="handleImportClose"
      width="550px"
      @cancel="dialogCancel('deviceImport', 'resetForm')"
      @ok="dialogOk('deviceImport', 'confirmBatchImport')"
    >
      <batch-import
        ref="deviceImport"
        @cancel="showBatchImport = false"
        @sale-succss="refreshTableAndTree('device')"
        :targetCustomer="currentNodeData"
        :showBatchImport="showBatchImport"
      />
    </base-dialog>

    <!-- 新增用户 -->
    <base-dialog
      @ok="handleAddConfirm"
      @cancel="handleAddCancel"
      :title="$t('asdiOIosdj_564Hjoijid')"
      custom-class="client-dialog"
      :visible.sync="addSubVisible"
      :close-on-click-modal="false"
      width="650px"
    >
      <add-sub ref="addSub" @closeAccountDialog="closeAccountDialog" :showButton="false"></add-sub>
    </base-dialog>

    <!-- 转移客户 -->
    <base-dialog
      :title="this.$t('YQKLFb8qkw8_3Cu2O2IZ_')"
      :visible.sync="showTransferClient"
      :close-on-click-modal="false"
      append-to-body
      width="370px"
      custom-class="transfer-client-dialog"
      @ok="confirmTransferClient"
      @cancel="resetTransferForm"
    >
      <el-form class="flex justify-center">
        <el-form-item :label="$t('lg.targetUser')" v-if="showTransferClient"
          ><search-user style="border:1px solid #dee0e3" ref="transferUserTree" :showSearch="false" @autoSelectUser="transferSelect"></search-user
        ></el-form-item>
      </el-form>
    </base-dialog>
    <!-- 用户树右键菜单弹窗相关 结束 -->

    <!-- 批量转移 -->
    <el-dialog
      :title="this.$t('0HfLeevl06T5-c-6Hybn9')"
      :visible.sync="showBatchTransfer"
      :close-on-click-modal="false"
      append-to-body
      custom-class="swd-dialog"
      @before-close="cancelBatchTransfer"
      @cancel="dialogCancel('deviceTransfer', 'cancel')"
      @ok="dialogOk('deviceTransfer', 'conformBatchTransfer')"
    >
      <BatchTransfer
        @refresh="refreshTableAndTree"
        ref="deviceTransfer"
        @cancel="cancelBatchTransfer"
        :showBatchTransfer="showBatchTransfer"
        :targetCustomer="currentNodeData"
      />
    </el-dialog>

    <base-dialog
      :title="this.$t('batchAlarmSettings')"
      :visible.sync="showBatchPolice"
      :close-on-click-modal="false"
      width="684px"
      append-to-body
      custom-class="swd-dialog"
      @close="batchPoliceCancel"
      @cancel="dialogCancel('batchPoliceRef', 'cancel')"
      @ok="dialogOk('batchPoliceRef', 'ok')"
    >
      <BatchPolice ref="batchPoliceRef" @cancel="batchPoliceCancel" />
    </base-dialog>

    <base-dialog
      :title="this.$t('batchInstructions')"
      :visible.sync="showBatchInstruction"
      :close-on-click-modal="false"
      width="684px"
      append-to-body
      custom-class="swd-dialog"
      @close="batchInstructionCancel"
      @cancel="dialogCancel('batchInstructionRef', 'cancel')"
      @ok="dialogOk('batchInstructionRef', 'ok')"
    >
      <BatchInstruction ref="batchInstructionRef" @cancel="batchInstructionCancel" />
    </base-dialog>

    <!-- 批量升级 -->
    <base-dialog
      :title="this.$t('lg.limits.car_manager_upgrade')"
      :visible.sync="showBatchUpGradation"
      :close-on-click-modal="false"
      width="720px"
      append-to-body
      custom-class="swd-dialog"
      @close="batchUpGradationCancel"
      @cancel="dialogCancel('batchUpGradationRef', 'cancel')"
      @ok="dialogOk('batchUpGradationRef', 'ok')"
    >
      <BathUpGradation ref="batchUpGradationRef" @cancel="batchUpGradationCancel" />
    </base-dialog>

    <!-- 批量修改信息 -->
    <BatchEdit ref="BatchEdit" :showBatchEdit.sync="showBatchEdit" @cancel="showBatchEdit = false" @refresh="refreshDeviceTable"></BatchEdit>

    <!-- 修改型号 -->
    <base-dialog
      width="600px"
      :title="this.$t('pWAH6OlawcOyPzpCQiD3t')"
      :visible.sync="showBatchEditModel"
      :close-on-click-modal="false"
      append-to-body
      custom-class="swd-dialog"
      @ok="dialogOk('batchEditModel', 'confirmEdit')"
      @cancel="dialogCancel('batchEditModel', 'cancel')"
    >
      <BatchEditModel
        @refresh="refreshDeviceTable"
        @cancel="showBatchEditModel = false"
        :imei="batchEditImei"
        :oldModel="editModelMachineType"
        ref="batchEditModel"
        :visible="showBatchEditModel"
      />
    </base-dialog>

    <!-- 修改用户到期 -->
    <base-dialog
      :title="this.$t('lg.limits.Modify_user_expiration')"
      :visible.sync="showChangeServiceTime"
      :close-on-click-modal="false"
      append-to-body
      custom-class="swd-dialog"
      @cancel="dialogCancel('batchEditServiceTime', 'cancel')"
      @ok="dialogOk('batchEditServiceTime', 'comfirmEdit')"
    >
      <BatchEditServiceTime ref="batchEditServiceTime" @cancel="showChangeServiceTime = false" @refresh="refreshDeviceTable" />
    </base-dialog>

    <!-- 设备分组 -->
    <CarGroupManage
      :visible.sync="showGroup"
      :id="currentNodeData"
      @refresh="refreshGroup"
      @refresh-device="carGroupTranRefresh"
      ref="carGroupManage"
    ></CarGroupManage>

    <!-- 设备详情 -->
    <DeviceDetail
      ref="deviceDetailHandle"
      :dialogVisible.sync="showDeviceDetail"
      :info="deviceInfo"
      :userId="deviceUserId"
      @refresh="refreshTableAndTree"
    ></DeviceDetail>

    <!-- 批量续费 -->
    <base-dialog
      :title="this.$t('lg.limits.Batch_renewal')"
      :visible.sync="showBatchCharge"
      :close-on-click-modal="false"
      append-to-body
      custom-class="swd-dialog"
      @close="closeRecharge"
      @cancel="dialogCancel('deviceRecharge', 'cancel')"
      @ok="dialogOk('deviceRecharge', 'conformBatchCharge')"
    >
      <Recharge ref="deviceRecharge" @cancel="showBatchCharge = false" @refresh="refreshTableAndTree('device')" />
    </base-dialog>

    <!-- 专属服务商续费 -->
    <DeviceServiceRenew ref="DeviceServiceRenew" :dialogVisible.sync="showParentService"></DeviceServiceRenew>

    <!-- 复制弹窗 -->
    <CopyDialog ref="Copy" :msg="$t('2gK1OvSK1N490nqe6BmML')" :userName="resetAccount" :passWord="resetPwdValue"></CopyDialog>

    <!-- 批量任务设置 -->
    <BatchTask :dialogVisible.sync="showBatchTask" ref="batchTaskRef" />

    <!-- 客户详情 -->
    <client-detail
      ref="clientDetailHandle"
      v-if="userInfo.userType !== 2"
      @refresh="refreshTableAndTree('client')"
      :dialogVisible.sync="showClientDetail"
      :clientInfo="clientDetailInfo"
      :command="detailCommand"
      @showUser="showCertainUser"
    ></client-detail>
    <change-password :info="clientDetailInfo" :dialogVisible.sync="showChangePassword"></change-password>
    <!-- Start 套餐详情 -->
    <PackageDetails :visible.sync="PackageDetailsVisible" :info="deviceInfo" />
    <!-- End 套餐详情 -->
  </div>
</template>

<script>
/* 
  未完成需求
  1. 右键菜单销售设备成功回调 refreshTableAndTree
  2. 右键菜单导入设备设备成功回调refreshTableAndTree
  3. 新增用户刷新refreshClientTable、refreshTree
  4. 客户转移刷新
  4. 删除用户成功回调 √
*/

/* 
  触发刷新的地方， 设备表格、客户表格 激活的时候才需要刷新

  页面外部：
  1. 设备搜索-详情-发送设备转移： 用户树（保持续当前节点）、上方资产、设备表格、客户表格

  树组件：
  1. 销售设备：用户树（保持续当前节点）、上方资产、设备表格、客户表格
  2. 导入设备：用户树（保持续当前节点）、上方资产、设备表格、客户表格
  3. 新增客户：用户树（保持续当前节点）、上方资产、设备表格、客户表格
  4. 客户转移：用户树（保持续当前节点）、上方资产、设备表格、客户表格
  5. 删除客户：用户树（删除当前目标用户：选中根节点，删除非当前用户：保持续当前节点）、上方资产、设备表格、客户表格
  6. 基本功能： 搜索选中、点击节点、外部传参加载

  上方资产信息：
  1. 编辑信息：用户树（保持续当前节点）、上方资产、设备表格、客户表格
  2. 离线、未用、过期状态筛选：设备表格

  设备表格批量按钮：
  1. 批量销售：用户树（保持续当前节点）、上方资产、设备表格、客户表格
  2. 批量续费：用户树（只需要刷新过期用户树、保持当前节点） 上方资产 、设备表格
  3. 批量导入：用户树（保持续当前节点）、上方资产、设备表格、客户表格
  4. 批量转移：用户树（保持续当前节点）、上方资产、设备表格、客户表格
  5. 批量修改型号：设备表格
  6. 批量修改用户到期： 用户树（过期用户树、保持续当前节点）、上方资产
  7. 删除设备： 用户树（保持续当前节点、上方资产、设备表格、客户表格
  8. 分组管理： 设备表格、刷新当前分组信息、

  设备表格内嵌按钮：
  1. 销售：同上
  2. 详情：用户树（保持续当前节点）、上方资产、设备表格、客户表格
  3. 续费: 同上
  4. 转移：同上
  5. 分组：不触发

  下级客户内嵌按钮：
  1. 转移： 用户树（保持续当前节点）、上方资产、设备表格、客户表格
  2. 详情： 用户树（保持续当前节点）、上方资产、设备表格、客户表格
  3. 停用： ？
*/

import { mapGetters, mapState } from 'vuex'
import ClientTree from './Components/ClientTree.vue' // 用户树
import ClientAssets from './Components/ClientAssets.vue' // 资产信息
import EditClient from '@/components/Bussiness/EditClient' // 编辑用户
import PushRules from '@/components/Device/PushRules' // 推送规则
import Device from './Components/Device.vue' // 设备表格
import Customers from './Components/Customers.vue' // 设备表格

// 右键菜单弹窗相关
import BatchSale from '@/components/Device/DeviceBatchSale' // 销售设备
import BatchImport from '@/components/Device/DeviceBatchImport' // 批量导入
import AddSub from '@/components/Bussiness/AddSub' // 新增用户
import SearchUser from '@/components/Bussiness/SearchUser' // 客户转移

import BatchTransfer from '@/components/Device/DeviceBatchTransfer' // 批量转移
import BatchEdit from '@/components/Device/BatchEdit' // 批量修改
import BatchEditModel from '@/components/Device/BatchEditModel' // 修改型号
import BatchEditServiceTime from '@/components/Device/BatchEditServiceTime' // 修改用户到期
import CarGroupManage from '@/components/Device/CarGroupManage' // 设备分组
import DeviceDetail from '@/components/Device/DeviceDetail' // 设备详情
import Recharge from '@/components/Device/Recharge' // 批量续费
import CopyDialog from '@/components/Bussiness/CopyDialog.vue' // 重置密码复制
import DeviceServiceRenew from '@/components/Device/DeviceServiceRenew' // 专属服务商续费
import ClientDetail from '@/components/Bussiness/ClientDetailDialog' // 客户详情
import ChangePassword from '@/components/Device/ChangePassword' // 我的客户更改密码
import PackageDetails from '@/components/Bussiness/PackageDetails' // 套餐详情
import BatchTask from '@/components/Device/BatchTask.vue'
import BatchPolice from '@/components/Device/BatchPolice.vue'
import BatchInstruction from '@/components/Device/BatchInstruction.vue'
import BathUpGradation from '@/components/Device/BathUpGradation'

// 接口
import { transferUser, resetUserPsw, deleteClient } from '@/api/client.js'
import { batchDelete } from '@/api/device.js'
import { getCarGroup } from '@/api/monitor.js'
import { pwdReserValue } from '@/utils/common.js'

export default {
  name: 'MyClient',
  components: {
    ClientTree,
    ClientAssets,
    EditClient,
    PushRules,
    Device,
    BatchSale,
    BatchImport,
    BatchTransfer,
    BatchPolice,
    BatchEdit,
    AddSub,
    SearchUser,
    BatchEditModel,
    BatchEditServiceTime,
    CarGroupManage,
    DeviceDetail,
    Recharge,
    CopyDialog,
    DeviceServiceRenew,
    // Customers,
    ClientDetail,
    ChangePassword,
    PackageDetails,
    BatchInstruction,
    BathUpGradation,
    BatchTask
  },
  data() {
    return {
      treeType: 'all',
      targetUserId: '',
      collapseTree: true, // 控制用户树折叠
      activeName: 'device', // 表格类型
      currentUser: {}, // 当前目标用户资产信息
      showEditDialog: false, /// 编辑用户信息弹窗
      showPushRule: false, // 推送规则弹窗
      permissionArr: [], // 按钮权限
      tablePermissionArr: [], // 我的客户，设备列表权限
      currentNodeData: {}, // --- 用户树当前选中节点对象 ---
      // 弹窗开关
      meunCurrentCxt: null, // 右键菜单目标用户信息
      showBatchSale: false, // 批量销售弹窗开关
      showBatchImport: false, // 批量导入弹框开关
      addSubVisible: false, // 新增用户弹框开关
      showTransferClient: false, // 转移用户弹窗开关
      transferTarget: {}, // 客户转移目标用户信息
      // 设备搜索参数
      deviceSearchParam: {},
      showBatchTransfer: false, // 设备批量转移弹窗
      showBatchPolice: false, // 批量报警设置弹窗
      showBatchInstruction: false, // 批量指令设置弹窗
      showBatchUpGradation: false, // 批量升级弹窗
      showBatchEdit: false, // 设备批量修改弹窗
      showBatchEditModel: false, // 设备修改型号
      showChangeServiceTime: false, // 设备修改用户到期
      showBatchCharge: false, // 设备批量续费
      showBatchTask: false, // 批量任务设置
      showGroup: false, // 设备分组
      showParentService: false, // 设备专属服务商续费
      showDeviceDetail: false, // 设备详情
      batchEditImei: null, // 设备勾选的imei
      editModelMachineType: null,
      deviceInfo: {}, // 选中的设备信息
      deviceUserId: null, // 选中的设备id
      resetAccount: '', // 重置密码-账号
      resetPwdValue: '', // 重置密码-密码
      expireTime: 0, // 过期时间
      expireType: 1, // 过期类型
      showClientDetail: false, // 客户详情开关
      clientDetailInfo: {}, // 客户列表当前行信息
      detailCommand: '', // 客户详情当前tab
      showChangePassword: false, // 我的客户修改密码弹出开关
      PackageDetailsVisible: false // 套餐详情弹框开关
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    ...mapState({
      permissions: state => state.permissions
    })
  },
  created() {
    this.permissionArr = this.permissions.filter(item => {
      if (item.trans === 'sub_user' || (item.trans === 'Device_List' && item.parentId === 58)) {
        this.tablePermissionArr.push(item.trans)
      }
      return item.type === 2
    })

    // el-tabs切换
    if (this.tablePermissionArr.length === 1 && this.tablePermissionArr.indexOf('sub_user') >= 0 && this.userInfo.userType !== 2) {
      this.activeName = 'client'
    }
    this.permissionArr = this.permissionArr.map(item => item.perms)
  },
  mounted() {
    const { imei, openDialg } = this.$route.query
    if (openDialg == 1) {
      this.batchUpGradation({ selectedDevice: imei ? [{ imei }] : [] })
    }
  },
  methods: {
    batchPoliceCancel(value) {
      this.showBatchPolice = false
      this.$refs.batchPoliceRef.refreshData()
      if (value) {
        this.$refs.Device.advancedSearchDeviceFun()
      }
    },
    batchInstructionCancel(value) {
      this.showBatchInstruction = false
      this.$refs.batchInstructionRef.refreshData()
      if (value) {
        this.$refs.Device.advancedSearchDeviceFun()
      }
    },
    batchUpGradationCancel(value) {
      this.showBatchUpGradation = false
      this.$refs.batchUpGradationRef.refreshData()
      if (value) {
        this.$refs.Device.advancedSearchDeviceFun()
        this.$refs.ClientAssets.refreshAssetsInfo()
      }
    },
    // 用户树触发目标用户更新  在父组件统一更新查询参数
    goSearch(info) {
      let { currentNodeData, currentNodeLevel, expireTime, expireType, targetUserId, treeType } = info

      this.targetUserId = targetUserId
      this.currentNodeData = currentNodeData
      this.treeType = treeType
      this.expireTime = expireTime
      this.expireType = expireType

      // 更新资产信息、设备、下级客户
      this.$nextTick(() => {
        this.$refs.ClientAssets.refreshAssetsInfo()
      })

      if (this.activeName === 'device') {
        this.$refs.Device.refreshDeviceTable({ targetUserId })
      } else if (this.activeName === 'client') {
        this.$refs.ClientTable.reFreshClientTable(targetUserId)
      }
    },
    // 用户树右键菜单点击
    treeMeunClick(info) {
      let actionType = info.actionType
      this.meunCurrentCxt = info.meunCurrentCxt
      switch (actionType) {
        case 'sale':
          this.showBatchSale = true
          this.$nextTick(() => {
            this.$refs.deviceSale.confirmUser(this.meunCurrentCxt)
          })
          break
        case 'import':
          this.showBatchImport = true
          this.$nextTick(() => {
            this.$refs.deviceImport.confirmUser(this.meunCurrentCxt)
          })
          break
        case 'add':
          this.addSubVisible = true
          this.$nextTick(() => {
            this.$refs.addSub.confirmUser(this.meunCurrentCxt)
          })
          break
        case 'tran':
          this.showTransferClient = true
          break
        case 'reset':
          this.confirmResetPsw()
          break
        case 'del':
          this.confirmDeleteClient()
          break
      }
    },
    // 编辑用户信息
    editUserInfo({ currentUser }) {
      this.currentUser = currentUser
      this.showEditDialog = true
    },
    // 打开推送规则
    openPushRule() {
      this.showPushRule = true
    },
    // 关闭修改用户弹框
    closeEditClientDialog(e) {
      this.showEditDialog = false
      if (e.refresh) {
        this.refreshTableAndTree()
      }
    },
    // 编辑用户信息取消回调
    handleEditCancel() {
      this.$refs.editClient.cancel()
    },
    // 确认修改用户信息
    handleEditConfirm() {
      this.$refs.editClient.submitAccount()
    },
    // 表格类型tab切换
    handleTableTabClick() {
      if (this.activeName === 'device' && this.tablePermissionArr.indexOf('Device_List') >= 0) {
        this.$refs.Device.refreshDeviceTable({ targetUserId: this.targetUserId })
      } else if (this.tablePermissionArr.indexOf('sub_user') >= 0 && this.userInfo.userType !== 2) {
        this.$refs.ClientTable.reFreshClientTable(this.targetUserId)
      }
    },
    // 批量销售弹窗关闭回调
    handleSaleClose() {
      this.$refs.deviceSale.resetForm()
    },
    // 批量导入弹框关闭回调
    handleImportClose() {
      this.$refs.deviceImport.resetForm()
    },
    // 新增用户弹窗关闭回调
    closeAccountDialog(e) {
      if (e.close) {
        this.addSubVisible = false
      }
      if (e.refresh) {
        this.refreshTree()
        if (this.activeName == 'client') {
          this.refreshClientTable()
        }
      }
    },
    // 新增用户取消回调
    handleAddCancel() {
      this.$refs.addSub.cancel()
    },
    // 新增用户点击确认按钮
    handleAddConfirm() {
      this.$refs.addSub.submitAccount()
    },
    // 客户转移选中目标用户
    transferSelect(user) {
      this.transferTarget = user
    },
    // 客户转移取消回调
    resetTransferForm() {
      this.transferTarget = {}
      this.showTransferClient = false
      this.$refs.transferUserTree.inputClear()
    },
    // 客户转移确认回调
    confirmTransferClient() {
      let param = {
        userId: this.meunCurrentCxt.userId,
        targetUserId: this.transferTarget.userId
      }
      transferUser(param).then(res => {
        if (res.ret) {
          // this.transferForm = { userId: null, targetUserId: null } // 没用注释掉
          this.$message.success(this.$t('rVAoxxyYFdp_tC9Dnyb6M'))
          this.resetTransferForm()
          this.refreshTree()
        } else {
          switch (res.code) {
            case '-134':
              this.$message.warning(this.$t('9grU7SGwJ1bWmFgjQvuDS', [this.transferTarget.userName]))
              break
            default:
              this.$message.error(res.msg)
          }
        }
      })
    },
    // 重置密码
    confirmResetPsw() {
      this.$confirm(this.$t('igFoXwa2BzYkyYAJDqU9R', [this.meunCurrentCxt.name]), this.$t('lg.resetPsw'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      })
        .then(() => {
          // 生成随机密码
          this.resetPwdValue = pwdReserValue()
          this.resetAccount = this.meunCurrentCxt.userName
          let param = {
            id: this.meunCurrentCxt.userId,
            type: this.meunCurrentCxt.userType,
            password: this.resetPwdValue
          }
          resetUserPsw(param).then(res => {
            if (res.ret === 1) {
              this.$refs.Copy.copyDialog = true
            } else {
              this.$message({
                type: 'error',
                message: this.$t('6czW74k8BArZqrVE40tKG')
              })
            }
          })
        })
        .catch(() => {})
    },
    // 删除用户
    confirmDeleteClient() {
      this.$confirm(this.$t('KK1ozzOjBr5DpFPKLt1C6', [this.meunCurrentCxt.name]), this.$t('lg.limits.Delete_customer'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      })
        .then(() => {
          let param = {
            userId: this.meunCurrentCxt.userId
          }
          deleteClient(param).then(res => {
            if (res.ret === 1) {
              this.$message.success(this.$t('cvyH4iJuWCiLfB_Wsn4Nn'))
              // 若删除的是当前目标用户，则选中根节点，并刷新树
              // 若删除的不是当前目标用户，则选中当前用户，并刷新
              if (this.currentNodeData.userId == this.meunCurrentCxt.userId) {
                this.$refs.clientThree.refreshTreeAndSelectRootNode()
              } else {
                this.$refs.clientThree.refreshTreeAndEmitGoSearch()
              }
            } else {
              let errorCode = res.code.replace('-', '')
              let msg = this.$t('error' + errorCode)
              this.$message({
                type: 'error',
                message: res.msg || msg || this.$t('cUE4imnis7N3pb9QDkywH')
              })
            }
          })
        })
        .catch(() => {})
    },
    // 设备tab表格上方相关操作
    deviceOperate({ type, selectedDevice = [] }) {
      switch (type) {
        case 'sale':
          this.batchSale({ selectedDevice })
          break
        case 'renew':
          this.batchCharge({ selectedDevice })
          break
        case 'charge':
          this.batchCharge({ selectedDevice })
          break
        case 'import':
          this.batchImport({ selectedDevice })
          break
        case 'transfer':
          this.batchTransfer({ selectedDevice })
          break
        case 'editInfo':
          this.batchEditInfo()
          break
        case 'editModel':
          this.batchEditModel({ selectedDevice })
          break
        case 'editServiceTime':
          this.batchEditServiceTime({ selectedDevice })
          break
        case 'delete':
          this.batchDeleteFun({ selectedDevice })
          break
        case 'group':
          this.manageGroup({ selectedDevice })
          break
        case 'police':
          this.batchPolice({ selectedDevice })
          break
        case 'instructions':
          this.batchInstructions({ selectedDevice })
          break
        case 'upGradation':
          this.batchUpGradation({ selectedDevice })
          break
        case 'task':
          this.batchTask({ selectedDevice })
          break
        default:
          break
      }
    },
    // 批量设置任务
    batchTask({ selectedDevice }) {
      this.showBatchTask = true
      this.$nextTick(() => {
        this.$refs.batchTaskRef.addImei(selectedDevice)
      })
    },
    // 批量指令
    batchInstructions({ selectedDevice }) {
      this.showBatchInstruction = true
      this.$nextTick(() => {
        this.$refs.batchInstructionRef.addImei(selectedDevice)
      })
    },
    batchUpGradation({ selectedDevice }) {
      this.showBatchUpGradation = true
      this.$nextTick(() => {
        this.$refs.batchUpGradationRef.addImei(selectedDevice.map(item => item.imei))
      })
    },
    // 批量销售
    batchSale({ selectedDevice }) {
      // if (this.selectedDevice.length === 0) {
      //   this.$message({ message: this.$t('YodpqD_6tNSUO81daApII'), type: 'warning' })
      //   return
      // }
      this.showBatchSale = true
      let imei = selectedDevice.map(item => item.imei)
      this.meunCurrentCxt = null
      this.$nextTick(() => {
        this.$refs.deviceSale.batchSearchImei(imei)
      })
    },
    // 专属服务商续费
    serviceRenew({ serviceData }) {
      this.showParentService = true
      this.$nextTick(() => {
        this.$refs.DeviceServiceRenew.setServiceInfo(serviceData)
      })
    },
    // 批量续费
    batchCharge({ selectedDevice }) {
      // if (this.selectedDevice.length === 0) {
      //   this.$message({ message: this.$t('YodpqD_6tNSUO81daApII'), type: 'warning' })
      //   return
      // }
      this.showBatchCharge = true
      let imei = selectedDevice.map(item => item.imei)
      this.$nextTick(() => {
        this.$refs.deviceRecharge.batchAddImei(imei)
      })
    },
    // 批量导入
    batchImport({ selectedDevice }) {
      this.showBatchImport = true
      let imei = selectedDevice.map(item => item.imei)
      this.$nextTick(() => {
        this.$refs.deviceImport.batchAddImei(imei)
      })
    },
    // 批量转移
    batchTransfer({ selectedDevice }) {
      // if (this.selectedDevice.length === 0) {
      //   this.$message({ message: this.$t('YodpqD_6tNSUO81daApII'), type: 'warning' })
      //   return
      // }
      this.showBatchTransfer = true
      this.$nextTick(() => {
        this.$refs.deviceTransfer.addImei(selectedDevice)
      })
    },
    batchPolice({ selectedDevice }) {
      this.showBatchPolice = true
      this.$nextTick(() => {
        this.$refs.batchPoliceRef.addImei(selectedDevice)
      })
    },
    // 关闭批量转移弹窗
    cancelBatchTransfer() {
      this.showBatchTransfer = false
      this.$refs.deviceTransfer.resetForm()
    },
    // 批量修改
    batchEditInfo() {
      // if (this.selectedDevice.length === 0) {
      //   this.$message({ message: this.$t('YodpqD_6tNSUO81daApII'), type: 'warning' })
      //   return
      // }
      this.$refs.BatchEdit.resetStyle()
      this.showBatchEdit = true
    },
    // 修改型号
    batchEditModel({ selectedDevice }) {
      // 修改型号-不选中也能打开弹窗
      // if (this.selectedDevice.length === 0) {
      //   this.$message({ message: this.$t('YodpqD_6tNSUO81daApII'), type: 'warning' })
      //   return
      // }
      let imei = selectedDevice.map(item => item.imei)
      if (selectedDevice.length > 0) {
        this.editModelMachineType = selectedDevice[0].machineType
      } else {
        this.editModelMachineType = null
      }

      this.batchEditImei = imei.join('\n')
      this.showBatchEditModel = true
    },
    // 修改用户到期
    batchEditServiceTime({ selectedDevice }) {
      // if (this.selectedDevice.length === 0) {
      //   this.$message({ message: this.$t('YodpqD_6tNSUO81daApII'), type: 'warning' })
      //   return
      // }
      this.showChangeServiceTime = true
      let name = this.currentNodeData.name
      if (!name) name = this.userInfo.name
      selectedDevice.forEach(item => (item.userName = name))
      this.$nextTick(() => {
        this.$refs.batchEditServiceTime.addImei(selectedDevice)
      })
    },
    // 点击删除删除设备
    batchDeleteFun({ selectedDevice }) {
      if (selectedDevice.length === 0) {
        this.$message({ message: this.$t('YodpqD_6tNSUO81daApII'), type: 'warning' })
        return
      }
      this.$confirm(this.$t('_QtO05JtDYmcbYWbiYs6y'), '', {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning',
        showClose: false
      })
        .then(() => {
          this.confirmBatchDelete({ selectedDevice })
        })
        .catch(() => {})
    },
    // 删除设备回调
    async confirmBatchDelete({ selectedDevice }) {
      let carIds = selectedDevice.map(item => item.carId)
      let params = { carIds: carIds.join(',') }
      this.$refs.Device.loading = true
      let res = await batchDelete(params)
      try {
        if (res.ret) {
          this.$message.success(this.$t('cvyH4iJuWCiLfB_Wsn4Nn'))
          this.refreshTableAndTree()
          this.$refs.Device.clearSelection()
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.Device.loading = false
      } catch (error) {
        this.$refs.Device.loading = false
      }
    },
    //  分组弹出
    manageGroup({ selectedDevice }) {
      // let node = this.$refs.clientThree.getCurrentNode()
      // this.$refs.GroupManage.getCarGroupFun(node.userId) // 获取当前选中用户分组
      this.showGroup = true
      let imeiArr = selectedDevice.map(item => item.imei)
      if (imeiArr.length) {
        this.$nextTick(() => {
          this.$refs.carGroupManage.setIMEI(imeiArr)
        })
      }
    },
    //  刷新分组
    refreshGroup() {
      // let node = this.$refs.clientThree.getCurrentNode()
      this.getCarGroupFun(this.currentNodeData.userId)
      this.showGroup = false
    },
    // 批量转移分组，刷新设备表格
    carGroupTranRefresh() {
      this.$refs.Device.advancedSearchDeviceFun()
    },
    // 获取表格 分组列数据
    async getCarGroupFun(id) {
      let res = await getCarGroup({ userId: id })
      if (res.ret) {
        this.carGroup = res.data
        this.carGroup.forEach((item, i) => {
          if (item.carGroupId === 0) {
            this.carGroup[i].name = this.$t('Y9r-CsONpxxRmg00jzjLm')
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设备表格状态筛选
    filterChange(filterObject) {
      this.$refs.Device.setStatusAndFilter(filterObject)
    },
    // 打开设备详情
    openDeviceDetail({ activeName, deviceInfo, deviceUserId }) {
      console.log('activeName', activeName)
      this.showDeviceDetail = true
      this.deviceInfo = deviceInfo
      this.deviceUserId = deviceUserId
      if (this.$refs.deviceDetailHandle) {
        this.$refs.deviceDetailHandle.activeName = activeName
      }
    },
    // 关闭批量续费
    closeRecharge() {
      this.$refs.deviceRecharge.resetForm()
    },
    // 打开复制弹窗
    openCopyDiaolg({ resetAccount, resetPwdValue }) {
      this.resetAccount = resetAccount
      this.resetPwdValue = resetPwdValue
      this.$refs.Copy.copyDialog = true
    },
    // 客户详情弹框查看某个用户
    showCertainUser(userId) {
      this.$refs.clientThree.handleSelect({ userId: userId })
    },
    // 我的客户表格更多操作
    handleClientTableMoreOporateClick(info) {
      let actionType = info.actionType
      this.clientDetailInfo = info.row
      switch (actionType) {
        case 'transfer':
          this.showClientDetail = true
          this.detailCommand = 'transfer'
          this.$refs.clientDetailHandle.activeName = 'transfer'
          break
        case 'detail':
          this.showClientDetail = true
          this.detailCommand = 'detail'
          this.$refs.clientDetailHandle.activeName = 'detail'
          break
        case 'reset':
          this.showChangePassword = true
          break
      }
    },
    // 刷新树和表格(全局刷新、刷新用户树、刷新上方资源、根据当前激活的tab刷新设备表还是客户表)
    refreshTableAndTree(type) {
      // type 传 'device' 或 'client'
      if (type) {
        // 有传入指定刷新某种类型（只刷新某个表格）
        if (this.activeName == type && type == 'device') {
          // 刷新设备表格
          this.refreshDeviceTable()
        } else if (this.activeName == type && type == 'client') {
          // 刷新客户表格
          this.refreshClientTable()
        }
      } else {
        // 没有指定刷新某种类型 （两个表格都需要刷新）
        if (this.activeName == 'device') {
          // 刷新设备表格
          this.refreshDeviceTable()
        } else if (this.activeName == 'client') {
          // 刷新客户表格
          this.refreshClientTable()
        }
      }
      // 刷新树
      this.refreshTree()
      // 刷新上方资源
      this.$refs.ClientAssets && this.$refs.ClientAssets.refreshAssetsInfo()
    },
    // 只刷新设备表格
    refreshDeviceTable() {
      if (this.activeName == 'device' && this.$refs.Device) {
        // 保留状态刷新
        this.$refs.Device.advancedSearchDeviceFun()
      }
    },
    // 只刷新树
    refreshTree() {
      this.$refs.clientThree && this.$refs.clientThree.refreshTree()
    },
    // 只刷新客户表格
    refreshClientTable() {
      if (this.activeName == 'client' && this.$refs.ClientTable) {
        this.$refs.ClientTable.getTableData()
      }
    },
    // 打开套餐详情
    openPackageDetail(info) {
      this.deviceInfo = info
      this.PackageDetailsVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  height: calc(100vh - 60px);
  padding: 16px;
  box-sizing: border-box;
  overflow: auto;

  // 内容主体
  .content {
    height: 100%;
    overflow: hidden;
    @include flex-row(center, stretch);
    border-radius: 6px;
  }
  .expand_arrow_left {
    position: absolute;
    width: 32px;
    height: 28px;
    top: 71px;
    left: 304px;
    z-index: 2;
    cursor: pointer;
    padding: 3px 8px 3px 5px;
    border: 1px solid $border;
    border-right: none;
    border-radius: $inputRadius 0 0 $inputRadius;
    flex-shrink: 0;
    box-sizing: border-box;
    background-color: #fff;
    &.fold {
      left: 16px;
      border-left: none;
      border-right: 1px solid $border;
      border-radius: 0 $inputRadius $inputRadius 0;
      img {
        transform: rotate(180deg);
      }
    }
    img {
      width: 18px;
      height: 18px;
    }
  }
  .right {
    height: 100%;
    flex: 1;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: 6px;
    .expand_arrow_right {
      cursor: pointer;
      position: absolute;
      margin-top: 55px;
      left: 0;
      // font-size: 23px;
      z-index: 99;
      transform: rotate(180deg); // 旋转180度
    }
    .client-table_content {
      position: relative;
      flex: 1;
      height: 0;
      background: #ffffff;
      border-radius: $containerRadius;
      // 新加样式
      display: flex;
      flex-direction: column;
      .devlice-client-tab {
        height: 100%;
        ::v-deep .el-tabs__nav-wrap {
          .el-tabs__nav {
            height: 54px;
          }
          .el-tabs__nav-scroll {
            padding-left: 20px;
          }
        }
        ::v-deep .el-tabs__content {
          height: calc(100% - 68px);
          max-width: 100%;
          // padding: 0 20px 16px;
          overflow-x: auto;
          box-sizing: border-box;
        }
        ::v-deep .el-tab-pane {
          height: 100%;
        }
        ::v-deep .el-tabs__item {
          font-weight: 600;
          font-size: 16px;
          line-height: 54px;
        }
      }
    }
  }
}
.page-line {
  width: 12px;
  background-color: $ContainerBg;
}
</style>
<style lang="scss">
.base-dialog__wrapper .device-recharge {
  padding: 0;
}
</style>
