/**
 * 翻译出错时 需要再次执行脚本
 */

const axios = require('axios')
const CryptoJS = require('crypto-js')
const zhCn = require('../../src/i18n/langs/cn.json')
const fs = require('fs')
const path = require('path')

const appKey = '455ba052db4af225' // 有道个人 id
const key = 'iymcPaVVc9A0uWz0IMpWZrWU8CQuP71o' // 有道个人 appSecretKey
// 主要翻译的json文件映射表    key: JSON名 value: 有道翻译语言简称
// https://ai.youdao.com/DOCSIRMA/html/trans/api/wbfy/index.html
const translateMap = {
  en: 'en',
  ar: 'ar',
  bg: 'bn',
  de: 'de',
  es: 'es',
  fa: 'fa',
  fr: 'fr',
  he: 'he',
  id: 'id',
  it: 'it',
  pt: 'pt',
  ru: 'ru',
  tw: 'zh-CHT',
  vi: 'vi',
  ka: 'ka',
  nl: 'nl',
  tr: 'tr',
  pl: 'pl',
  ro: 'ro',
  sq: 'sq'
}

class Queue {
  constructor(config) {
    this.queues = []
    this.limit = config.limit || 5 // 一次可执行的任务
    this.doingTaskLength = 0 // 正在进行的任务个数
  }

  addTask(fn) {
    this.queues.push(fn)
  }

  removeTask() {
    return this.queues.shift()
  }

  startQueue() {
    if (this.doingTaskLength > this.limit) {
      return
    }
    if (this.queues.length) {
      const fn = this.removeTask()
      this.doingTaskLength = this.doingTaskLength + 1
      fn().then(() => {
        this.doingTaskLength = this.doingTaskLength - 1
        this.startQueue()
      })
    }
  }
}

/**
 * 对比两对象之间的差异
 * @param obj1
 * @param obj2
 * @returns {{}}
 */
function findMissingAttributes(obj1, obj2) {
  const missingAttributes = {}
  for (const key in obj1) {
    if (Object.prototype.hasOwnProperty.call(obj1, key) && !Object.prototype.hasOwnProperty.call(obj2, key)) {
      missingAttributes[key] = obj1[key]
    }
  }
  return missingAttributes
}

/**
 * 整合多文本
 * @param record
 * @returns {string}
 */
function getTranslateLang(record) {
  let langs = ''
  Object.keys(record).forEach(key => (langs += record[key] + '\n'))
  return langs
}

/**
 * 有道翻译所需要的文本
 * @param q
 * @returns {*}
 */
function truncate(q) {
  const len = q.length
  if (len <= 20) return q
  return q.substring(0, 10) + len + q.substring(len - 10, len)
}

/**
 * 调用有道翻译接口
 * @param to
 * @param json
 * @returns {Promise<unknown>}
 */
function startTranslate(to, json) {
  return function() {
    return new Promise((resolve, reject) => {
      const lang = require(`../../src/i18n/langs/${json}.json`)
      const translateLangMap = findMissingAttributes(zhCn, lang)
      if (!Object.keys(translateLangMap).length) {
        console.log(`${json}.JSON 无需翻译，已经是最新的`)
        resolve()
        return
      }
      const salt = new Date().getTime()
      const curtime = Math.round(new Date().getTime() / 1000)
      const query = getTranslateLang(translateLangMap)

      // 检查翻译文本长度
      if (query.length > 5000) {
        console.error(`${json}.JSON 翻译文本过长：${query.length} 字符，超过API限制`)
        reject(new Error('翻译文本过长'))
        return
      }

      const from = 'zh-CHS'
      const str1 = appKey + truncate(query) + salt + curtime + key
      const sign = CryptoJS.SHA256(str1).toString(CryptoJS.enc.Hex)

      console.log(`开始翻译 ${json}.JSON，目标语言：${to}`)

      axios
        .get('https://openapi.youdao.com/api', {
          params: {
            q: query,
            appKey: appKey,
            salt: salt,
            from: from,
            to: to,
            sign: sign,
            signType: 'v3',
            curtime: curtime
          }
        })
        .then(res => {
          if (!res.data || !res.data.translation) {
            console.error(`${json}.JSON 翻译返回数据异常:`, res.data)
            reject(new Error('翻译返回数据异常'))
            return
          }

          const langs = res.data.translation.toString().split('\n')
          Object.keys(translateLangMap).forEach((key, index) => {
            translateLangMap[key] = langs[index]
          })

          const jsonData = JSON.stringify(Object.assign(lang, translateLangMap), null, 2)
          fs.writeFile(path.resolve(__dirname, `../../src/i18n/langs/${json}.json`), jsonData, err => {
            if (err) {
              console.error(`${json}.JSON 写入文件出错：`, err)
              reject(err)
              return
            }
            console.log(`${json}.JSON 文件已成功保存`)
            resolve()
          })
        })
        .catch(error => {
          console.error(`${json}.JSON 翻译出错：`, error.message || error)
          reject(error)
        })
    })
  }
}

/**
 * 开始翻译
 * @returns {Promise<void>}
 */
async function startSearchTranslateKey() {
  const queue = new Queue({ limit: 1 })

  try {
    Object.keys(translateMap).forEach(key => {
      queue.addTask(startTranslate(translateMap[key], key))
    })
    queue.startQueue()
  } catch (error) {
    console.error('翻译过程发生错误：', error)
  }
}

startSearchTranslateKey()
