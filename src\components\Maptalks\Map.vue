<template>
  <div class="map-container" ref="MapContainer">
    <img src="@/assets/img/monitor/google_logo.png" class="google_logo" v-if="this.mapType === 2" />
    <div id="mapPanel"></div>
    <div v-show="trackPageScreenFull" id="mapBarControl" :class="{ showTxt: lang === 'cn' }">
      <div v-if="pageType !== 'monitor' && !isVirtualUser" class="barItem" :title="lang === 'cn' ? '' : $t('IJW7kt6UjfglE5oKPiJ0z')" @click="handleStreetView">
        <PcMapStreet size="23" color="#262626" class="icon-svg-map" />
        <div class="txt">{{ $t('IJW7kt6UjfglE5oKPiJ0z') }}</div>
      </div>
      <div class="barItem type" :title="lang === 'cn' ? '' : $t('E2cJxJ0qcSBiNzRMKbaj0')">
        <!-- <div class="icon icon_map"></div> -->
        <div class="map-icon-container" @click="showMapTypeMenu" @mouseenter="showMapTypeMenu">
          <PcMap size="23" color="#262626" class="icon-svg-map" />
          <div class="txt">{{ $t('E2cJxJ0qcSBiNzRMKbaj0') }}</div>
        </div>
        <div class="mapTypeMenu" :class="judgeMapTypeMenuDisplay === true ? 'show-type-menu' : 'hide-type-menu'">
          <div class="menu">
            <div class="mapitem" :class="{ active: item.selected }" v-for="(item, index) in mapTypes" :key="item.type" @click="chooseMapItem(index)">
              <span>{{ item.text }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="barItem" @click="switchMapSatellite('satellite')" :title="lang === 'cn' ? '' : $t('IKjGxwah7gczqSoJQWeJJ')">
        <PcMapSatellite size="20" />
        <div class="txt">{{ $t('IKjGxwah7gczqSoJQWeJJ') }}</div>
      </div>
      <div
        v-if="(pageType === 'monitor' || pageType === 'history') && !isVirtualUser"
        v-has="{ type: 2, perms: 'fence:list,fence:info' }"
        class="barItem"
        :title="lang === 'cn' ? '' : $t('nuMj_JYBMK5JmtZA19Q7D')"
        @click="switchShowFence()"
      >
        <PcMapFence size="20" />
        <div class="txt">{{ $t('nuMj_JYBMK5JmtZA19Q7D') }}</div>
      </div>
      <!--      线路管理-->
      <el-popover
        placement="left"
        trigger="hover"
        class="barItem"
        v-if="barData[3].show && pageType === 'monitor' && !isVirtualUser"
        popper-class="tools_popper"
      >
        <div class="tools_box">
          <div @click="changeManageLineModal">{{ $t('wegasqASDE32446sgwe07') }}</div>
          <div @click="toManageLine(1, 1)">{{ $t('wegasqASDE32446sgwe13') }}</div>
          <div @click="toManageLine(1, 2)">{{ $t('wegasqASDE32446sgwe14') }}</div>
          <div v-if="mapType === 1" @click="toManageLine(1, 3)">{{ $t('wegasqASDE32446sgwe15') }}</div>
        </div>
        <div
          slot="reference"
          v-if="barData[3].show && pageType == 'monitor'"
          :title="lang === 'cn' ? '' : $t('wegasqASDE32446sgwe01')"
          class="icon_tools_box"
          @click="changeManageLineModal"
        >
          <PcMapLine size="20" />
          <div class="txt">{{ $t('wegasqASDE32446sgwe01') }}</div>
        </div>
      </el-popover>
      <!--      兴趣点POI-->
      <div
        v-has="{ type: 2, perms: 'place:group:list' }"
        class="barItem"
        :title="lang === 'cn' ? '' : $t('n26EViLKtRWW73EXNwoIe')"
        v-if="barData[4].show && (pageType === 'monitor' || pageType === 'history') && !isVirtualUser"
        @click="showPOIPanel()"
      >
        <PcMapInterest size="24" />
        <div class="txt">{{ $t('n26EViLKtRWW73EXNwoIe') }}</div>
      </div>
      <el-popover
        :append-to-body="!isFullScreen"
        placement="left"
        trigger="hover"
        class="barItem"
        v-if="barData[5].show && pageType !== 'manageline' && !isVirtualUser"
        popper-class="tools_popper"
      >
        <div class="tools_box">
          <!-- 监控页面才有画框查车和区域查车，其他页面没做 -->
          <div v-if="pageType == 'monitor'" @click="searchCarNumber">{{ $t('wegasqASDE32446sgwe03') }}</div>
          <div v-if="isShowRegional && pageType == 'monitor'" @click="showRegionalDialog">{{ $t('wegasqASDE32446sgwe04') }}</div>
          <div @click="showTrafficLayer">{{ $t('eq8ubUOnx-iIN5d8flNTZ') }}</div>
          <div @click="openRulerTool">{{ $t('p5s_fNJw6Bfo4E59dkz8t') }}</div>
        </div>
        <div slot="reference">
          <!--      工具-->
          <div slot="reference" v-if="barData[5].show" :title="lang === 'cn' ? '' : $t('wegasqASDE32446sgwe02')" class="icon_tools_box">
            <!-- <div class="icon icon_tools" :class="{ active: showTools }"></div> -->
            <PcTool size="23" color="#262626" class="icon-svg-tool" :class="{ active: showTools }" />
            <div class="txt">{{ $t('wegasqASDE32446sgwe02') }}</div>
          </div>
        </div>
      </el-popover>
      <div
        class="barItem"
        @click="openMoreBarControl"
        v-if="pageType !== 'manageline' && !isVirtualUser"
        :title="lang === 'cn' ? '' : $t('IikV2EDWehRNB_DuPyoAi')"
      >
        <PcMoreTool size="23" color="#262626" class="icon-svg-satellite" :class="{ active: nowShowSatellite }" />
        <div class="txt">{{ showMoreTool ? $t('qsvd2Se3tlv5Dsb9Hya01') : $t('IikV2EDWehRNB_DuPyoAi') }}</div>
      </div>
      <!-- <div class="barItem lastchild" @click="openMoreBarControl" v-if="pageType !== 'manageline'">
        <div class="icon icon_drown" :class="{ rolate: barFlagOpen }"></div>
      </div> -->
    </div>
    <div v-show="trackPageScreenFull" id="mapSearchControl" @click="showSearchFun" :class="{ isLineSettingMode: isLineSettingMode }">
      <PcMapSearch size="20" />
    </div>
    <div id="mapSearchInput" v-if="showSearch" :class="{ isLineSettingMode: isLineSettingMode }">
      <input id="searchInputBaidu" v-if="mapType === 1" :placeholder="$t('YmEYZtN_qZQbAvfoi-EnT')" />
      <input id="searchInputGoogle" v-else :placeholder="$t('YmEYZtN_qZQbAvfoi-EnT')" />
      <div v-if="!isVirtualUser" :class="[{ isLineSettingMode: isLineSettingMode }, 'btn']" :title="$t('wegasqASDE32446sgwe05')">
        <img src="@/assets/img/monitor/location.png" class="nav_img" @click="toManageLine(1, 3)" />
      </div>
      <div class="btn">
        <img src="@/assets/img/search.png" @click="searchAddress" />
      </div>
    </div>
    <div v-show="trackPageScreenFull" id="mapZoomControl">
      <div class="addZoom" @click="enlargeZoom">
        <PcMapAmplify size="20" />
      </div>
      <div class="descZoom" @click="reduceZoom">
        <PcMapReduce size="20" />
      </div>
    </div>
    <!--     智能机器人-->
    <!-- <div id="mapRobotControl" :class="{ 'mapRobotControl--move': barFlagOpen, showTxt: lang === 'cn' }">
      <div v-if="pageType == 'monitor' && isSwd && domainUrl !== 'gpsgy'" class="flex column center justify-center cursor-point" @click="showRobot">
        <PcMapCustomer size="20" />
        <span class="ljdw-mt-5 desc-text">客服</span>
      </div>
      <div
        v-if="pageType == 'monitor'"
        @click="showMoreAlarm"
        v-has="{ type: 2, perms: 'monitor:alarm:list' }"
        class="flex column center justify-center cursor-point ljdw-mt-16"
      >
        <PcMapNotice size="24" />
        <span class="ljdw-mt-5 desc-text">信息</span>
      </div>
    </div> -->
    <el-dialog :title="$t('lg.limits.Share_track')" custom-class="swd-dialog" :visible.sync="shareHistoryFlag" :close-on-click-modal="false" width="480px">
      <share-track :shareData="shareData" v-if="shareHistoryFlag" @success="shareUrlSuccess" @close="shareHistoryFlag = $event"></share-track>
    </el-dialog>
    <!-- 分享轨迹结果 -->
    <el-dialog
      :title="$t('T2Sz03hEpYKf8z_mXsC9e')"
      custom-class="swd-dialog"
      :visible.sync="shareHistoryResultFlag"
      :close-on-click-modal="false"
      width="488px"
    >
      <share-track-result v-if="shareHistoryResultFlag" :shareUrl="shareHistoryUrl"></share-track-result>
    </el-dialog>
    <div v-show="showFence" id="fenceControl" :class="{ fixed: isFixed }">
      <div class="fence-header">
        {{ fenceTitle }}
        <div class="close" @click="switchShowFence"><img src="@/assets/img/close.png" /></div>
      </div>
      <div class="fence-content" @mousedown.stop="" @mousemove.stop="">
        <div class="fence-btn">
          <span class="btn-label" @click="showDrawSteps">
            <el-tooltip popper-class="swd-tooltip-dark" :content="$t('n14RoDOGBsNntIwetoWMq')" placement="top"
              ><img class="info" src="@/assets/img/monitor/ques.png"
            /></el-tooltip>
            {{ $t('sxg1Ago7Wgz102blS1Uw0') }}：</span
          >
          <el-tooltip popper-class="swd-tooltip-dark" :content="$t('sgH0wIb6CRMhrPErfzZNo')" placement="top">
            <span class="btn-f" @click="drawFence('Circle')"><img class="circle" src="@/assets/img/monitor/circle_i.png"/></span>
          </el-tooltip>
          <el-tooltip popper-class="swd-tooltip-dark" :content="$t('YRsd0eFEx-XpEoHM4bQOf')" placement="top">
            <span class="btn-f" @click="drawFence('Polygon')"><img class="polygon" src="@/assets/img/monitor/polygon_i.png"/></span>
          </el-tooltip>
          <el-tooltip v-if="lang == 'cn'" popper-class="swd-tooltip-dark" :content="$t('xhq87s8s1ds1hdh1dhddh')" placement="top">
            <span class="btn-f" @click="showAreaDialog()"><img class="area" src="@/assets/img/monitor/area_i.png"/></span>
          </el-tooltip>
        </div>
        <div class="fence-search">
          <el-input v-model="allFenceQuery.name" :placeholder="$t('vMpgu5wFuK5er9Dlkz9fF')" @keyup.enter.native="searchFence">
            <span slot="suffix" class="fence-s" @click="searchFence">
              <img src="@/assets/img/search.png" />
            </span>
          </el-input>
        </div>
        <div class="fence-tabs">
          <div class="btn" @click="showAllFenceBtn">{{ $t('lg.showAll') }}</div>
          <div v-has="{ type: 2, perms: 'fence:delete' }" class="btn" @click="batchDelFence">{{ $t('lg.limits.batch_deletion') }}</div>
        </div>
        <div class="fence-table">
          <div class="table-height">
            <el-table
              height="400"
              :highlight-current-row="true"
              :data="allFenceData"
              style="width: 100%"
              :header-cell-class-name="tableHeaderClassName"
              :cell-class-name="tableRowClassName"
              @row-click="fencTableClickRow"
              @selection-change="handleSelectFenceChange"
            >
              <el-table-column type="selection" width="42"> </el-table-column>
              <el-table-column prop="name" :label="$t('KQBFZQ0Voyfhg6fQXT_Rn')">
                <template slot-scope="scope">
                  <div class="col-hidden">
                    <img class="circle" v-if="scope.row.type == 0" src="@/assets/img/monitor/circle.png" />
                    <img class="polygon" v-else-if="scope.row.type == 1" src="@/assets/img/monitor/polygon.png" />
                    <img class="area" v-else-if="scope.row.type == 2" src="@/assets/img/monitor/area.png" />
                    <el-tooltip popper-class="swd-tooltip-dark" :content="scope.row.name + '（' + scope.row.carNum + '）'" placement="top">
                      <span>{{ scope.row.name }}（{{ scope.row.carNum }}）</span>
                    </el-tooltip>
                    <svg-icon
                      icon-class="out_fence"
                      class="out_fence"
                      v-show="scope.row.isSupportControlPoil == 1 || scope.row.isSupportControlPoil == 2"
                    ></svg-icon>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="carFenceId" :label="$t('lg.operator')" align="right" width="80px">
                <template slot-scope="scope">
                  <div class="operate">
                    <el-tooltip v-has="{ type: 2, perms: 'fence:add' }" popper-class="swd-tooltip-dark" :content="$t('lg.edit')" placement="top">
                      <!-- <img class="opera-look" @click.stop="editRowFence(scope.row)" src="@/assets/img/monitor/look.png" /> -->
                      <svg-icon @click.stop="editRowFence(scope.row)" icon-class="modify" class-name="opera-look"></svg-icon>
                    </el-tooltip>
                    <el-tooltip
                      v-if="scope.row.boundCar"
                      v-has="{ type: 2, perms: 'fence:bind' }"
                      popper-class="swd-tooltip-dark"
                      :content="$t('a2soi5XT71DCr2H3iW75d')"
                      placement="top"
                    >
                      <img class="opera-bind" @click.stop="bindFence(scope.row)" src="@/assets/img/monitor/binded.png" />
                    </el-tooltip>
                    <el-tooltip v-else popper-class="swd-tooltip-dark" :content="$t('FMD-CUGF0TJ-UFTwcQV2j')" placement="top">
                      <img class="opera-bind" @click.stop="bindFence(scope.row)" src="@/assets/img/monitor/edit.png" />
                    </el-tooltip>
                    <el-tooltip v-has="{ type: 2, perms: 'fence:delete' }" popper-class="swd-tooltip-dark" :content="$t('lg.delete')" placement="top">
                      <img class="opera-del" @click.stop="deleteFence(scope.row)" src="@/assets/img/monitor/delete.png" />
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <base-pagination
            class="pagination monitor-pagina"
            @size-change="allFenceSizeChange"
            @current-change="allFenceCurrentChange"
            :current-page="allFenceQuery.pageNO"
            :page-size="allFenceQuery.rowCount"
            :page-sizes="[15, 20, 30]"
            :pager-count="pagerCount"
            layout="prev, jumper, next, total"
            :total="allFenceTotal"
          >
          </base-pagination>
        </div>
      </div>
    </div>
    <!-- 兴趣点列表 -->
    <div id="poiControl" class="poiModal" v-if="showPOIFlag">
      <div class="poiheader">
        <span>POI</span>
        <div class="close" @click="hidePOIPanel"><img src="@/assets/img/close.png" /></div>
      </div>
      <div class="poibody">
        <div class="poisearch">
          <el-input v-model="poiparam" :placeholder="$t('LtNPazzpjZ8Ki6SHzTfs6')">
            <span slot="suffix" class="poi-s" @click="searchPOIByName"><img src="@/assets/img/search.png"/></span>
          </el-input>
        </div>
        <div class="operate">
          <div class="btnrow">
            <span v-show="showPOIEditButton" v-has="{ type: 2, perms: 'place:group:add' }" class="btn one" @click="initPlacePoint"
              ><img src="@/assets/img/add_icon1.png" />POI</span
            >
            <span v-show="showPOIEditButton" v-has="{ type: 2, perms: 'place:group:add' }" class="btn more" @click="showAddPOIModal(3)">
              <img src="@/assets/img/add_icon2.png" />{{ $t('mnCksVnc2zGhj-VRBgGPw') }}
            </span>
          </div>
          <div>
            <el-checkbox v-model="varshowpoi" @change="switchChangePOI($event)">{{ $t('lg.showAll') }}（{{ poiCount }}）</el-checkbox>
          </div>
        </div>
        <div class="groups">
          <overlay-scrollbars ref="poiComponentRef" :options="groupOptions" style="height:100%">
            <div class="groupItem" v-for="(item, index) in poiList" :key="item.placeGroupId" :class="{ open: item.open }">
              <div class="groupName" @click="openPoiGroup(index)">
                <span>{{ item.placeGroupId === 0 ? $t('Y9r-CsONpxxRmg00jzjLm') : item.name }}</span>
                <div class="item-opera">
                  <el-tooltip v-if="item.placeGroupId" popper-class="swd-tooltip-dark" :content="$t('lg.edit')" placement="top">
                    <svg-icon
                      v-has="{ type: 2, perms: 'place:group:update' }"
                      @click.stop="editPOIRowClick(item, index)"
                      icon-class="modify"
                      class-name="modify"
                    ></svg-icon>
                  </el-tooltip>
                  <el-tooltip v-if="item.placeGroupId" popper-class="swd-tooltip-dark" :content="$t('lg.delete')" placement="top">
                    <svg-icon
                      v-has="{ type: 2, perms: 'place:group:delete' }"
                      @click.stop="deletePOIGroup(item, index)"
                      v-if="item.placeGroupId"
                      icon-class="delete"
                      class-name="delete"
                    ></svg-icon>
                  </el-tooltip>
                  <div class="drown" :class="{ open: item.open }"><img src="@/assets/img/drown_icon.png" /></div>
                </div>
              </div>
              <div v-if="item.open">
                <div class="subItem" v-for="(sub, inx) in item.places" :key="sub.placeId" @click="selectItemPOI(sub)">
                  <span class="poiname">{{ sub.name }}</span>
                  <div class="item-opera">
                    <el-tooltip popper-class="swd-tooltip-dark" :content="$t('lg.edit')" placement="top">
                      <svg-icon
                        v-has="{ type: 2, perms: 'place:group:update' }"
                        @click.stop="editPOISubRowClick(sub, index, inx)"
                        icon-class="modify"
                        class-name="modify"
                      ></svg-icon>
                    </el-tooltip>
                    <el-tooltip popper-class="swd-tooltip-dark" :content="$t('lg.delete')" placement="top">
                      <svg-icon
                        v-has="{ type: 2, perms: 'place:group:delete' }"
                        @click.stop="deletePOIItem(sub, index, inx)"
                        icon-class="delete"
                        class-name="delete"
                      ></svg-icon>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </overlay-scrollbars>
        </div>
      </div>
      <div class="poibottom">
        <div v-show="showPOIEditButton" class="poiaddgroup" @click="showAddPOIModal(1)">
          <img src="@/assets/img/add_icon1.png" />{{ $t('3bKE4RZjKEEnP-6Lvd3QO') }}
        </div>
      </div>
    </div>
    <!-- 兴趣点添加编辑 -->
    <el-dialog
      title=""
      custom-class="poiModal"
      :visible="showAddOrEditPOI"
      :close-on-click-modal="false"
      :width="poiAddOrEditData.type == 3 ? '514px' : '387px'"
    >
      <div class="poiheader">
        <span>{{ poiAddOrEditData.title }}</span>
        <div class="close" @click="hideAddPOIModal()"></div>
      </div>
      <div class="poicenter">
        <div v-if="poiAddOrEditData.type == 1 || poiAddOrEditData.type == 2">
          <div class="row">
            <div class="label">{{ $t('KQBFZQ0Voyfhg6fQXT_Rn') }}：</div>
            <div class="content"><el-input v-model="poiAddOrEditData.name" placeholder=""></el-input></div>
          </div>
          <div class="row" v-if="poiAddOrEditData.type == 2 && poiAddOrEditData.flag == 'add'">
            <div class="label">{{ $t('g0Z4WYTOSoNVw6icvU8Xa') }}：</div>
            <div class="content iconType">
              <div
                class="iconImg"
                :class="{ active: poiAddOrEditData.iconType === index + 1 }"
                v-for="(item, index) in poiIconTypeImgs"
                :key="item"
                @click="selectIconType(index)"
              >
                <img :src="item" />
              </div>
            </div>
          </div>
          <div class="row" v-if="poiAddOrEditData.type == 2">
            <div class="label">{{ $t('3bKE4RZjKEEnP-6Lvd3QO') }}：</div>
            <div class="content">
              <el-select v-model="poiAddOrEditData.placeGroupId" :placeholder="$t('lg.pleaseChoose')">
                <el-option v-for="item in poiList" :key="item.placeGroupId" :label="item.name" :value="item.placeGroupId"> </el-option>
              </el-select>
            </div>
          </div>
          <div class="row">
            <div class="label">{{ $t('lg.remark') }}：</div>
            <div class="content">
              <el-input type="textarea" :rows="4" v-model="poiAddOrEditData.remark" placeholder=""></el-input>
            </div>
          </div>
        </div>
        <div v-if="poiAddOrEditData.type == 3">
          <div class="upload_row">
            <div class="upWrap">
              <label class="upBtn" for="poi-upload-file">
                <img src="@/assets/img/upload_1.png" />
                {{ $t('a3JOJwEQkkXHUNJ9B8gzq') }}
              </label>
              <span class="filename" v-if="poiUploadFileUrl">{{ poiUploadFileUrl }}</span>
              <el-input @change="upLoadFileChange($event)" id="poi-upload-file" type="file" v-model="inputFile"></el-input>
            </div>
            <a class="downBtn" href="https://s.gpsnow.net/lib/file/poi_batchAdd.xls">{{ $t('oNh27yZ5e5LDkyOhuFrCO') }}<img src="@/assets/img/upload_2.png"/></a>
          </div>
          <div class="uploadtip">{{ $t('YRfNHk3LACKCdKZKpdGFt') }}</div>
          <div class="infotip">
            <div class="u_row">{{ $t('993p62YTFUY0OgmGiplEj') }}</div>
            <div class="u_row">
              {{ $t('SXm03GuPs_W8_bHDUIiOO') }}（1-<img :src="poiIconTypeImgs[0]" />、2-<img :src="poiIconTypeImgs[1]" />、3-<img
                :src="poiIconTypeImgs[2]"
              />、4-<img :src="poiIconTypeImgs[3]" />
              ）
            </div>
            <div class="u_row">{{ $t('WdUDofXEM4vPVut2ggyll') }}</div>
            <div class="u_row">{{ $t('ihJPRdZfKZWbwqQg3TC6p') }}</div>
            <div class="u_row">
              {{ $t('GPiUGvJyTduYFCJS0VF_W') }}
            </div>
            <div class="u_row">{{ $t('K98R9Kl6t3eFtWbQBdzh-') }}</div>
          </div>
        </div>
      </div>
      <div class="poibottom">
        <div class="cancel" @click="hideAddPOIModal()">{{ $t('lg.cancel') }}</div>
        <div v-if="poiAddOrEditData.type != 3" class="poiaddgroup" @click="addPostPOI">
          {{ poiAddOrEditData.flag == 'edit' ? $t('lg.confirm') : $t('lg.limits.GeoKey_Add') }}
        </div>
        <div v-if="poiAddOrEditData.type == 3" class="poiaddgroup" @click="submitUploadPOI">{{ $t('lg.confirm') }}</div>
      </div>
    </el-dialog>
    <!-- 绘制围栏提示 -->
    <el-dialog title="" custom-class="drawTipModal" :visible="showDrawFlag" :close-on-click-modal="false" width="306px">
      <div class="drawheader">
        <span>{{ $t('SUBLSbsZXN6Yu-QsHnH5_') }}</span>
        <div class="close" @click="showDrawFlag = false"></div>
      </div>
      <div class="drawbody">
        <div class="drawItem">
          <div class="drawName">{{ $t('AmtYv4ISEbwIRe2OC3KP3') }}</div>
          <div class="drawSteps">
            <div class="step"><img src="@/assets/img/monitor/dot.png" />{{ $t('hu_Qd-LVVX--ytlHSCPNI') }}</div>
            <div class="step"><img src="@/assets/img/monitor/dot.png" />{{ $t('AmLlcGde2A2OHyFdaqiJa') }}</div>
            <div class="step"><img src="@/assets/img/monitor/dot.png" />{{ $t('qAs2oKd_k5MsKqmoor8Py') }}</div>
          </div>
        </div>
        <div class="drawItem">
          <div class="drawName">{{ $t('X7_1ZjxC6jNbqqwtSdZ77') }}</div>
          <div class="drawSteps">
            <div class="step"><img src="@/assets/img/monitor/dot.png" />{{ $t('B8Vp2heRHQQFC19TLbWtu') }}</div>
            <div class="step"><img src="@/assets/img/monitor/dot.png" />{{ $t('7ptVgNAs_gId9fabwwyXY') }}</div>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 设备过期提示的弹窗 -->
    <el-dialog title="" custom-class="parent-service-modal" :visible.sync="showParentService" :close-on-click-modal="false" width="410px" top="35vh">
      <div class="service-content">
        <div class="content-top">
          <div class="left">
            <img :src="serviceData.imageURL" class="user-avatar" />
          </div>
          <div class="right">
            <div class="row">
              <div class="label">{{ $t('lg.serviceProvide') }}：</div>
              <div class="txt">{{ serviceData.name }}</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('lg.linkMan') }}：</div>
              <div class="txt">{{ serviceData.linkMan }}</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('lg.linkPhone') }}：</div>
              <div class="txt">{{ serviceData.linkPhone }}</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('lg.address') }}：</div>
              <div class="txt">{{ serviceData.address }}</div>
            </div>
          </div>
        </div>
        <span class="close" @click="showParentService = false"></span>
        <div class="content-bottom">
          <div class="tips">{{ $t('H7ZxbhzZKtJsZjdkN7YJL') }}</div>
        </div>
      </div>
    </el-dialog>
    <!-- 围栏关联 -->
    <el-dialog title="" custom-class="monitor-bind-fence" :visible.sync="bindFenceFlag" :close-on-click-modal="false" width="880px">
      <bind-fence-modal
        ref="refBindeFence"
        v-if="bindFenceFlag"
        :fenceCarImei="fenceCarImei"
        :fenceCarId="fenceCarId"
        :carFenceId="bindCarFenceId"
        @hideBindClose="hideBindClose"
        @updateFenceList="updateFenceList"
      ></bind-fence-modal>
    </el-dialog>
    <!-- 行政区域选择省市区 -->
    <base-dialog
      @ok="confirmAreaChose"
      @cancel="hideAreaChose"
      :title="$t('xhq87s8s1ds1hdh1dhddh')"
      custom-class="monitor-area-chose"
      :visible.sync="areaChoseFlag"
      :close-on-click-modal="false"
      width="530px"
    >
      <div>
        <div class="fenceBody">
          <el-select v-model="provinceCode" value-key="adcode" placeholder="请选择省份" @change="provinceChange">
            <el-option v-for="item in provinceData" :key="item.adcode" :label="item.name" :value="item"> </el-option>
          </el-select>
          <el-select v-model="cityCode" value-key="adcode" placeholder="请选择市" @change="cityChange">
            <el-option v-for="item in cityData" :key="item.adcode" :label="item.name" :value="item"> </el-option>
          </el-select>
          <el-select v-model="districCode" value-key="adcode" placeholder="请选择区/镇">
            <el-option v-for="item in districtData" :key="item.adcode" :label="item.name" :value="item"> </el-option>
          </el-select>
        </div>
      </div>
    </base-dialog>
    <!-- 报警列表 -->
    <base-dialog :title="$t('bo540fd46Pfi59A-x8C_R')" custom-class="alarm-table-modal" :visible.sync="showAlarm" width="940px">
      <div>
        <div class="alarm-header">
          <img v-if="audioBtnStatus" class="audio-icon" src="@/assets/svg/audio_opend.svg" alt="audio_opend" @click="toggleAlarmSound(false)" />
          <img v-else class="audio-icon" src="@/assets/svg/audio_closed.svg" alt="audio_closed" @click="toggleAlarmSound(true)" />
        </div>
        <div class="alarm-body">
          <el-table :data="alarmTableData" :stripe="true" style="width: 100%" height="300">
            <el-table-column prop="machineName" :label="$t('lg.machineName')"></el-table-column>
            <el-table-column prop="typestr" :label="$t('ntUmxqogGfJUuLkjvK6ZV')"></el-table-column>
            <el-table-column prop="alarmTime" :label="$t('CA1jOSCwNEQbOx07cHlZm')" width="150">
              <template slot-scope="scope">
                {{ scope.row.alarmTime | UTCTimeToLocalTime }}
              </template>
            </el-table-column>
            <el-table-column prop="userName" :label="$t('aFq5tmk1n281NDkCSp8X6')"></el-table-column>
            <el-table-column prop="speed" :label="$t('dlW7RH6fkSItw_Mtz2Ykw') + '(' + miledgeUnitEN + '/h)'" width="100">
              <template slot-scope="scope">
                {{ scope.row.speed | MeterToKmMiBySpeed }}
              </template></el-table-column
            >
            <el-table-column prop="address" :label="$t('lg.address')">
              <template slot-scope="scope">
                <!-- <el-tooltip v-if="scope.row.address" popper-class="swd-tooltip-dark" :content="scope.row.address" placement="top">
                  <div class="address">{{ scope.row.address }}</div>
                </el-tooltip> -->
                <!-- <span class="addrbtn" @click="getParsingAddress(scope.row, scope.$index)">{{ $t('vBpDexpv7D_KNy_aaxmZx') }}</span> -->
                <svg-icon class="addrbtn" icon-class="daohangdizhi" @click="getParsingAddress(scope.row, scope.$index)" />
              </template>
            </el-table-column>
            <el-table-column prop="remark" :label="$t('lg.remark')"> </el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="setAlarmRead" type="primary" v-has="{ type: 2, perms: 'monitor:alarm:update' }">{{ $t('lg.setAllRead') }}</el-button>
        <el-button @click="linkToMoreAlarm" v-has="{ type: 2, perms: 'device:report:alarm:overview' }">{{ $t('IjtUG09nKBjgDwMEUXJEd') }}</el-button>
      </div>
    </base-dialog>
    <!-- 报警铃声 -->
    <audio ref="audio" src="../../../public/sound.mp3" />
    <!--    线路管理弹框-->
    <div class="manage-line-modal modal" v-if="showManageLine">
      <div class="title">
        <span> {{ $t('wegasqASDE32446sgwe07') }}</span>
        <div @click="ManageLineHandleClose"><img class="close" src="@/assets/img/close.png" /></div>
      </div>
      <div class="manage-line-body modal-body">
        <div class="search-line">
          <el-input :placeholder="$t('wegasqASDE32446sgwe08')" v-model="currentLineName">
            <img slot="suffix" src="@/assets/img/search2.png" @click="searchLineByName(1)" class="search-img" />
          </el-input>
        </div>
        <div class="btn-tools">
          <el-button @click="toManageLine(1, 1)" class="add-line">
            <svg-icon icon-class="add2" class="search-svg" /><span class="button-text">{{ $t('wegasqASDE32446sgwe09') }}</span>
          </el-button>
          <el-button @click="toSettingAlarm">
            <svg-icon icon-class="setting" class="search-svg" /><span class="button-text">{{ $t('GHkNMYw76nVM5W4mRICca') }}</span>
          </el-button>
        </div>
        <el-table :data="manageLineTableData.data" @selection-change="selectLine" height="404">
          <el-table-column type="selection" width="42"> </el-table-column>
          <el-table-column prop="name" :label="$t('KQBFZQ0Voyfhg6fQXT_Rn')"> </el-table-column>
          <el-table-column prop="operator" :label="$t('lg.operator')" align="right" width="80px">
            <template slot-scope="scope">
              <div class="operate">
                <el-tooltip popper-class="swd-tooltip-dark" :content="$t('lg.edit')" placement="top">
                  <svg-icon @click.stop="editLine(scope.row)" icon-class="modify" class-name="opera-look"></svg-icon>
                </el-tooltip>
                <el-tooltip v-has="{ type: 2, perms: 'fence:delete' }" popper-class="swd-tooltip-dark" :content="$t('lg.delete')" placement="top">
                  <img class="opera-del" @click.stop="deleteLine(scope.row)" src="@/assets/img/monitor/delete.png" />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <base-pagination
          class="line-pagination"
          @current-change="searchLineByName"
          :current-page.sync="manageLineTableData.pageIndex"
          layout="prev, jumper, next, total"
          :total="manageLineTableData.total"
        >
        </base-pagination>
      </div>
    </div>
    <!-- 免责声明 -->
    <PasswordCheckDialog :visible.sync="showPasswordCheck" @success="VerifySuccess" />
    <!-- 通知 -->
    <Notification :notifications="notifications" :customStyle="{ position: 'fixed', top: '400px', right: '50px' }" />
  </div>
</template>

<script>
import { queryPathLine, deletePathLine } from '@/api/public.js'
import ShareTrack from '@/components/Bussiness/ShareTrack'
import ShareTrackResult from '@/components/Bussiness/ShareTrackResult'
import BindFenceModal from '@/components/Bussiness/BindFenceModal'
import PasswordCheckDialog from '@/components/Bussiness/PasswordCheckDialog.vue'
import { mapState, mapGetters, mapActions } from 'vuex'
import '@/filter/publicfilter.js'
import { packagePath } from '@/constant/common'
import {
  loadScript,
  getIconByState,
  DateToFormatString,
  TimeStringToUTC,
  parseSeconds,
  TimeUTCToLocal,
  dirationToText,
  parseExData,
  DiffTimeSampleFormateUtil,
  matchMachineArr,
  IcondirationToText,
  GCJ02ToBD09,
  toUdskRobot,
  isSwdDomain
} from '@/utils/common.js'
import { MeterToKmMi } from '@/utils/convert.js'
import { loadAddress } from '@/utils/address.js'
import { subtract } from '@/utils/float.js'
import {
  getMileageStaById,
  getAllFenceData,
  deleteCarFence,
  deleteBatchCarFence,
  getAreaJSON,
  getAreaData,
  carFenceAdd,
  carFenceUpdate,
  getNotReadList,
  updateAlarmRead,
  updateAlarmIds,
  getPlaceGroup,
  getNotReadCount,
  deleteGroupPOI,
  deleteItemPOI,
  searchPlaceGroup,
  addGroupPOI,
  updateGroupPOI,
  addItemPOI,
  updateItemPOI,
  addBatchItemPOI,
  getCarFenceInfo,
  boundFenceCar,
  getWindowCenter,
  isPOICustomUserCheck
} from '@/api/monitor.js'
import { queryUserPerFence, addUserPerFence } from '@/api/public'
import { getParentUserInfoByCar, _userAuthConfigGetInfo } from '@/api/system.js'
import { getWarningMessageById } from '@/api/message.js'
import { _getCarImg } from '@/api/device.js'
import publicUserpic from '@/assets/img/userpic.png'
import 'maptalks/dist/maptalks.css'
import * as maptalks from 'maptalks'
window.maptalks = maptalks
import * as mapboxgl from 'mapbox-gl'
window.mapboxgl = mapboxgl
mapboxgl.accessToken = 'pk.eyJ1IjoiZ3VhcmR3ZW4iLCJhIjoiY2p1czB3YmZtMG51ZzQzcDh4ZjVieHJqcyJ9.Z4smjCpf6YCyk0WDAbOPWw'
import { MapboxglLayer } from 'maptalks.mapboxgl'
import { ClusterLayer } from 'maptalks.markercluster'
import { getHtml } from '@/assets/icon/buildIcon'
import { Notification } from '@/base-ui/notification'
import {
  PcMapSearch,
  PcMapReduce,
  PcMapAmplify,
  PcMapSatellite,
  PcMapFence,
  PcMapLine,
  PcMapInterest,
  PcMap,
  PcTool,
  PcMoreTool,
  PcOutage,
  PcMapStreet
} from '@/assets/icon'
import dayjs from 'dayjs'
import { getMapCarIcon } from '@/utils/mapTool'
import { timeConvert } from '@/utils/date'

const isDev = process.env.NODE_ENV === 'development'
let _api = ''
if (isDev) {
  _api = '/api'
}
let $ = null
export default {
  name: 'MaptalksMap',
  components: {
    Notification,
    ShareTrack,
    BindFenceModal,
    ShareTrackResult,
    PcMapSearch,
    PcMapReduce,
    PcMapAmplify,
    PcMapSatellite,
    PcMapFence,
    PcMapLine,
    PcMapInterest,
    PasswordCheckDialog,
    PcMap,
    PcMapStreet,
    PcTool,
    PcMoreTool
  },
  props: {
    carArr: {
      type: Array,
      default: () => []
    },
    updateCar: {
      type: Boolean,
      default: () => {
        return false // false
      }
    },
    showLine: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    pointFlag: {
      // 点聚合开关
      type: Boolean,
      default: () => {
        return false
      }
    },
    pageType: {
      type: String,
      default: () => {
        return ''
      }
    },
    alarmId: {
      type: String,
      default: () => {
        return ''
      }
    },
    timefresh: {
      type: [Number]
    },
    targetCarId: {
      type: [String, Number],
      default: () => {
        return -2
      }
    },
    targetUserId: {
      type: [String, Number],
      default: () => {
        return 1
      }
    },
    // 追踪页面全屏显示，非全屏模式下隐藏组件全部按钮，不出现车辆信息小弹窗
    trackPageScreenFull: {
      type: Boolean,
      default: true
    },
    // 是否全屏，实时视频全屏下，地图工具栏层级问题
    isFullScreen: {
      type: Boolean,
      default: () => {
        return false // false
      }
    },
    // 是否是线路设置页面
    isLineSettingMode: {
      type: Boolean,
      default: false
    },
    currentTrackCar: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      judgeMapTypeMenuDisplay: false,
      domainUrl: null,
      alarmNoteTimeout: null,
      activeCarTimeout: null,
      alarmTimeout: null,
      // 存储当前经纬度
      lat: 100.446638,
      lon: 23.171729,
      currentAddress: '',
      carsMap: {}, // 地图上所有车辆Map集合
      carsMapPoints: [], // 地图上所有车辆的点集合 和 carsMap 同步
      carMarkerMap: {}, // 车辆Marker集合，carsMap的carId如果存在carMarkerMap中就更新，否则carMarkerMap中增加
      carTrailMap: {},
      carTrailLineMap: {},
      swdMapTalk: null, // 地图引用对象
      nowZoom: 5, // 当前地图缩放等级
      nowShowSatellite: false, // 是否显示卫星地图模式
      vecLayer: null, // 轨迹和地点共用图层
      carsLayer: null, // 不需要点聚合的车辆图层
      initUserLocation: [100.446638, 23.171729], // 初始位置
      udt: '', // 当前日期戳
      timestamp: '', // 当前时间戳
      lang: 'cn', // 语言
      distanceTool: null, // 测距工具对象
      distanceInfoWindow: null, // 测距提示框
      fenceInfoWindow: null, // 存储围栏新增编辑信息框
      drawPoint: null,
      drawGeo: null,
      drawCycleRadius: null,
      fenceEditInfo: null, // 新增或者编辑的围栏信息
      areaChoseFlag: false,
      provinceData: [],
      cityData: [],
      districtData: [],
      provinceCode: '',
      cityCode: '',
      districCode: '',
      jsFileArr: [
        /* '/mapjs/downLoad.js', */
        '/mapjs/baidu-polygon.js',
        '/mapjs/jquery.min.js'
        /* '/mapjs/maptalks.markercluster.js' , '/js/maptalks.mapboxgl.js' */
      ],
      // mapjsFiles: ['https://maps.googleapis.com/maps/api/js?key=AIzaSyARQ1sSccKwIMll9WZlxFxXBBq68ag2PqY&libraries=places&callback=initGoogleMap'],
      mapjsFiles: ['https://maps.googleapis.com/maps/api/js?key=AIzaSyCpiiWsdoIB2tFSLI-PVUYp0FwvlAcD8Uk&libraries=places&callback=initGoogleMap'],
      searchAdr: '',
      showSearch: false,
      googlePoint: null,
      searchPointIcon: '/images/monitor/searchPoint.png',
      infoImages: [
        // 注意顺序不能随意改动
        '/images/monitor/elec.png', // 电量
        '/images/monitor/sign.png', // 信号
        '/images/monitor/wireline.png', // 是否有线
        '/images/monitor/location.png', // 定位
        '/images/monitor/mileage.png', // 里程
        '/images/monitor/acc.png', // acc
        '/images/monitor/oil.png', // 油量
        '/images/monitor/temp.png', // 温度
        '/images/monitor/voltage.png', // 电压
        '/images/monitor/addr.png', // 解析地址
        '/images/monitor/humidity.png', // 湿度
        '/images/monitor/oilAndEle.png', // 油电
        '/images/monitor/totalMileage.png', // 总里程
        '/images/monitor/enduranceMileage.png', // 续航里程
        '/images/monitor/gear.png', // 档位
        '/images/monitor/chargingStatus.png', // 充电状态
        '/images/monitor/video.png', // 视频监控
        '/images/monitor/tfCard.png', // TF卡
        '/images/monitor/tf_cartd_tip.png', // TF卡
        '/images/monitor/Charging.png', // 充电中
        '/images/monitor/icon-intercom-small-blue.png', // 对讲
        '/images/monitor/icon-warning-blue.png', // 对讲popover提示图标1
        '/images/monitor/icon-warning-yellow.png', // 对讲popover提示图标2
        '/images/monitor/ac.png', // ac
        '/images/monitor/car_door.png' // 车门
      ],
      replayImgs: [
        '/images/monitor/speed.png', // 速度
        '/images/monitor/date.png', // 日期
        '/images/monitor/mileage.png', // 里程
        '/images/monitor/pass.png', // 停留
        '/images/monitor/jingweidu.png' // 经纬度
      ],
      alarmImages: [
        '/images/monitor/alarm_icon1.png',
        '/images/monitor/alarm_icon2.png',
        '/images/monitor/alarm_icon3.png',
        '/images/monitor/alarm_icon4.png',
        '/images/monitor/alarm_icon5.png',
        '/images/monitor/alarm_icon6.png',
        '/images/monitor/alarm_icon7.png',
        '/images/monitor/alarm_icon8.png', // 线路名称
        '/images/monitor/alarm_icon9.png', // 查看报警视频
        '/images/monitor/alarm_icon10.png' // 查看报警照片
      ],
      poiIconTypeImgs: ['/images/map/1.png', '/images/map/2.png', '/images/map/3.png', '/images/map/4.png'],
      loadFileIndex: 0,
      barData: [
        { type: 1, show: true },
        { type: 2, show: true },
        { type: 3, show: true },
        { type: 4, show: false },
        { type: 5, show: false, active: false },
        { type: 6, show: false },
        { type: 7, show: false }
      ],
      barFlagOpen: false, // 是否展开更多右侧工具栏
      showManageLine: false, // 显示线路管理
      showTools: false, // 显示工具栏
      showFlagTraffic: false, // 是否展示路况信息
      mapTypes: [
        { type: 1, text: this.$t('A7xrOFhybj9Js9R7Oulub'), selected: false },
        { type: 2, text: this.$t('2yf5VqhJTX_NvDljHWexs'), selected: false },
        { type: 3, text: this.$t('rc-VB0jPoY98SMUVoUe7C'), selected: false },
        { type: 4, text: 'MapBox', selected: false },
        { type: 5, text: 'OSM Map', selected: false }
      ],
      nowSelectMarkerObj: {
        marker: null,
        carId: ''
      },
      // 上一个选择后的设备名称标记
      lasetSelectNameMarkerObj: {
        marker: null,
        carId: ''
      },
      shareHistoryFlag: false,
      shareData: {},
      isFixed: true,
      // 围栏管理
      showFence: false,
      allFenceQuery: {
        pageNO: 1,
        rowCount: 10,
        name: ''
      },
      allFenceTotal: 0,
      pagerCount: 5,
      allFenceData: [],
      fenceflag: true,
      geoObjects: [],
      fenceCarId: null, // 围栏是否属于包含的设备
      fenceCarImei: '',
      fenceTitle: null,
      drawFenCarId: null,
      bindCarFenceId: -1,
      showEveryFence: false, // 全部显示围栏图形
      multipleFence: [], // 选中的围栏
      bindFenceFlag: false, // 绑定围栏弹窗
      showAlarm: false, // 报警列表弹窗
      alarmAccount: 0, // 报警总数，比对总数变化，就要提示最新报警
      curAlarmInfo: {
        machineName: null
      }, // 最新报警的信息
      alarmTimer: null, // 报警定时器
      currentAlarm: null, // 当前报警点
      showPOIFlag: false,
      varshowpoi: true,
      poiparam: '', // poi搜索
      groupOptions: {},
      poiList: [], // POI所有分组信息
      placePointOverlays: [], //存储兴趣点的Overlays
      showAddOrEditPOI: false,
      poiAddOrEditData: {
        title: '',
        type: 1,
        placeGroupId: '',
        placeId: '',
        name: '',
        sequence: 1,
        remark: '',
        lon: '',
        lat: '',
        iconType: '',
        flag: 'add'
      },
      currentDefaultPoint: null, // 当前默认兴趣点
      poiAddOrEditDataBak: {},
      inputFile: '',
      poiUploadFileUrl: null,
      showDrawFlag: false,
      alarmTableData: [],
      poiCount: 0,
      scrollFlagToView: true,
      serviceData: {}, // 服务商
      showParentService: false, // 服务商弹窗
      playMarker: null, // 轨迹播放标记
      dirRefreshTimer: null,
      // 车辆弹框是否居中
      isWindowCenterUser: true,
      // POi定制用户
      isPOICustomUer: false,
      // 当前登录用户
      loginUserId: null,
      // 判断是否公司域名
      isSwd: true,
      timer: null,
      // 轨迹分享结果
      shareHistoryResultFlag: false,
      shareHistoryUrl: '',

      audioBtnStatus: false, //报警铃声开关
      currentLineName: null, // 当先线路查询名称
      // 线路管理列表
      manageLineTableData: {
        data: [],
        pageSize: 10,
        pageIndex: 1,
        total: 0
      },
      lineVecLayer: null, // 线路管理层
      // 标点绘制线数组
      drawLinePointsList: [],
      // 测距总里程数
      drawLinemileage: null,
      isRegionalCarSearch: false,
      // 起始 终点图片Maker
      startAndEndMarker: {
        startMaker: null,
        endMaker: null
      },
      // 绘图工具
      drawTool: null,
      // 国外屏蔽区域查车
      isShowRegional: true,
      showPasswordCheck: false, // 免责声明开关
      fenceInfo: null, // 当前编辑围栏信息
      showIntercomPop: false, //显示对讲popover
      // 轨迹回放-----------------------------------------
      currentCar: {}, //轨迹回放选中的车辆信息
      currentIndex: 0, // 当前所在帧
      allMileage: 0,
      currentDir: '',
      historyData: [], // 存储所有轨迹数据
      historyMarkers: [], // 轨迹产生的标记
      playIntervalValue: 4, //回放速度间隔
      markerPlayPause: false,
      slidePlayMarkerTimer: null,
      showMoreTool: false,
      notifications: [],
      carImageUrl: '', //车辆图片
      trackingCarMarker: null,
      trackingPoints: [],
      // trackingIndex: -1,
      markerAnimatePlayer: null
    }
  },
  computed: {
    ...mapState({
      mapType: state => state.app.mapType,
      userType: state => state.user.userinfo.userType,
      userinfo: state => state.user.userinfo,
      videoType: state => state.video.type,
      isVirtualUser: state => state.user.isVirtualUser
    }),
    ...mapGetters({
      miledgeUnitCN: 'user/miledgeUnitCN',
      miledgeUnitEN: 'user/miledgeUnitEN'
    }),
    isCnLang() {
      return this.$cookies.get('lang') === 'cn' || this.$cookies.get('lang') === 'tw'
    },
    showPOIEditButton() {
      if (this.isPOICustomUer && this.targetUserId != this.loginUserId) {
        return false
      } else {
        return true
      }
    }
  },
  directives: {
    drag: {
      // 使用bind会有可能没有渲染完成
      inserted: function(el, binding, vnode) {
        const _el = el //获取当前元素
        const ref = vnode.context.$refs[binding.value] // 判断基于移动的是哪一个盒子
        const masterNode = ref ? ref : document // 用于绑定事件
        const masterBody = ref ? ref : document.body // 用于获取高和宽
        const mgl = 0
        const mgt = 0
        const maxWidth = masterBody.clientWidth
        const maxHeight = masterBody.clientHeight
        console.log('maxWidth', maxWidth)
        console.log('maxHeight', maxHeight)
        const elWidth = _el.clientWidth
        const elHeight = _el.clientHeight
        let positionX = 0,
          positionY = 0
        _el.onmousedown = e => {
          //算出鼠标相对元素的位置，加上的值是margin的值
          let disX = e.clientX - _el.offsetLeft + mgl
          let disY = e.clientY - _el.offsetTop + mgt

          masterNode.onmousemove = e => {
            //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
            let left = e.clientX - disX
            let top = e.clientY - disY
            // 绑定的值不能滑出基于盒子的范围
            left < 0 && (left = 0)
            left > maxWidth - elWidth - mgl && (left = maxWidth - elWidth - mgl)
            top < 0 && (top = 0)
            top > maxHeight - elHeight - mgt && (top = maxHeight - elHeight - mgt)
            //绑定元素位置到positionX和positionY上面
            positionX = top
            positionY = left

            //移动当前元素
            _el.style.left = left + 'px'
            _el.style.top = top + 'px'
          }
          // 这里是鼠标超出基于盒子范围之后再松开，会监听不到
          document.onmouseup = e => {
            masterNode.onmousemove = null
            document.onmouseup = null
          }
        }
      }
    }
  },
  watch: {
    carArr: {
      handler(newValue, oldValue) {
        const self = this
        // 初始化的时候
        // updateCar 为true只是定时器更新设备状态，false是重新加载设备列表相当初始化
        if (!this.updateCar) {
          // console.log('初始化')
          this.scrollFlagToView = true
          this.carsMap = null
          this.carsMap = {}
          this.carsMapPoints = null
          this.carsMapPoints = []
          this.carMarkerMap = null
          this.carMarkerMap = {}
          if (this.nowSelectMarkerObj.marker) {
            this.nowSelectMarkerObj.marker.removeInfoWindow()
          }
          if (this.swdMapTalk && this.swdMapTalk.getLayer('cluster')) {
            this.swdMapTalk.removeLayer('cluster')
          }
          if (this.swdMapTalk && this.carsLayer) {
            this.swdMapTalk.removeLayer(this.carsLayer)
            this.carsLayer = null
          }
          this.nowSelectMarkerObj = null
          this.nowSelectMarkerObj = {
            marker: null,
            carId: ''
          }
        } else {
          // console.log('更新')
          // console.log(newValue)
        }
        if (!newValue.length) {
          this.carsMap = null
          this.carsMap = {}
          if (this.swdMapTalk && this.swdMapTalk.getLayer('cluster')) {
            this.swdMapTalk.removeLayer('cluster')
          }
        } else {
          let arr = []
          let points = []
          newValue.forEach(car => {
            this.carsMap[car.carId] = car
            if (/* car.serviceState !== '1' &&  */ car.carStatus && car.carStatus.latc !== 0 && car.carStatus.lonc !== 0) {
              arr.push([car.carStatus.lonc, car.carStatus.latc, car.carId])
              points.push({ lonc: car.carStatus.lonc, latc: car.carStatus.latc })
            }
          })
          this.carsMapPoints = JSON.parse(JSON.stringify(arr))
          arr = null
          if (!this.updateCar && points.length && this.targetCarId === -1) {
            // 初始化
            let center = this.getCenterPoint(points)
            this.initUserLocation = [center.lon, center.lat]
            if (this.$cookies.get('site') === 'gpsnow') {
              this.setCenter(center.lon, center.lat, 6)
            } else {
              this.setCenter(center.lon, center.lat, 5)
            }
          }

          if (this.swdMapTalk && points.length) {
            this.showCars()
            if (!this.updateCar) {
              // 初始化 选中了设备
              if (this.targetCarId !== -1) {
                let _choseCar = this.carsMap[this.targetCarId]
                if (_choseCar && _choseCar.carStatus && _choseCar.carStatus.lonc) {
                  this.initUserLocation = [_choseCar.carStatus.lonc, _choseCar.carStatus.latc]
                }
              }
              this.swdMapTalk.setCenter(this.initUserLocation)
            } else {
              // 更新，并且选中了具体设备
              /* let _car = this.carsMap[this.targetCarId]
              if (this.targetCarId !== -1 && _car.carStatus) {
                this.initUserLocation = [_car.carStatus.lonc, _car.carStatus.latc]
                this.swdMapTalk.setCenter(this.initUserLocation)
              } */
            }
          }
          points = null
          if (this.targetCarId !== -1 && this.scrollFlagToView) {
            let self = this
            this.activeCarTimeout = setTimeout(() => {
              let target = document.querySelectorAll('.choseActiveCar')
              if (target.length) {
                // 选中的设备在可视区域内
                target[0].scrollIntoView({ block: 'center' })
                this.scrollFlagToView = false
              }
              clearTimeout(self.activeCarTimeout)
              self.activeCarTimeout = null
            }, 100)
          }
        }
        // console.log(this.initUserLocation)
      },
      deep: true
    },
    targetCarId: {
      handler(newValue, oldValue) {
        if (newValue === '' || newValue === -1) {
          console.log('清除具体设备的信息框')
        } else {
          console.log('显示设备：' + newValue + '信息提示')
          /* let marker = this.carMarkerMap[newValue]
          if (marker) {
            this.showCarInfoBox(marker, this.carsMap[newValue])
          } */
          if (this.scrollFlagToView) {
            // if (this.nowSelectMarkerObj.marker) {
            //   this.nowSelectMarkerObj.marker.removeInfoWindow()
            // }
            this.nowSelectMarkerObj = {
              marker: null,
              carId: ''
            }
            let target = document.querySelectorAll('.choseActiveCar')
            if (target.length) {
              // 选中的设备在可视区域内
              target[0].scrollIntoView({ block: 'center' })
              this.scrollFlagToView = false
            }
          }
        }
      },
      deep: true
    },
    timefresh: {
      handler(newValue, oldValue) {
        if ($ && $('#monitor_second').length) {
          $('#monitor_second').html(newValue)
        }
      },
      deep: true
    },
    userType: {
      handler(newValue, oldValue) {
        // console.log('用户类型加载到了：' + newValue)
      },
      deep: true
    },
    targetUserId(targetUserId) {
      console.log('map中的targetUserID', targetUserId)
      // this.removeAllPlacePoints()
      // this.getPOIList()
      if (this.isPOICustomUer && this.showPOIFlag) {
        console.log('移除兴趣点')
        this.removeAllPlacePoints()
        this.getPOIList()
      }
    },
    // 实时视频小屏显示
    trackPageScreenFull: {
      handler(newValue, oldValue) {
        // 大屏是自动打开信息窗口,小窗口关闭窗口
        if (this.nowSelectMarkerObj && this.nowSelectMarkerObj.marker) {
          // 大窗口
          if (newValue) {
            this.nowSelectMarkerObj.marker.openInfoWindow()
          } else {
            // 小窗口
            this.nowSelectMarkerObj.marker.closeInfoWindow()
          }
        }
      }
    },
    currentTrackCar: {
      handler(val) {
        if (!this.vecLayer) {
          this.vecLayer = new maptalks.VectorLayer('vector').addTo(this.swdMapTalk)
        }
        if (!this.carsLayer) {
          this.carsLayer = new maptalks.VectorLayer('carsLayer').addTo(this.swdMapTalk)
        }
        let carPoint = JSON.parse(JSON.stringify(val))
        carPoint.currentTime = dayjs().valueOf()
        carPoint.speedColor = this.getColorBySpeed(carPoint.carStatus.speed)
        let status = 'blue'
        let icon = ''
        // 车辆图标
        if (val.carStatus) {
          if (val.carStatus.online) {
            if (val.carStatus.speed === 0) {
              //  怠速状态
              const idleTime = parseExData(val.carStatus.exData, 'idleTime') // 怠速时间
              if (idleTime) {
                // icon = getIconByState(val, 'orange')
                status = 'orange'
              } else {
                // 静止状态
                // icon = getIconByState(val, 'blue')
                status = 'blue'
              }
            } else {
              //运动状态
              let flag = true // 超速标记  true：正常运动  false： 超速运动
              // 超速报警打开
              if (val.carStatus.alarm) {
                let _str = val.carStatus.alarm
                let _arr = []
                if (val.carStatus.alarm.indexOf(':') === 0) {
                  _str = _str.substring(1, _str.length)
                  _arr = _str.split(':')
                }
                for (let i = 0; i < _arr.length; i++) {
                  let item = _arr[i].split(',')[0]
                  if (item === 5) {
                    flag = false
                    break
                  }
                }
              } else {
                if (val.carStatus.speed >= 120) {
                  flag = false
                }
              }
              if (flag) {
                status = 'green'
                // icon = getIconByState(val, 'green')
              } else {
                // 超速
                // icon = getIconByState(val, 'red')
                status = 'red'
              }
            }
          } else {
            // icon = getIconByState(val, 'white')
            status = 'white'
          }
        }
        icon = getMapCarIcon(status, val.carType)
        // if (!isDev) {
        //   icon = `${packagePath}` + icon
        // }
        carPoint.icon = icon

        this.trackingPoints.push(carPoint)
        // this.carsMap[val.carId] = val

        //车辆类型1-水滴icon、2和16-个人，22是宠物
        // 2025 07-30,coolpet v1.1.7 修正猫（31），狗（32）图标没有方向
        let changeDir = [1, 2, 16, 22, 31, 32, 33, 34].includes(Number(val.carType)) ? false : true
        let dir = val.carStatus.dir || 0
        let marker
        if (this.trackingCarMarker) {
          let car = this.trackingPoints[this.trackingPoints.length - 2]
          if (!car) {
            return
          }
          this.updateMarkerPositionAnimate(changeDir)
          this.showCarInfoBox(this.trackingCarMarker, car)
          // this.setCenter(val.lonc, val.latc)
        } else {
          marker = new maptalks.Marker([val.carStatus.lonc, val.carStatus.latc], {
            visible: true,
            editable: true,
            cursor: 'pointer',
            shadowBlur: 0,
            shadowColor: 'black',
            draggable: false,
            dragShadow: false, // display a shadow during dragging
            drawOnAxis: null, // force dragging stick on a axis, can be: x, y
            zIndex: 9998,
            symbol: {
              markerFile: icon,
              carId: val.carId,
              markerDx: 0,
              markerDy: 16,
              markerRotation: changeDir ? 360 - dir : null
            }
          })

          marker.addTo(this.carsLayer)
          this.bindInfoWindow(marker, val)
          this.trackingCarMarker = marker
        }
      },
      deep: true
    }
  },
  created() {
    this.domainUrl = this.$cookies.get('domainUrl')
    // 检查是否POI定制用户
    this.checkPOICustomUser()
    this.udt = new Date().Format('yyyy-MM-dd')
    this.timestamp = new Date().getTime()
    this.lang = this.$cookies.get('lang')
    this.nowZoom = 5
    if (this.pageType === 'track' || this.pageType === 'monitor') {
      // 跟踪页面或监控页面
      this.nowZoom = 17
    }
    this.mapTypes.forEach(item => {
      if (item.type === this.mapType) {
        item.selected = true
      }
    })
    this.poiAddOrEditDataBak = JSON.parse(JSON.stringify(this.poiAddOrEditData))
    if (!isDev) {
      this.searchPointIcon = `${packagePath}` + this.searchPointIcon
      this.infoImages = this.infoImages.map(item => {
        item = `${packagePath}` + item
        return item
      })
      this.alarmImages = this.alarmImages.map(item => {
        item = `${packagePath}` + item
        return item
      })
      this.poiIconTypeImgs = this.poiIconTypeImgs.map(item => {
        item = `${packagePath}` + item
        return item
      })
    }
    // this.getCurLocation()
    // console.log(this.$store.state.permissions)
    this.$store.state.permissions.forEach(item => {
      if (item.perms === 'monitor:alarm:list') {
        this.getAlarmCountSever()
        return
      }
    })
    // monitor:alarm:list
  },
  mounted() {
    window.location.host.indexOf('whatsgps.com') === -1 ? (this.isShowRegional = true) : (this.isShowRegional = false)
    this.isSwd = isSwdDomain()
    const self = this
    // 动态加载地图相关js
    this.jsFileArr.forEach(item => {
      let _url = item
      if (!isDev) {
        _url = `${packagePath}` + item
      }
      loadScript(_url, function() {
        self.loadFileIndex++
        self.loadJsFileAfter()
      })
    })
    // 判断是否定制用户车辆弹框居中
    getWindowCenter({ userId: this.$cookies.get('userId') }).then(res => {
      if (res.data && res.data.popKeepStill === 1) {
        this.isWindowCenterUser = false
      }
    })
    this.$store.dispatch('video/loadWebrtcJS')
  },
  methods: {
    ...mapActions({
      setMapType: 'app/setMapType',
      setIframeIndex: 'app/setIframeIndex'
    }),
    loadJsFileAfter() {
      if (this.loadFileIndex == this.jsFileArr.length) {
        // js加载完毕
        // console.log(window.BMap)
        $ = window.$
        this.showMapLayer(this.mapType)
        if (this.alarmId) {
          this.getAlarmPointInfo()
        }
        // 线路管理页面自动打开地址搜索框
        this.isLineSettingMode && this.showSearchFun()
      }
    },
    // 显示具体设备的信息框，父组件有调用
    async showTargetCar(carId) {
      let marker = this.carMarkerMap[carId]
      let car = this.carsMap[carId]
      let expiredType = -1
      if (car.serviceState == '1') {
        let _now = new Date(new Date().Format('yyyy-MM-dd')).getTime()
        if (new Date(car.platformTime).getTime() <= _now) {
          expiredType = 1 // 平台到期
        } else {
          expiredType = 2 // 用户到期
        }
        if (expiredType !== -1) {
          if (this.nowSelectMarkerObj.marker) {
            let _handle = this.nowSelectMarkerObj.marker.getInfoWindow()
            if (_handle && _handle.isVisible()) {
              _handle.hide()
            }
          }
          this.showExpriedDialog(car, expiredType)
          this.$parent.getCarInfoAndShowDetail(carId, 'detail')
          return
        }
      }
      if (marker) {
        if (expiredType == 2) {
          this.getServiceInfoByCar(car)
        }
        this.swdMapTalk.setCenter([car.carStatus.lonc, car.carStatus.latc])
        this.swdMapTalk.setZoom(17)
        if (this.nowSelectMarkerObj.carId === carId) {
          // 已经存在，并且隐藏了，需要显示出来
          let _handle = this.nowSelectMarkerObj.marker.getInfoWindow()
          if (_handle && !_handle.isVisible()) {
            _handle.show()
          }
        }
        this.showCarInfoBox(marker, this.carsMap[carId])
      }
    },
    async getServiceInfoByCar(car) {
      // 获取设备所属用户的经销商
      const self = this
      let res = await getParentUserInfoByCar({
        carId: car.carId
      })
      if (res.ret === 1) {
        self.serviceData = res.data
        if (self.serviceData.imageURL) {
          self.serviceData.imageURL = '../..' + _api + '/image/getImage.do?imageId=' + self.serviceData.imageURL + '&token=' + self.$cookies.get('token')
        } else {
          if (!self.serviceData.imageURL) {
            self.serviceData.imageURL = publicUserpic
          }
        }
      }
    },
    // 展开更多右侧工具栏
    openMoreBarControl() {
      if (!this.showMoreTool) {
        for (let i = 2; i < this.barData.length; i++) {
          this.barData[i].show = true
        }
      } else {
        for (let i = 2; i < this.barData.length; i++) {
          this.barData[i].show = false
        }
      }
      this.showMoreTool = !this.showMoreTool
      // this.barFlagOpen = !this.barFlagOpen
    },
    // 测距
    openRulerTool() {
      const self = this
      self.swdMapTalk.setCursor('crosshair')
      if (self.distanceTool) {
        self.distanceTool.clear()
        self.distanceTool = null
        self.drawLinePointsList = []
        self.drawLinemileage = null
      }
      self.distanceTool = new maptalks.DistanceTool({
        once: true,
        symbol: {
          lineColor: '#fd8044',
          lineWidth: 2
        },
        vertexSymbol: {
          markerType: 'ellipse',
          markerFill: '#fff',
          markerLineColor: '#ff0000',
          markerLineWidth: 2,
          markerWidth: 9,
          markerHeight: 9
        },
        metric: self.miledgeUnitEN == 'km',
        imperial: self.miledgeUnitEN == 'mi',
        language: self.lang == 'cn' || self.lang == 'tw' ? 'zh-CN' : 'en-US'
      }).addTo(self.swdMapTalk)
      self.distanceTool.on('drawstart', function(event) {
        let options = {
          autoPan: false,
          //'autoOpenOn' : 'click',  //set to null if not to open window when clicking on map
          single: true,
          custom: true,
          dx: 50,
          dy: 40,
          content: '<div class="distanceTip" style="white-space:nowrap;background:#fff;padding:5px" >' + self.$t('kGPteGn1UwCDzRpDTYOOA') + '</div>',
          animation: 'none'
        }
        let infoWindow = (self.distanceInfoWindow = new maptalks.ui.InfoWindow(options))
        infoWindow.addTo(self.swdMapTalk).show(event.coordinate)
      })
      self.distanceTool.on('drawend', function(event) {
        self.drawLinePointsList = event.geometry._coordinates
        if (event.drawTool) {
          self.drawLinemileage = event.drawTool._lastVertex._content
        }
        self.distanceInfoWindow.remove()
        self.distanceTool.removeEventListener('mousemove')
        self.swdMapTalk.setCursor('auto')
      })
      self.distanceTool.on('mousemove', function(event) {
        self.distanceInfoWindow.show(event.coordinate)
      })
    },
    // 查看更多报警信息
    showMoreAlarm() {
      this.notifications = []
      this.showAlarm = true
      let postData = {
        focus: false,
        nameFlag: true,
        targetUserId: this.$cookies.get('userId'),
        mapType: this.mapType
      }
      getNotReadList(postData).then(res => {
        if (res.ret === 1) {
          let arr = res.data
          arr = arr.map(item => {
            item.typestr = this.$t('lg.alarmType.' + item.alarmType)
            item.address = ''
            return item
          })
          this.alarmTableData = arr
        } else {
          this.alarmTableData = []
        }
      })
    },
    showRobot() {
      toUdskRobot()
    },
    // 获取报警总数
    getAlarmCountSever() {
      if (this.pageType !== 'monitor') {
        return
      }
      // 报警铃声开关用户偏好
      this.queryUserAlarmAudioPerFence()
      let postData = {
        focus: false,
        nameFlag: true,
        targetUserId: this.$cookies.get('userId'),
        mapType: this.mapType
      }
      getNotReadCount(postData).then(res => {
        if (res.ret === 1 && res.data) {
          this.alarmAccount = 1
          this.curAlarmInfo = res.data
          this.notifications.push({
            id: Date.now(),
            message: '报警消息通知',
            description: `${this.curAlarmInfo.machineName}${this.$t('EDcFx0tNb2Z2qVSxFsaJh')}，${this.$t('kbqyQYySG3XGM2bVA-4-t')}`,
            callBackFunc: () => {
              this.showMoreAlarm()
            }
          })
          // 报警铃声播放
          this.playAudio()
        } else {
          this.alarmAccount = 0
        }
        this.getAlarmCountTimer()
      })
    },
    // 标记已读
    setAlarmRead() {
      const self = this
      if (!self.alarmTableData.length) {
        self.$message({
          message: self.$t('6ed5szctmCrXayO-9xgWW'),
          type: 'error'
        })
        return
      } else {
        updateAlarmRead({}).then(res => {
          if (res.ret === 1) {
            self.showAlarm = false
            self.alarmAccount = 0
            self.$message({
              message: self.$t('lg.success'),
              type: 'success'
            })
          }
        })
      }
    },
    // 定时获取报警
    async getAlarmCountTimer() {
      const self = this
      clearInterval(self.alarmTimer)
      self.alarmTimer = null
      self.alarmTimer = setInterval(async () => {
        clearTimeout(self.alarmTimeout)
        self.alarmTimeout = null
        self.alarmTimeout = setTimeout(() => {
          self.getAlarmCountTimeout()
        }, 0)
      }, 15000)
    },
    async getAlarmCountTimeout() {
      const self = this
      let postData = {
        focus: false,
        nameFlag: true,
        targetUserId: this.$cookies.get('userId'),
        mapType: this.mapType,
        t: new Date().getTime(),
        token: this.$cookies.get('token')
      }
      let urlReadCount = _api + '/carAlarm/getOneNotRead.do'
      let res = await self.$jsonp(urlReadCount, postData).then(json => {
        return json
      })
      let showAlarm = false
      if (res.ret === 1 && res.data) {
        if (self.curAlarmInfo.alarmId) {
          //上一次查询有报警消息
          if (res.data.alarmId !== self.curAlarmInfo.alarmId) {
            //上一次报警id不是这次的，说明有新的报警
            showAlarm = true
          }
        } else {
          showAlarm = true
        }
        if (showAlarm) {
          this.alarmAccount = 1
          self.curAlarmInfo = res.data
          this.notifications.push({
            id: Date.now(),
            message: '报警消息通知',
            description: `${this.curAlarmInfo.machineName}${this.$t('EDcFx0tNb2Z2qVSxFsaJh')}，${this.$t('kbqyQYySG3XGM2bVA-4-t')}`,
            callBackFunc: () => {
              this.showMoreAlarm()
            }
          })
          // 报警铃声播放
          this.playAudio()
          if (self.alarmNoteTimeout) {
            clearTimeout(self.alarmNoteTimeout)
          }
          self.alarmNoteTimeout = setTimeout(() => {
            //30s后自动关闭switch
            clearTimeout(self.alarmNoteTimeout)
            self.alarmNoteTimeout = null
          }, 30000)
        }
      }
    },
    // 跳转
    linkToMoreAlarm() {
      this.$router.push({ name: 'Statistics', query: { flag: 'a', cat: '2-1' } })
    },
    // 地址解析
    async getParsingAddress(data, index) {
      let _update = false
      this.$store.state.permissions.forEach(item => {
        if (item.perms === 'monitor:alarm:update') {
          _update = true
          return
        }
      })
      if (_update) {
        updateAlarmIds({
          ids: data.alarmId
        }).then(res => {
          if (res.ret === 1) {
            this.alarmAccount--
            // 16 监控-报警单条查看地址
            this.showAlarmPoint(data, 16)
          }
        })
      } else {
        this.showAlarmPoint(data, 16)
      }
    },
    getAlarmPointInfo() {
      getWarningMessageById({ id: this.alarmId, mapType: this.mapType }).then(res => {
        if (res.ret === 1 && res.data) {
          this.showAlarmPoint(res.data, 13)
          this.carsMap[res.data.carId] = res.data
          this.nowSelectMarkerObj = { carId: res.data.carId }
        }
      })
    },
    // 地图上显示报警信息点
    showAlarmPoint: async function(data, businessName) {
      console.log('报警点', data)
      const self = this
      let imgs = self.alarmImages
      /* eslint-disable */
      // let isInChina = ptInPolygon(data.lon, data.lat) // 坐标点是否在中国的判断算法
      /* eslint-enable */
      /* if (isInChina !== 'cn') {
        data.lonC = data.lon //使用国际标
        data.latC = data.lat //使用国际标
      } */
      let _alarmTime = TimeUTCToLocal(data.alarmTime)
      let _pointTime = TimeUTCToLocal(data.pointTime)
      let _alarmType = self.$t('lg.alarmType.' + data.alarmType)
      let _address = await loadAddress({ lon: data.lon, lat: data.lat, lonC: data.lonC, latC: data.latC, businessType: businessName, carId: data.carId })
      let _htmlstr = ''
      let showLineName = 'hide'

      // 判断是否显示报警图片、报警视频
      let videoList = [] // 视频列表
      let pictureList = [] // 图片列表
      let isShowPicture = 'hide'
      let isShowVideo = 'hide'
      const { attachments } = data
      if (data && Object.prototype.hasOwnProperty.call(data, 'attachments') && attachments instanceof Array) {
        attachments.forEach(elem => {
          let { url } = elem
          if (url && /\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(url)) {
            pictureList.push(elem)
          }
          if (url && /(.*)\.(mp4|rmvb|avi|ts)$/.test(url)) {
            videoList.push(elem)
          }
        })
        if (pictureList.length > 0) {
          isShowPicture = ''
        }
        if (videoList.length > 0) {
          isShowVideo = ''
        }
      }

      // 视频设备查看报警图片、视频
      let _pictureStr = `<div class="right-icon-container">
      <div class="right-icon-item ${isShowPicture}">
      <img class="alarm-pictrue" src="${imgs[9]}"/>
      <div class="item-tips">${this.$t('76VJ_0CYltD6DDiTNyZxf')}</div>
      </div>
      <div class="right-icon-item ${isShowVideo} ">
      <img class="alarm-video" src="${imgs[8]}"/>
      <div class="item-tips">${this.$t('8j-cIxRn0mxuGcQu-J6U-')}</div>
      </div>
      </div>`

      // 线路偏移和超速报警时显示
      data.alarmType === 73 || data.alarmType === 74 ? (showLineName = '') : (showLineName = 'hide')
      if (data.alarmType === 27 || data.alarmType === 26 || data.alarmType === 28 || data.alarmType === 29) {
        let _alarmFenceName = ''
        if (data.remark) {
          let arr = data.remark.split(',')
          if (arr.length > 0) {
            _alarmFenceName = arr[0]
          }
        }
        _htmlstr = `<div id="alarmBoxModal">
        <div class="alarmBoxHeader">
          <span>${data.machineName}</span>${_pictureStr}<div class="close"></div>
        </div>
        <div class="alarmBoxBody">
          <div class="col"><img class="icon" src="${imgs[6]}"/><span class="fence-name">${_alarmFenceName}</span></div>
          <div class="col"><img class="icon" src="${imgs[0]}"/>${_alarmTime}</div>
          <div class="col"><img class="icon" src="${imgs[1]}"/>${_alarmType}</div>
          <div class="col"><img class="icon" src="${imgs[2]}"/>${_pointTime}</div>
          <div class="col"><img class="icon" src="${imgs[3]}"/>${dirationToText(data.dir)}（${data.dir}°）</div>
          <div class="col"><img class="icon" src="${imgs[4]}"/>${MeterToKmMi(data.speed * 1000)}${self.miledgeUnitEN}/h</div>
        </div>
        <div class="alarmAddress"><img class="icon3" src="${imgs[5]}"/>${_address}</div>
      </div>`
      } else {
        _htmlstr = `<div id="alarmBoxModal">
        <div class="alarmBoxHeader">
          <span>${data.machineName}</span>${_pictureStr}<div class="close"></div>
        </div>
        <div class="alarmBoxBody">
          <div class="col ${showLineName}"><img class="icon" src="${imgs[7]}"/>${data.remark}</div>
          <div class="col"><img class="icon" src="${imgs[0]}"/>${_alarmTime}</div>
          <div class="col"><img class="icon" src="${imgs[1]}"/>${_alarmType}</div>
          <div class="col"><img class="icon" src="${imgs[2]}"/>${_pointTime}</div>
          <div class="col"><img class="icon" src="${imgs[3]}"/>${dirationToText(data.dir)}（${data.dir}°）</div>
          <div class="col"><img class="icon" src="${imgs[4]}"/>${MeterToKmMi(data.speed * 1000)}${self.miledgeUnitEN}/h</div>
        </div>
        <div class="alarmAddress"><img class="icon3" src="${imgs[5]}"/>${_address}</div>
      </div>`
      }

      let opt = {
        lng: data.lonC, // 位置*/
        lat: data.latC, // 位置*/
        icon: '/images/map/machine/alarm.png', // 获取图标
        label: _htmlstr
      }
      if (!isDev) {
        opt.icon = `${packagePath}` + opt.icon
      }
      console.log('opt', opt)
      self.removeAlarmPoint()
      let alarmMarker = self.newStopMarker(opt, 'alarmPoint')
      alarmMarker.openInfoWindow()
      // 绑定关闭信息窗口事件
      if ($ && $('#alarmBoxModal .close').length) {
        $('#alarmBoxModal .close')
          .unbind()
          .click(function() {
            self.removeAlarmPoint()
          })
      }
      // 绑定报警抓拍图片预览
      if ($ && $('#alarmBoxModal .right-icon-container .alarm-pictrue').length) {
        $('#alarmBoxModal .right-icon-container .alarm-pictrue')
          .unbind()
          .click(function() {
            self.$emit('alarmPicturePreview', {
              pictureList,
              alarmInfo: {
                machineName: data.machineName,
                Event: _alarmType,
                speed: data.speed,
                address: _address
              }
            })
          })
      }
      // 绑定报警视频预览
      if ($ && $('#alarmBoxModal .right-icon-container .alarm-video').length) {
        $('#alarmBoxModal .right-icon-container .alarm-video')
          .unbind()
          .click(function() {
            self.$emit('alarmVideoPreview', {
              videoList,
              alarmInfo: {
                machineName: data.machineName,
                Event: _alarmType,
                speed: data.speed,
                address: _address
              }
            })
          })
      }
      self.currentAlarm = alarmMarker
      self.setCenter(opt.lng, opt.lat, 13)
      // 解决图标初次定位不准
      this.swdMapTalk.setZoom(12)
      self.showAlarm = false
      return alarmMarker
    },
    // 创建方向marker
    newDirMarker: function(opt) {
      let marker = new maptalks.Marker([opt.lng, opt.lat], {
        visible: true,
        editable: true,
        cursor: 'pointer',
        shadowBlur: 0,
        shadowColor: 'black',
        draggable: false,
        dragShadow: false, // display a shadow during dragging
        drawOnAxis: null, // force dragging stick on a axis, can be: x, y
        zIndex: 100,
        symbol: {
          markerFile: opt.icon
          // 'textFaceName' : 'sans-serif',
          // 'textName' : 'MapTalks',
          // 'textFill' : '#34495e',
          // 'textHorizontalAlignment' : 'right',
          // 'textSize' : 40
          // 'markerDx': 0,
          // 'markerDy': 10
        }
      }).addTo(this.vecLayer)
      marker.on('click', async function() {
        console.log('方向')
      })
      if (opt.label) {
        let infoWindow = {
          title: '',
          content: opt.label,
          // 'width': 300,
          // 'minHeight': 50,
          single: false, //是否只显示一个信息框
          autoPan: false,
          custom: false, //自定义信息框
          dx: 0,
          dy: -10
        }
        marker.setInfoWindow(infoWindow)
      }
      return marker
    },
    // 创建图标
    //  type:  lineVecLayer 则是线路层
    newStopMarker: function(opt, type = 'other', infoWindow) {
      let tempSymbol = {
        markerFile: opt.icon
      }
      // 图标有配置大小
      if (opt.markerWidth) {
        tempSymbol.markerWidth = opt.markerWidth
        tempSymbol.markerHeight = opt.markerHeight
      }
      let vecLayer = null
      if ('lineVecLayer' === type) {
        if (!this.lineVecLayer) {
          this.lineVecLayer = new maptalks.VectorLayer('lineVecLayer').addTo(this.swdMapTalk)
        }
        vecLayer = this.lineVecLayer
      } else {
        if (!this.vecLayer) {
          this.vecLayer = new maptalks.VectorLayer('vector').addTo(this.swdMapTalk)
        } else if (type === 'alarmPoint') {
          this.vecLayer.addTo(this.swdMapTalk)
        }
        vecLayer = this.vecLayer
      }

      let marker = null
      // 获取图片ID
      let markerId = null
      opt.id ? (markerId = opt.id) : (markerId = opt.lat + opt.lng)
      if (vecLayer.getGeometryById(markerId)) {
        marker = vecLayer.getGeometryById(markerId)
        marker.setSymbol(tempSymbol)
        marker.setCoordinates({
          x: parseFloat(opt.lng),
          y: parseFloat(opt.lat)
        })
      } else {
        marker = new maptalks.Marker([parseFloat(opt.lng), parseFloat(opt.lat)], {
          visible: true,
          editable: true,
          cursor: 'pointer',
          shadowBlur: 0,
          shadowColor: 'black',
          draggable: false,
          dragShadow: false, // display a shadow during dragging
          drawOnAxis: null, // force dragging stick on a axis, can be: x, y
          zIndex: opt.zIndex ? opt.zIndex : 98,
          symbol: tempSymbol,
          id: opt.id ? opt.id : opt.lat + opt.lng
        }).addTo(vecLayer)
      }
      if (opt.label) {
        if (infoWindow) {
          marker.setInfoWindow(infoWindow)
        } else {
          let infoWindow = {
            title: '',
            content: opt.label,
            single: true, //是否只显示一个信息框
            autoPan: false,
            custom: true,
            dx: 0,
            dy: -8
          }
          marker.setInfoWindow(infoWindow)
        }
      }
      return marker
    },
    // 移除报警信息点
    removeAlarmPoint() {
      if (this.currentAlarm) {
        this.currentAlarm.remove()
        this.currentAlarm = null
      }
    },
    // 显示地图路况信息
    showTrafficLayer() {
      if (window._hmt) {
        window._hmt.push(['_trackEvent', '定位监控', '路况', '路况'])
      }
      if (this.mapType !== 1) {
        let point = this.swdMapTalk.getCenter()
        // 除了百度地图，路况信息都打开，路径要线上环境才能打开
        window.open('../../page/traffic.html?lng=' + point.x + '&lat=' + point.y + '&zoom=' + this.nowZoom, '_blank')
      } else {
        if (this.swdMapTalk) {
          if (!this.showFlagTraffic) {
            this.swdMapTalk.getLayer('traffic').show()
          } else {
            this.swdMapTalk.getLayer('traffic').hide()
          }
        }
        this.showFlagTraffic = !this.showFlagTraffic
      }
    },
    // 切换不同类型地图
    chooseMapItem(index) {
      let selected = this.mapTypes[index].selected
      if (!selected) {
        this.mapTypes.forEach(item => {
          item.selected = false
        })

        this.mapTypes[index].selected = true
        let type = this.mapTypes[index].type
        // 存储地图类型
        this.setMapType(type)
        this.$cookies.set('select_maptype', type)
        /* 
          2025-05-21 打个补丁，分享链接里带着地图类型。切换地图后会清除图层，导致弹窗信息窗口消失
          debugger定位到问题出现在maptalks源码里，无法定位到问题，此外这里是旧代码不敢动逻辑，加上原来切换地图也是重新实例化map，与页面reload效果一致
          所以直接获取完整的url并且修改url中的maptype
          */
        if (this.$route.query.shareId) {
          let url = window.location.href
          url = url.replace(/maptype=\d+/, 'maptype=' + this.mapType)
          window.location.href = url
          window.location.reload()
        }
        // this.switchMapSatellite()
        // 切换地图需要先移除，不能通过setBaseLayer
        if (this.swdMapTalk) {
          // 可能百度地图显示了路况
          if (this.swdMapTalk.getLayer('traffic')) {
            this.swdMapTalk.getLayer('traffic').hide()
          }
          this.showFlagTraffic = false
          // 移除聚合图层
          if (this.swdMapTalk.getLayer('cluster')) {
            console.log('移除')
            this.carMarkerMap = null
            this.carMarkerMap = {}
            this.swdMapTalk.removeLayer('cluster')
          }
          if (this.carsLayer) {
            console.log('移除carsLayer')
            this.swdMapTalk.removeLayer(this.carsLayer) // 清除不需要点聚合的车辆图层
            this.carsLayer = null
          }
          this.swdMapTalk.removeLayer(this.vecLayer) // 清除轨迹图层
          this.swdMapTalk.remove()
          this.swdMapTalk = null
          let panel = document.getElementById('mapPanel')
          // 防止remove不干净
          if (panel.firstChild) {
            panel.removeChild(panel.firstChild)
          }
          this.nowShowSatellite = false
          this.showSearch = false
          // 线路管理切换地图后自动打开地址搜索
          this.isLineSettingMode &&
            this.$nextTick(() => {
              this.showSearchFun()
            })

          this.showMapLayer(this.mapType)
        }
      }
      this.hideMapTypeMenu()
    },
    showMapTypeMenu() {
      this.judgeMapTypeMenuDisplay = true
    },
    hideMapTypeMenu() {
      this.judgeMapTypeMenuDisplay = false
      console.log('this.judgeMapTypeMenuDisplay', this.judgeMapTypeMenuDisplay)
    },
    // 显示不同类型地图
    showMapLayer(maptype) {
      const self = this
      switch (parseInt(maptype)) {
        case 1: //百度地图
          self.showBaiduMap()
          break
        case 2: //谷歌地图
          self.showGoogleMap()
          break
        case 3: //必应地图
          self.showBingMap()
          break
        case 4: //Mapbox
          self.showMapbox()
          break
        case 5: //Mapbox
          self.showOSMmap()
          break
      }
      /* if (self.swdMapTalk) {
        self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
      } */
      self.showCars()
    },
    // 显示百度地图
    showBaiduMap: function() {
      const self = this
      self.swdMapTalk = new maptalks.Map('mapPanel', {
        center: self.initUserLocation,
        zoom: self.nowZoom,
        // centerCross : true,
        /* zoomControl: {
          position: 'bottom-right',
          slider: false,
          zoomLevel: false
        }, */
        maxExtent: new maptalks.Extent(-180, -80, 180, 80),
        scaleControl: true,
        spatialReference: {
          projection: 'baidu'
        },
        attribution: {
          content: '&copy;' + self.$t('A7xrOFhybj9Js9R7Oulub')
        },
        baseLayer: new maptalks.GroupTileLayer(self.$t('E2cJxJ0qcSBiNzRMKbaj0'), [
          new maptalks.TileLayer(self.$t('A7xrOFhybj9Js9R7Oulub'), {
            visible: !self.nowShowSatellite,
            urlTemplate: 'https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&s=1&styles=pl&scaler=1&p=1&s=1&udt=' + self.udt, // udt=20190410
            subdomains: ['0', '1', '2']
          }),
          new maptalks.TileLayer(self.$t('TrFNV69OZRAH0okN4sumS'), {
            visible: self.nowShowSatellite,
            urlTemplate:
              'https://ss{s}.bdstatic.com/8bo_dTSlR1gBo1vgoIiO_jowehsv/starpic/?qt=satepc&s=1&u=x={x};y={y};z={z};v=009;type=sate&fm=46&app=webearth2&v=009',
            subdomains: ['0', '1', '2', '3']
          })
        ]),
        layers: [
          new maptalks.TileLayer('traffic', {
            id: 'traffic',
            visible: false,
            urlTemplate: 'https://its.map.baidu.com/traffic/TrafficTileService?x={x}&y={y}&s=1&level={z}&time=' + self.timestamp + '&v=017'
          })
        ]
      })
      self.swdMapTalk.setMinZoom(4) //限制最小缩放比例
      self.swdMapTalk.setMaxZoom(20) //限制最大缩放比例
      self.zoomEventListen()
      self.swdMapTalk.on('click', function(param) {
        // console.log(param)
      })
    },
    // 显示谷歌地图
    showGoogleMap: function() {
      const self = this
      let site = ''
      if (window.location.host.indexOf('gpsnow.net') != -1 || window.location.host.indexOf('localhost') != -1) {
        site = 'CN'
      } else {
        site = 'EN'
      }
      let langParam = 'EN'
      if (this.lang == 'cn') {
        langParam = 'zh-CN'
      } else if (this.lang == 'tw') {
        langParam = 'zh-TW'
      }
      self.swdMapTalk = new maptalks.Map('mapPanel', {
        center: self.initUserLocation,
        zoom: self.nowZoom,
        maxExtent: new maptalks.Extent(-180, -80, 180, 80),
        /* zoomControl: {
          position: 'bottom-right',
          slider: false,
          zoomLevel: false
        }, */
        scaleControl: true,
        attribution: {
          content:
            '<span> ' +
            self.$t('erhrhxfWEGsfdgeergdfs') +
            ' &copy;' +
            new Date().getFullYear() +
            ' GS(2011)6020 Google | </span>' +
            '<a href="https://www.google.com/intl/zh-CN_US/help/terms_maps.html" style="color: rgb(0, 0, 0);text-decoration: none; cursor: pointer;">' +
            self.$t('sdfghESDGdgeerREFgdfs') +
            '</a>'
        },
        baseLayer: new maptalks.GroupTileLayer(self.$t('E2cJxJ0qcSBiNzRMKbaj0'), [
          new maptalks.TileLayer(self.$t('2yf5VqhJTX_NvDljHWexs'), {
            visible: !self.nowShowSatellite,
            urlTemplate: 'https://mt{s}.google.com/maps/vt?lyrs=m&hl=' + langParam + '&gl=' + site + '&x={x}&y={y}&z={z}',
            // urlTemplate: 'https://gac-geo.googlecnapps.cn/maps/vt?lyrs=m&hl=' + langParam + '&gl=' + site + '&x={x}&y={y}&z={z}',
            subdomains: ['0', '1', '2', '3']
          }) /* ,
          new maptalks.TileLayer('谷歌卫星', {
            visible: self.nowShowSatellite,
            urlTemplate: 'https://mt{s}.google.cn/maps/vt?lyrs=s%40781&hl=' + langParam + '&gl=' + site + '&x={x}&y={y}&z={z}',
            subdomains: ['0', '1', '2', '3']
          }) */
        ]),
        layers: [
          new maptalks.TileLayer('addplayer', {
            visible: self.nowShowSatellite,
            // urlTemplate: 'https://gac-geo.googlecnapps.cn/maps/vt?lyrs=y&hl=' + langParam + '&gl=' + site + '&x={x}&y={y}&z={z}',
            urlTemplate: 'https://mt{s}.google.com/maps/vt?lyrs=y&hl=' + langParam + '&gl=' + site + '&x={x}&y={y}&z={z}',
            subdomains: ['0', '1', '2', '3']
          })
        ]
      })
      self.swdMapTalk.setMinZoom(3)
      self.swdMapTalk.setMaxZoom(20)
      console.log(self.swdMapTalk)
      self.zoomEventListen()
    },
    // 显示必应地图
    showBingMap: function() {
      const self = this
      self.swdMapTalk = new maptalks.Map('mapPanel', {
        center: self.initUserLocation,
        zoom: self.nowZoom,
        maxExtent: new maptalks.Extent(-180, -80, 180, 80),
        /* zoomControl: {
          position: 'bottom-right',
          slider: false,
          zoomLevel: false
        }, */
        scaleControl: true,
        attribution: {
          content: '&copy;' + self.$t('rc-VB0jPoY98SMUVoUe7C')
        },
        baseLayer: new maptalks.GroupTileLayer(self.$t('rc-VB0jPoY98SMUVoUe7C'), [
          new maptalks.TileLayer('必应电子地图', {
            visible: !self.nowShowSatellite,
            id: 'bingBase',
            urlTemplate: function(x, y, z, domain) {
              //通过x,y,z计算quadkey和url
              var quadKey = self.quadTreeChange(x, y, z)
              // return 'http://r'+domain+'.tiles.ditu.live.com/tiles/r'+quadKey+'.png?g=100&mkt=zh-cn&callback=func'  //国内url 受跨域限无法使用
              return 'http://ecn.t' + domain + '.tiles.virtualearth.net/tiles/r' + quadKey + '.jpeg?g=7863&mkt=zh-CN&shading=hill'
            },
            subdomains: ['0', '1', '2', '3']
          }),
          new maptalks.TileLayer('必应卫星地图', {
            id: 'bingSatellite',
            visible: self.nowShowSatellite,
            urlTemplate: function(x, y, z, domain) {
              //通过x,y,z计算quadkey和url
              var quadKey = self.quadTreeChange(x, y, z)
              return 'http://ecn.t' + domain + '.tiles.virtualearth.net/tiles/a' + quadKey + '.jpeg?g=7863'
            },
            subdomains: ['0', '1', '2', '3']
          })
        ])
      })
      self.swdMapTalk.setMinZoom(3) //限制最小缩放比例
      self.swdMapTalk.setMaxZoom(20)
      self.zoomEventListen()
    },
    // 显示Mapbox
    showMapbox: function() {
      const self = this
      this.mapboxMap = self.swdMapTalk = new maptalks.Map('mapPanel', {
        center: self.initUserLocation,
        maxExtent: new maptalks.Extent(-180, -80, 180, 80),
        zoom: self.nowZoom,
        scaleControl: true,
        baseLayer: new MapboxglLayer('mapbox', {
          glOptions: {
            style: self.nowShowSatellite ? 'mapbox://styles/mapbox/satellite-v9' : 'mapbox://styles/mapbox/streets-v11',
            logoPosition: 'top-left'
          }
        })
      })
      self.swdMapTalk.setMinZoom(3)
      self.swdMapTalk.setMaxZoom(20)
      self.zoomEventListen()
    },
    // 显示OSM
    showOSMmap: function() {
      const self = this
      this.swdMapTalk = new maptalks.Map('mapPanel', {
        center: self.initUserLocation,
        maxExtent: new maptalks.Extent(-180, -80, 180, 80),
        zoom: self.nowZoom,
        /* zoomControl: {
          position: 'bottom-right',
          slider: false,
          zoomLevel: false
        }, */
        scaleControl: true,
        attribution: {
          content: 'maptalks'
        },
        baseLayer: new maptalks.GroupTileLayer('OSM', [
          new maptalks.TileLayer('base', {
            visible: !self.nowShowSatellite,
            // urlTemplate: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png',
            urlTemplate: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            // urlTemplate: 'https://{s}.tile.thunderforest.com/transport/{z}/{x}/{y}.png?apikey=6170aad10dfd42a38d4d8c709a536f38',
            // urlTemplate: 'https://tile-{s}.openstreetmap.fr/hot/{z}/{x}/{y}.png',

            subdomains: ['a', 'b', 'c'],
            attribution: '&copy; <a href="http://osm.org">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/">CARTO</a>'
          }),
          new maptalks.TileLayer(self.$t('r9Z0vOw35FkxLAXFabw2R'), {
            visible: self.nowShowSatellite,
            urlTemplate: 'https://mt{s}.google.cn/maps/vt?lyrs=s%40781&hl=zh-CN&gl=CN&x={x}&y={y}&z={z}',
            subdomains: ['0', '1', '2', '3']
          })
        ])
      })
      self.swdMapTalk.setMinZoom(3)
      self.swdMapTalk.setMaxZoom(20)
      self.zoomEventListen()
    },
    // 计算必应地图URL
    quadTreeChange: function(x, y, z) {
      // xyz转quadKeys算法
      // 转换规则：10进制的参数x，y值要先转为2进制，再将y，x从右到左进行位数拼接，例如y=100,x=11,则补齐0后x=011，拼接为100101。
      // 再把结果值转换为4进制。4进制的结果长度小于z值需要在前面补0
      let x2 = x.toString(2) //10进制转换为2进制
      let y2 = y.toString(2)
      let zeroMap = ['0', '00', '000', '0000', '00000', '000000', '0000000', '00000000', '000000000', '0000000000', '00000000000']
      // 位数不相等则在前面补齐0
      if (x2.length > y2.length) {
        let length = x2.length - y2.length
        y2 = zeroMap[length - 1] + y2
      } else if (x2.length < y2.length) {
        let length = y2.length - x2.length
        x2 = zeroMap[length - 1] + x2
      }
      let val = ''
      for (let i = 0; i < y2.length; i++) {
        val += y2[i] + x2[i]
      }
      let value4 = parseInt(val, 2).toString(4) //转为10进制再转为四进制
      // 4进制数据的长度小于z值的话要在前面补0
      if (value4.length < z) {
        value4 = zeroMap[z - value4.length - 1] + value4
      }
      return value4
    },
    // 计算多个点的中心点
    getCenterPoint: function(data) {
      let lng = 0.0
      let lat = 0.0
      for (let i = 0; i < data.length; i++) {
        lng = lng + parseFloat(data[i].lonc)
        lat = lat + parseFloat(data[i].latc)
      }
      lng = lng / data.length
      lat = lat / data.length
      return { lon: lng, lat: lat }
    },
    // 显示车辆图标以及所在位置
    showSingleCar(car) {
      if (car) {
        const self = this
        let icon = '/images/map/machine/3/green_75.png'

        if (!isDev) {
          icon = `${packagePath}` + icon
        }

        let _marker = new maptalks.Marker([car.lonc, car.latc], {
          visible: true,
          editable: true,
          cursor: 'pointer',
          shadowBlur: 0,
          shadowColor: 'black',
          draggable: false,
          dragShadow: false, // display a shadow during dragging
          drawOnAxis: null, // force dragging stick on a axis, can be: x, y
          symbol: {
            markerFile: icon,
            carId: car.carId,
            markerDx: 0,
            markerDy: 16
          }
        })

        if (!self.vecLayer) {
          self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
        }
        _marker.addTo(self.vecLayer)

        this.setCenter(car.lonc, car.latc, 17)
      } else {
        this.setCenter(0, 0, 17)
      }
    },
    // 显示车标逻辑
    showCars: function() {
      const self = this
      if (!self.carsMapPoints.length) {
        return
      }
      console.log('showCARA', this.carsMapPoints)
      let markers = []
      let addressPoints = JSON.parse(JSON.stringify(self.carsMapPoints)) || []
      if (!self.pointFlag && self.swdMapTalk && !self.carsLayer) {
        self.carsLayer = null
        // 聚合开关已关 图层用carsLayer
        if (!self.vecLayer) {
          self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
        }
        self.carsLayer = new maptalks.VectorLayer('carsLayer').addTo(self.swdMapTalk)
        console.log('创建carsLayer')
      }
      for (let i = 0; i < addressPoints.length; i++) {
        let a = addressPoints[i]
        /* eslint-enable */
        let _car = JSON.parse(JSON.stringify(self.carsMap[a[2]])) || []
        let icon = ''
        // 车辆图标
        if (_car.carStatus) {
          if (_car.carStatus.online) {
            if (_car.carStatus.speed === 0) {
              //  怠速状态
              const idleTime = parseExData(_car.carStatus.exData, 'idleTime') // 怠速时间
              if (idleTime) {
                icon = getIconByState(_car, 'orange')
              } else {
                // 静止状态
                icon = getIconByState(_car, 'blue')
              }
            } else {
              //运动状态
              let flag = true // 超速标记  true：正常运动  false： 超速运动
              // 超速报警打开
              if (_car.carStatus.alarm) {
                let _str = _car.carStatus.alarm
                let _arr = []
                if (_car.carStatus.alarm.indexOf(':') === 0) {
                  _str = _str.substring(1, _str.length)
                  _arr = _str.split(':')
                }
                for (let i = 0; i < _arr.length; i++) {
                  let item = _arr[i].split(',')[0]
                  if (item === 5) {
                    flag = false
                    break
                  }
                }
              } else {
                if (_car.carStatus.speed >= 120) {
                  flag = false
                }
              }
              if (flag) {
                icon = getIconByState(_car, 'green')
              } else {
                // 超速
                icon = getIconByState(_car, 'red')
              }
            }
          } else {
            icon = getIconByState(_car, 'white')
          }
        }
        if (!isDev) {
          icon = `${packagePath}` + icon
        }
        let _marker = ''
        if (self.carMarkerMap[_car.carId]) {
          // 更新车辆图标和位置
          _marker = self.carMarkerMap[_car.carId]
          self.updateMarkerPosition(_marker, a[0], a[1], icon, _car.carId)
          if (self.nowSelectMarkerObj && self.nowSelectMarkerObj.carId === _car.carId) {
            console.log('存在信息框')
            self.showCarInfoBox(_marker, _car)
            /* let isPointInMapView = self.isGetBounds(a[0], a[1])
            if (!isPointInMapView) {} */
            if (self.pageType === 'monitor') {
              // 监控页面需要判断弹窗是否打开
              let infoWindow = self.nowSelectMarkerObj.marker ? self.nowSelectMarkerObj.marker.getInfoWindow() : null
              if (infoWindow && infoWindow.isVisible() && self.isWindowCenterUser) {
                self.setCenter(a[0], a[1])
              }
            } else if (self.pageType === 'track') {
              // 实时跟踪一直锁定居中
              self.setCenter(a[0], a[1])
            }
          }
        } else {
          _marker = new maptalks.Marker([a[0], a[1]], {
            visible: true,
            editable: true,
            cursor: 'pointer',
            shadowBlur: 0,
            shadowColor: 'black',
            draggable: false,
            dragShadow: false, // display a shadow during dragging
            drawOnAxis: null, // force dragging stick on a axis, can be: x, y
            zIndex: 98,
            symbol: {
              markerFile: icon,
              carId: _car.carId,
              markerDx: 0,
              markerDy: 16
            }
          })
          _marker.removeInfoWindow()
          _marker.setInfoWindow({
            content: '<div class="map_machineNameBox" style="position:relative">' + _car.machineName + '</div>',
            single: false,
            autoPan: false,
            custom: true,
            dx: 0,
            dy: -8
          })
          self.carMarkerMap[_car.carId] = _marker
          self.bindInfoWindow(_marker, _car)
          // 点聚合开启，之前不存在的设备需显示到该图层
          if (self.showPoint && self.carsLayer) {
            _marker.addTo(self.carsLayer)
          }
          markers.push(_marker)
        }
        if (self.showLine && _car.carStatus && _car.carStatus.online) {
          // 显示锁定的状态的轨迹
          if (_car.locked) {
            let point = {
              x: _car.carStatus.lonc,
              y: _car.carStatus.latc,
              color: self.getColorBySpeed(_car.carStatus.speed)
            }
            if (!self.carTrailMap[_car.carId]) {
              let arr = []
              arr.push(point)
              self.carTrailMap[_car.carId] = arr
              let line = self.drawCarLine(arr, point.color)
              console.log(line)

              self.carTrailLineMap[_car.carId] = [line]
            } else {
              let lineArr = self.carTrailMap[_car.carId]
              let lastPoint = lineArr[lineArr.length - 1] // 取出最后一条
              if (lastPoint && lastPoint.color !== point.color) {
                // 如果颜色发生变化
                lineArr = [] // 先重置数组
                lineArr.push(lastPoint) // 然后把上一个加进去作为绘制新颜色的起点
              }
              lineArr.push(point) // 新点加入到数组中
              self.carTrailMap[_car.carId] = lineArr
              let line = self.drawCarLine(lineArr, point.color) // 生成新颜色的轨迹
              let arr = self.carTrailLineMap[_car.carId]
              arr.push(line)
              self.carTrailLineMap[_car.carId] = arr
            }
          }
        }
        _car = null
      }
      addressPoints = null
      // 聚合开关开启
      if (self.pointFlag && self.swdMapTalk && !self.swdMapTalk.getLayer('cluster')) {
        console.log('0000')
        let clusterLayer = new ClusterLayer('cluster', markers, {
          noClusterWithOneMarker: true,
          maxClusterZoom: 12,
          //"count" is an internal variable: marker count in the cluster.
          symbol: {
            markerType: 'ellipse',
            markerFill: {
              property: 'count',
              type: 'interval',
              stops: [
                [0, '#3F7EFD'],
                [20, '#3F7EFD'],
                [40, '#3F7EFD']
              ]
            },
            markerFillOpacity: 1,
            markerLineOpacity: 0.6,
            markerLineWidth: 10,
            markerLineColor: '#3F7EFD',
            markerWidth: {
              property: 'count',
              type: 'interval',
              stops: [
                [0, 40],
                [18, 60],
                [60, 80]
              ]
            },
            markerHeight: {
              property: 'count',
              type: 'interval',
              stops: [
                [0, 40],
                [18, 60],
                [60, 80]
              ]
            }
          },
          textSymbol: {
            textFaceName: '"microsoft yahei"',
            textFill: '#FFFFFF',
            textSize: 16,
            textDx: 0,
            textDy: 0
          },
          drawClusterText: true,
          geometryEvents: true,
          single: true
        })
        if (!self.vecLayer) {
          self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
        }
        self.swdMapTalk.addLayer(clusterLayer)
        self.swdMapTalk.on('click', function(e) {
          let res = clusterLayer.identify(e.coordinate)
          if (res && res.children && res.children.length) {
            let _center = res.children[0]._coordinates
            console.log('res', res)
            self.nowZoom += 4
            if (self.nowZoom > 20) {
              self.nowZoom = 20
            }
            if (res.children.length) {
              let points = []
              res.children.forEach(item => {
                points.push({ lonc: item._coordinates.x, latc: item._coordinates.y })
              })
              let center = self.getCenterPoint(points)
              _center = { x: center.lon, y: center.lat }
              // 50 台设备放到最大
              /* if (res.children.length <= 50) {
                self.nowZoom = 13
              } */
              console.log(self.nowZoom)
            }
            self.swdMapTalk.setCenter(_center)
            self.swdMapTalk.setZoom(self.nowZoom)
          }
        })
      }
      markers = null
      // console.timeEnd('showCars')
    },
    //更新标记点位置
    updateMarkerPosition: function(marker, lon, lat, icon, carId) {
      marker.setCoordinates({
        x: lon,
        y: lat
      })
      marker.setSymbol({
        markerFile: icon,
        carId: carId,
        markerDx: 0,
        markerDy: 16
      })
    },
    updateMarkerPositionAnimate(changeDir) {
      // this.trackingCarMarker.updateSymbol({
      //   markerFile: icon,
      //   carId: car.carId,
      //   markerDx: 0,
      //   markerDy: 16
      // })
      this.carMarkerAnimate(changeDir)
      // if (this.trackingIndex === -1) {
      //   this.carMarkerAnimate(changeDir)
      // }
    },
    carMarkerAnimate(changeDir) {
      // console.log(this.trackingPoints, this.trackingIndex, 'this.trackingIndex')
      // this.trackingIndex++
      let marker = this.trackingCarMarker
      let trackingPoints = this.trackingPoints
      let current = trackingPoints[trackingPoints.length - 2]
      let next = trackingPoints[trackingPoints.length - 1]
      // console.log(this.trackingIndex, this.trackingIndex + 1, next)
      if (!next) {
        return
      }
      let duration = next.currentTime - current.currentTime
      marker.setCoordinates([current.carStatus.lonc, current.carStatus.latc])
      this.setCenter(current.carStatus.lonc, current.carStatus.latc)
      let angle = this.getAngle(current.carStatus.lonc, current.carStatus.latc, next.carStatus.lonc, next.carStatus.latc)
      let dx = subtract(next.carStatus.lonc, current.carStatus.lonc)
      let dy = subtract(next.carStatus.latc, current.carStatus.latc)
      // console.log();

      marker.updateSymbol({
        markerFile: current.icon,
        carId: current.carId,
        markerDx: 0,
        markerDy: 16
        // markerRotation: changeDir && !isNaN(angle) ? 360 - angle.toFixed(4) : null // marker rotation in degree, clock-wise
      })
      if (changeDir && !isNaN(angle)) {
        marker.updateSymbol({
          markerRotation: 360 - angle.toFixed(4) // marker rotation in degree, clock-wise
        })
      }
      if (this.markerAnimatePlayer) {
        this.markerAnimatePlayer.finish()
      }
      this.markerAnimatePlayer = marker.animate(
        {
          translate: [dx, dy]
        },
        {
          duration: duration + 50,
          easing: 'linear',
          //let map focus on the marker
          focus: false
        },
        frame => {
          // console.log(frame.state.playState)
          if (frame.state.playState === 'finished') {
            // console.log('finished')
            // this.carMarkerAnimate(changeDir)
          }
        }
      )
      let line = new maptalks.LineString(
        [
          [current.carStatus.lonc, current.carStatus.latc],
          [next.carStatus.lonc, next.carStatus.latc]
        ],
        {
          symbol: {
            lineColor: current.speedColor,
            lineWidth: 6,
            lineJoin: 'round', //miter, round, bevel
            lineCap: 'round', //butt, round, square
            lineDasharray: null, //dasharray, e.g. [10, 5, 5]
            lineOpacity: 1
          }
        }
      ).addTo(this.vecLayer)
      line.hide()
      line.animateShow(
        {
          duration: duration + 50,
          easing: 'linear'
        },
        function(frame) {
          if (frame.state.playState === 'finished') {
            // console.log('finished')
          }
        }
      )
    },
    // 绑定信息框，添加点击事件
    bindInfoWindow: function(marker, car) {
      const self = this
      marker.on('click', async function() {
        if (localStorage.getItem('MachineName') === 'true') {
          // 设备名称在车辆信息框切换时回显
          if (self.lasetSelectNameMarkerObj.carId !== '' && self.lasetSelectNameMarkerObj.carId !== car.carId) {
            self.carMarkerMap[self.lasetSelectNameMarkerObj.carId].removeInfoWindow()
            self.carMarkerMap[self.lasetSelectNameMarkerObj.carId].setInfoWindow({
              content: '<div class="map_machineNameBox" style="position:relative">' + self.carsMap[self.lasetSelectNameMarkerObj.carId].machineName + '</div>',
              single: false,
              autoPan: false,
              custom: true,
              dx: 0,
              dy: -8
            })
            self.carMarkerMap[self.lasetSelectNameMarkerObj.carId].openInfoWindow()
          }
          self.lasetSelectNameMarkerObj.carId = car.carId
        }
        let marker = self.carMarkerMap[car.carId]
        console.log(car)
        let expiredType = -1
        if (car.serviceState == '1') {
          let _now = new Date(new Date().Format('yyyy-MM-dd')).getTime()
          if (new Date(car.platformTime).getTime() <= _now) {
            expiredType = 1 // 平台到期
          } else {
            expiredType = 2 // 用户到期
          }
        }
        self.$emit('choseCarFromMap', {
          car: car
        })
        if ((self.userType === 2 || self.userType === 5) && car.serviceState == '1') {
          self.showExpriedDialog(car)
        } else if (car.serviceState == '1' && expiredType != -1) {
          self.showExpriedDialog(car, expiredType)
        } else {
          if (expiredType === 2) {
            self.getServiceInfoByCar(car)
          }
          self.showCarInfoBox(marker, car) //显示设备信息框
        }
        // self.leftCarListSelect(car.carId) //联动选中左边车辆列表
      })
      if (self.targetCarId !== -1 && self.targetCarId === car.carId && car.serviceState != '1') {
        self.showCarInfoBox(marker, car) //显示设备信息框
        self.initUserLocation = [car.carStatus.lonc, car.carStatus.latc]
        self.swdMapTalk.setCenter(this.initUserLocation)
      }
      /* if (car.carId === self.targetCarId) {
        console.log('选中车辆：')
        console.log(self.carsMap[car.carId])
      } */
    },
    // 显示信息框
    showCarInfoBox: async function(marker, car) {
      const self = this
      if (!self.nowSelectMarkerObj.carId || (self.nowSelectMarkerObj.carId && self.nowSelectMarkerObj.carId !== car.carId)) {
        marker.removeInfoWindow()
        let infoBoxWidth = 410 //监控页面 宽度300
        let htmlStr = await self.getDetailCarTip(car.carId, car)
        marker.setInfoWindow({
          width: infoBoxWidth,
          content: htmlStr,
          single: true,
          autoPan: false,
          custom: false,
          dx: 0,
          dy: 4
        })
        self.nowSelectMarkerObj = {
          marker: marker,
          carId: car.carId
        }
        // 追踪页面非全屏模式不自动开启信息窗口
        self.trackPageScreenFull && marker.openInfoWindow()
        // marker.openInfoWindow()
        self.bindEventInfo() // 绑定弹窗里的相关点击事件
        // 先生成弹框再地址解析
        self.carInfoLoadAddress(marker, car)
      } else {
        let infoWindow = marker.getInfoWindow()
        // console.log('车辆信息窗口内容', infoWindow.getContent())
        // 实时视频的追踪小窗口，车辆信息弹窗隐藏状态下也需要更新地址 trackPageScreenFull默认为true,小窗状态为false
        //  if (infoWindow && (infoWindow.isVisible()|| !self.trackPageScreenFull )) {
        if (infoWindow && infoWindow.isVisible()) {
          self.getDetailCarTip(car.carId, car).then(res => {
            infoWindow.setContent(res)
            self.bindEventInfo() // 绑定弹窗里的相关点击事件
            // 先生成弹框再地址解析
            self.carInfoLoadAddress(marker, car)
          })
        }
      }
    },
    onMarkerClick: function(e) {
      console.log(e)
    },
    // 显示已过期弹窗提示
    showExpriedDialog(car, expiredType) {
      let self = this
      getParentUserInfoByCar({
        carId: car.carId
      }).then(res => {
        if (res.ret === 1) {
          this.serviceData = res.data
          if (this.serviceData.imageURL) {
            this.serviceData.imageURL = '../..' + _api + '/image/getImage.do?imageId=' + this.serviceData.imageURL + '&token=' + this.$cookies.get('token')
          } else {
            if (!this.serviceData.imageURL) {
              this.serviceData.imageURL = publicUserpic
            }
          }
          this.timer = setTimeout(() => {
            self.showParentService = true
            clearTimeout(self.timer)
          }, 500)
        }
      })
    },
    // 车辆弹窗信息拼接
    async getDetailCarTip(carId, carData) {
      // console.log('----改弹窗视频---', carData)
      // 显示video标签
      let deviceType = carData.deviceType // 0是普通设备，1是视频设备
      let _showVedio = deviceType == 1 ? '' : 'hide'
      //200--V6设备  201--V6S设备，有对讲功能
      let _showIntercom = [201, 202].includes(carData.machineType) ? '' : 'hide'
      const self = this
      let imgs = self.infoImages
      // let car = self.carsMap[carId]
      let car = carData
      //
      // console.clear()
      // console.log(self.carsMap)
      // console.log(self.carsMap[carId])
      // console.log(JSON.parse(JSON.stringify(car)))
      if (!this.carImageUrl) {
        this.carInfoLoadImage(carData)
      }
      let _carStatus = car.carStatus
      let _color = car._class
      let _statusTxt = ''
      let _showTxt = ''
      let expiredType = -1
      let showRenew = 'hide'
      let showOperate = ''
      let trackContainer = ''
      let imei = car.imei
      if (_carStatus) {
        if (_carStatus.online === 0) {
          // _color = 'yellow'
          _statusTxt = self.$t('dtFn22xfEx789uFKvaG_n')
          _showTxt = DiffTimeSampleFormateUtil(Date.parse(new Date()) - Date.parse(new Date(_carStatus.heartTime)))
        } else if (_carStatus.online === 1) {
          if (_carStatus.speed === 0) {
            const idleTime = parseExData(_carStatus.exData, 'idleTime') // 怠速时间
            if (idleTime) {
              _statusTxt = self.$t('Bad9L51R3bTOzbZK96v4M')
              _showTxt = DiffTimeSampleFormateUtil(Date.parse(new Date()) - Date.parse(new Date(idleTime)))
            } else {
              // _color = 'blue'
              _statusTxt = self.$t('goF8T8dHU6gao6PljrtNi')
              _showTxt = DiffTimeSampleFormateUtil(Date.parse(new Date()) - Date.parse(new Date(_carStatus.staticTime)))
            }
          } else if (_carStatus.speed > 0) {
            // _color = 'green'
            _statusTxt = self.$t('OCvfOWyVHgRIOLEA021Kl')
            _showTxt = MeterToKmMi(_carStatus.speed * 1000) + self.miledgeUnitEN + '/h'
          }
        }
      }
      if (car.serviceState == '1') {
        // _color = 'red'
        _statusTxt = self.$t('Jet1u7xx2NupyskORg_e-')
        let startTime = ''
        if (car.serviceTime) {
          startTime = car.serviceTime
        }
        if (car.platformTime) {
          startTime = car.platformTime
        }
        if (car.platformTime && car.serviceTime) {
          if (new Date(car.serviceTime).getTime() < new Date(car.platformTime).getTime()) {
            startTime = car.serviceTime
          } else {
            startTime = car.platformTime
          }
        }
        let _now = new Date(new Date().Format('yyyy-MM-dd')).getTime()
        if (new Date(car.platformTime).getTime() <= _now) {
          expiredType = 1 // 平台到期
        } else {
          expiredType = 2 // 用户到期
          showRenew = ''
          showOperate = 'hide'
        }
        let diffTime = new Date().getTime() - new Date(startTime).getTime()
        let diffHour = Math.abs((diffTime / 1000 / 60 / 60) % 24)
        let diffDay = Math.abs(diffTime / 1000 / 60 / 60 / 24)
        if (diffDay > 1) {
          _showTxt = parseInt(diffDay) + self.$t('lg._day')
        } else {
          _showTxt = parseInt(diffHour) + self.$t('NprpRhvbYE9KLFSrs1Ut3')
        }
      }
      if (self.pageType !== 'monitor') {
        showOperate = 'hide'
      }
      let _pointTime = timeConvert(_carStatus.pointTime, 'local')
      let _heartTime = timeConvert(_carStatus.heartTime, 'local')
      // 只有中文才显示 类型：有线/无线
      let _wireTyp = car.wireless ? self.$t('K_s5NFaeLyxrujarrzfMk') : self.$t('XoaUHxf1yCHUm8G-ttmOA')
      let machineInfo = matchMachineArr(car.machineType) //获取当前设备型号的信息
      let showAcc = ''
      let _engine = ''
      let _accTime = ''
      // 根据设备型号判断是否显示acc -1没有 0是关 1是开
      //acc关闭时对讲开始前需要弹popover提示用户
      let accClose = false
      if (_carStatus.accStatus > -1) {
        // 判断ACC状态
        if (_carStatus.accStatus === 0) {
          accClose = true
          _engine = this.$t('dMl02a_mb9XIn8CyX3A_D')
        } else {
          accClose = false
          _engine = this.$t('oeP8MwRcMAnrw4rbs7DY3')
        }
        // ACC持续时间判断
        if (_carStatus.online === 1 && _carStatus.accTime) {
          let _showTxt = DiffTimeSampleFormateUtil(Date.parse(new Date()) - Date.parse(new Date(_carStatus.accTime)))
          _accTime = `（${_showTxt}）`
        }
      } else {
        showAcc = 'hide'
      }

      let showPower = 'hide'
      let _power
      if ([130, 177].indexOf(+car.machineType) !== -1) {
        _power = parseExData(_carStatus.exData, 'vehiclePowerPercent')
      } else {
        _power = parseExData(_carStatus.exData, 'power')
      }
      _power = _power === '0%' ? '1%' : _power
      // 电量显示
      // if (car.wireless || car.machineType === 35 || +car.machineType === 130 || +car.machineType === 138 || +car.machineType === 177) {
      if (car.wireless || car.machineType === 35 || +car.machineType === 130 || +car.machineType === 138 || +car.machineType === 199) {
        showPower = '' // S100内置电池电量
      }
      // 单独处理S711L W18L
      if ([130, 208, 215].indexOf(+car.machineType) !== -1 && !_power) {
        showPower = 'hide'
      }
      // 是否正在充电（S16L）
      let showCharging = 'hide'
      const terminalChargingStatus = parseExData(_carStatus.exData, 'terminalChargingStatus')
      showCharging = terminalChargingStatus ? '' : 'hide'

      let showOv = 'hide'
      let _resOv = '' // 油量
      let _ov = parseExData(_carStatus.exData, 'ov')
      let _ov1 = parseExData(_carStatus.exData, 'ov1')
      let _ob = parseExData(_carStatus.exData, 'ob')
      let _ob1 = parseExData(_carStatus.exData, 'ob1')
      // 电阻
      let resistencia
      if (_ov || _ov1) {
        showOv = ''
        if (_ov) {
          _resOv = _resOv + _ov + 'L'
        }
        if (_ob) {
          _resOv = _resOv + ' (' + _ob + '%)'
        }
        resistencia = parseExData(_carStatus.exData, 'res') + 'Ω'
        if (_ov1) {
          _resOv = _resOv + (_resOv ? ', ' : '') + _ov1 + 'L'
          if (_ob1) {
            _resOv = _resOv + ' (' + _ob1 + '%)'
          }
        }
      }
      let _volt = parseExData(_carStatus.exData, 'v') // 电压
      let showVolt = _volt ? '' : 'hide'
      showVolt === 'hide' ? true : (_volt = _volt + 'V')
      // 湿度
      let showHumidity = 'hide'
      let _humidity = parseExData(_carStatus.exData, 'humidity')
      if (_humidity) {
        showHumidity = ''
        if (car.machineType === 208) {
          _humidity += '%'
        } else {
          _humidity += '℃'
        }
      }
      let _tempStr = []
      let _temp = parseExData(_carStatus.exData, 't') // 温度
      let _temp1 = parseExData(_carStatus.exData, 't1') // 温度1
      let _temp2 = parseExData(_carStatus.exData, 't2') // 温度2
      let _temp3 = parseExData(_carStatus.exData, 't3') // 温度3
      let showTemp = ''
      if (_temp || _temp1 || _temp2 || _temp3) {
        showTemp = ''
        if (_temp) {
          _tempStr.push(_temp + '°C')
        }
        if (_temp1) {
          _tempStr.push(_temp1 + '°C')
        }
        if (_temp2) {
          _tempStr.push(_temp2 + '°C')
        }
        if (_temp3) {
          _tempStr.push(_temp3 + '°C')
        }
        if (_tempStr.length) {
          _tempStr = _tempStr.join(' ')
        }
      } else {
        showTemp = 'hide'
      }
      if (this.lat !== _carStatus.lat || this.lon !== _carStatus.lon) {
        // 先生成弹窗再解析地址
        // this.currentAddress = 'loading...'
        this.currentAddress = this.$t('3DYXD-6GdyKqIdA4QkIPq')
      }
      let _address = this.currentAddress
      if (carData.carStatus && carData.carStatus.lat && carData.carStatus.lon) {
        this.lat = carData.carStatus.lat
        this.lon = carData.carStatus.lon
      }
      let startTime = TimeStringToUTC(new Date().Format('yyyy-MM-dd') + ' 00:00:00')
      let endTime = TimeStringToUTC(new Date().Format('yyyy-MM-dd hh:mm:ss'))
      let mileageObj = await getMileageStaById({ carId: car.carId })
      let _mileage = 0
      let totalMileages = 0
      if (mileageObj.ret === 1 && mileageObj.data) {
        if (mileageObj.data.todayMileages) {
          _mileage = MeterToKmMi(parseFloat(mileageObj.data.todayMileages))
        }
        if (mileageObj.data.totalMileages) {
          totalMileages = MeterToKmMi(parseFloat(mileageObj.data.totalMileages))
        }
      }
      let _mileageTitle =
        this.$t('XsXoMT3CDfSMMxm_H8UGU') +
        ':  ' +
        _mileage +
        self.miledgeUnitEN +
        ',' +
        this.$t('wegasqASDE32446sgwe39') +
        ':  ' +
        totalMileages +
        self.miledgeUnitEN
      let _second = self.timefresh
      let _pointstr = ''
      let _line = ''
      if (self.lang === 'cn' || self.lang === 'tw') {
        _pointstr = car.carStatus && car.carStatus.pointTypeDes ? car.carStatus.pointTypeDes : self.$t('lg.pointedarray.' + car.carStatus.pointType)
        _line = 'line1'
      } else {
        _pointstr = 'GSM Update：' + _heartTime + '<br/>' + 'GPS Update：' + _pointTime
        _line = 'line2'
      }
      /// 油电
      let _showOilAndElectricity = 'hide'
      let oilAndEleText = self.$t('zU8zLd7T1sJBQIv0bzTu5')
      if (parseInt(_carStatus.status) === 12) {
        console.log(_carStatus)
        _showOilAndElectricity = ''
      }
      // TF卡
      let showTfCardType = 'hide'
      let showTfCard1Type = 'hide'
      let showTfCard2Type = 'hide'
      let isAbnormal = 'hidden'
      let abnormalText = ''
      let tfCardType = self.$t('zU8zLd7T1sJBQIv0bzTu5')
      let tf1CardType = self.$t('dashloadibzPA6vDcwblT')
      let tf2CardType = self.$t('dashloadibzPA6vDcwblT')
      let _memoryStatus = parseExData(_carStatus.exData, 'memoryStatus')
      let _memoryStatus1 = parseExData(_carStatus.exData, 'memoryStatus1')
      let _memoryStatus2 = parseExData(_carStatus.exData, 'memoryStatus2')
      let StorageFailureLocation = parseExData(_carStatus.exData, 'StorageFailureLocation')
      if (_memoryStatus) {
        showTfCardType = ''
        switch (_memoryStatus.value) {
          case 0:
            tfCardType = self.$t('dashloadibzPA6vDcwblT')
            break
          case 1:
            tfCardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
          case 2:
            tfCardType = self.$t('asdf2353WEEEW34463E11')
            break
          case 3:
            tfCardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
          case 4:
            tfCardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
          case 5:
            tfCardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
        }
      }
      if (_memoryStatus1) {
        showTfCard1Type = ''
        switch (_memoryStatus1.value) {
          case 0:
            tf1CardType = self.$t('dashloadibzPA6vDcwblT')
            break
          case 1:
            tf1CardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
          case 2:
            tf1CardType = self.$t('asdf2353WEEEW34463E11')
            break
          case 3:
            tf1CardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
          case 4:
            tf1CardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
          case 5:
            tf1CardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
        }
      }
      if (_memoryStatus2) {
        showTfCard2Type = ''
        switch (_memoryStatus2.value) {
          case 0:
            tf2CardType = self.$t('dashloadibzPA6vDcwblT')
            break
          case 1:
            tf2CardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
          case 2:
            tf2CardType = self.$t('asdf2353WEEEW34463E11')
            break
          case 3:
            tf2CardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
          case 4:
            tf2CardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
          case 5:
            tf2CardType = self.$t('dashloadi1BDxcwIKFkO2')
            isAbnormal = ''
            abnormalText = self.$t('asdf2353WEEEW34463E10')
            break
        }
      }

      // 权限
      const isVirtualUser = this.isVirtualUser ? 'hide' : ''
      let _showTrack = 'hide'
      let _showReplay = 'hide'
      let _showCom = 'hide'
      let _showFence = 'hide'
      let _showReport = 'hide'
      let _showShare = 'hide'
      let _showFenceadd = 0
      let _showFenceList = 'hide'
      let _showGpsMileage = 'hide'
      let _showTotalMileage = 'hide'
      let _showEnduranceMiledge = 'hide'
      let _showGear = 'hide'
      let _showChargingStatus = 'hide'
      let _showDignosis = 'hide'
      if ([130, 138, 177].indexOf(+car.machineType) !== -1) {
        _showGpsMileage = ''
        _showTotalMileage = ''
        _showEnduranceMiledge = ''
        _showGear = ''
        _showChargingStatus = ''
        _showDignosis = ''
      }
      self.$store.state.permissions.forEach(item => {
        if (item.perms === 'device:monitor:track') {
          _showTrack = ''
        }
        if (item.perms === 'device:monitor:playback') {
          _showReplay = ''
        }
        if (item.perms === 'device:remote:control') {
          _showCom = ''
        }
        if (item.perms === 'device:monitor:fence') {
          _showFence = ''
        }
        if (item.trans === 'Statistical_report' && item.type === 0) {
          _showReport = ''
        }
        if (item.perms === 'device:share:trajectory:add') {
          _showShare = ''
        }
        if (item.perms === 'fence:list,fence:info') {
          _showFenceList = ''
        }
        if (item.perms === 'fence:add') {
          _showFenceadd++
        }
        if (item.perms === 'fence:bind') {
          _showFenceadd++
        }
      })
      _showFenceadd = _showFenceadd == 2 ? '' : 'hide'
      // 中文语言状态下labeltxt显示icon，否则相反
      //  判断电量
      let powerNumber = _power ? Number(_power.substring(0, _power.length - 1)) : 0
      let batteryClass = '' // 电池容器类名
      let batteryBorderColor = '#42b041' // 电池边框颜色
      let powerNumberOffset = 7 // 电池值偏移
      switch (true) {
        case powerNumber > 100:
          powerNumber = 100 // 不能大于100
          batteryClass = 'green'
          batteryBorderColor = '#42b041'
          break
        case powerNumber > 50:
          batteryClass = 'green'
          batteryBorderColor = '#42b041'
          break
        case powerNumber > 20:
          batteryClass = 'yellow'
          batteryBorderColor = '#F79C00'
          break
        case powerNumber >= 0:
          batteryClass = 'red'
          batteryBorderColor = '#FF6648'
          break
      }
      // 电量值在容器内的偏移
      switch (true) {
        case powerNumber >= 100:
          powerNumberOffset = 4
          break
        case powerNumber >= 10:
          powerNumberOffset = 7
          break
        case powerNumber >= 0:
          powerNumberOffset = 9
          break
      }
      // 总里程
      let totalMileage = _carStatus.totalMileage && MeterToKmMi(parseFloat(_carStatus.totalMileage * 1000))
      _showTotalMileage = totalMileage ? '' : 'hide'
      // 续航里程
      let enduranceMiledge = MeterToKmMi(parseFloat(parseExData(_carStatus.exData, 'enduranceMileage')))
      _showEnduranceMiledge = parseExData(_carStatus.exData, 'enduranceMileage') ? '' : 'hide'
      // 档位
      let gear = parseExData(_carStatus.exData, 'gear')
      _showGear = gear ? '' : 'hide'
      // 充电状态
      let chargingStatus = parseExData(_carStatus.exData, 'chargingStatus')
      _showChargingStatus = chargingStatus ? '' : 'hide'
      let chargingStatusText
      switch (chargingStatus) {
        case -1:
          chargingStatusText = self.$t('gmiIB_fgQPFeHSPgUZ1OX')
          break
        case 1:
          chargingStatusText = self.$t('sjqasBHO4-7v_oE_oQhzf')
          break
        case 2:
          chargingStatusText = self.$t('rr1RNSaQZHRkg3YmwbXN3')
          break
        case 3:
          chargingStatusText = self.$t('3NqztzkemfaYubkgd94tg')
          break
        case 4:
          chargingStatusText = self.$t('gW9Sr4E4DO3vcWEXwSANp')
          break
        default:
          chargingStatusText = '-'
      }

      // ac和车门开关
      let showAC = 'hide'
      let showDoor = 'hide'
      if (_carStatus.exData && _carStatus.exData.length !== 0 && _carStatus.exData.indexOf('DOOR') !== -1) {
        showDoor = ''
      }
      if (_carStatus.exData && _carStatus.exData.length !== 0 && _carStatus.exData.indexOf('AC') !== -1) {
        showAC = ''
      }
      let _ac = parseExData(_carStatus.exData, 'AC') ? self.$t('oeP8MwRcMAnrw4rbs7DY3') : self.$t('dMl02a_mb9XIn8CyX3A_D')
      let _door = parseExData(_carStatus.exData, 'DOOR') ? self.$t('oeP8MwRcMAnrw4rbs7DY3') : self.$t('dMl02a_mb9XIn8CyX3A_D')

      let showWorkMode = 'hide'
      let _workModeText = ''
      let workMode = parseExData(_carStatus.exData, 'workMode')
      if (workMode) {
        // showWorkMode = ''
        showWorkMode = 'hide' // 先屏蔽，后续看需求再开放
        switch (Number(workMode)) {
          case 0:
            _workModeText = self.$t('rbAN2_LHXoCO3Rj9ifHHq')
            break
          case 1:
            _workModeText = self.$t('CEQE89HjKKDIM5xBDWBA8')
            break
          case 2:
            _workModeText = self.$t('OPHvVo3S-kkq8EJfg7gSZ')
            break
          case 3:
            _workModeText = self.$t('j-SWrpc1Vrf1S2wKVXsrg')
            break
          default:
            break
        }
      }
      // let battery = `
      // <div class="${batteryClass}-battery">
      //   <div class="battery-box">
      //     <div class="electricity" style="width:${powerNumber}%"}></div>
      //   </div>
      //   <div class="line"></div>
      // </div>`

      // 新电池样式
      // 变量：电池边框颜色、背景颜色、电量值、遮罩层偏移(值为1px==0 ~ 24px==100)、电池显隐、电量值偏移

      let batteryMask = Math.ceil((powerNumber / 100) * 23 + 1)

      let battery = ` <div class="${batteryClass}-battery">
      <div class="battery-bg"></div>
      <div class="battery-mask" style="left:${batteryMask}px;"></div>
      <svg
        class="battery-border"
        width="25px"
        height="14px"
        viewBox="0 0 25 14"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g fill="none" x="5">
          <g transform="translate(-407.000000, -31.000000)" fill-rule="nonzero" stroke="${batteryBorderColor}">
            <g transform="translate(408.000000, 32.000000)">
              <path
                d="M21.9732143,2.56034483 L20.6127232,2.56034483 C20.5524783,2.56041067 20.4923542,2.56577587 20.4330357,2.57637931 L20.4330357,2.06896552 C20.4330357,0.926307414 19.5136205,0 18.3794643,0 L2.05357143,0 C0.919415246,0 0,0.926307414 0,2.06896552 L0,9.93103448 C0,11.0736926 0.919415246,12 2.05357143,12 L18.3794643,12 C19.5136205,12 20.4330357,11.0736926 20.4330357,9.93103448 L20.4330357,9.42362069 C20.4923542,9.43422413 20.5524783,9.43958933 20.6127232,9.43965517 L21.9732143,9.43965517 C22.5402924,9.43965517 23,8.97650147 23,8.40517241 L23,3.59482759 C23,3.02349853 22.5402924,2.56034483 21.9732143,2.56034483 Z"
                id="形状"
              ></path>
            </g>
          </g>
        </g>
        <text class="battery-value" x="${powerNumberOffset}" y="10" fill="black">${powerNumber}</text>
      </svg>
    </div>`
      const outageSvgIcon = this.isCnLang ? this.$t('oilOutage') : getHtml(PcOutage).innerHTML

      let str = `<div id="carInfoPanel" class="${self.lang} ${this.carImageUrl ? 'hasImage' : ''}">
        <div class="topRow">
          <div><span class="name" title="${imei}">${
        car.machineName
      }</span><span class="txt1 ${_color}">${_statusTxt}</span><span class="txt2 ${_color}">(${_showTxt})</span></div>
          <div class="topRowIcons">
          <div class="videoIconContainer ${_showIntercom || isVirtualUser}">
           <img class="intercomIcon" src="${imgs[20]}"/>
           <div class="videoHoverTips">${this.$t('0re2Dz1xGLdRIiOtLWBC7')}</div>
         </div>
         <div class="videoIconContainer ${_showVedio || isVirtualUser}">
           <img class="videoIcon" src="${imgs[16]}"/>
           <div class="videoHoverTips">${self.$t('lg.limits.Video_Monitor')}</div>
         </div>
          <div class="outage ${_showOilAndElectricity}">${outageSvgIcon}</div>
          <div class="wrapelec ${showPower}">${battery}</div>
          <div class="charging ${showCharging}"><img src="${imgs[19]}"/></div>
          </div>
        </div>
        <div class="carInfo-container">
        <div class="wrapcol">
          <div class="col help">
            <div class="labeltxt">${self.$t('Ii_xMJ-aK06I6WjE_7RvK')}：</div>
            <div class="icon"><img class="sign" src="${imgs[1]}"/></div>${_heartTime}
            <div class="hoverinfoTips">${self.$t('LoX_Y5XcmGFXPrKocd3Vy')}
              <br>${self.$t('uHD_UGEfA88QbFYjH40Sk')}
              <br>${self.$t('CfXyIfQ7YrDKYGs6IcEjE')}
            </div>
          </div>
          <div class="col help" title="${self.$t('lg.type')}">
            <div class="labeltxt">${self.$t('lg.type')}：</div>
            <div class="icon"><img class="wireline" src="${imgs[2]}"/></div>
            ${_wireTyp}
          </div>
          <div class="col help">
            <div class="labeltxt">${self.$t('SKow9wO-zFrJ-bTY4iiNk')}：</div>
            <div class="icon"><img class="location" src="${imgs[3]}"/></div>
            ${_pointTime}
             <div class="hoverinfoTips h60 ${_line}">${_pointstr}</div>
          </div>
          <div class="col help" title="${_mileageTitle}">
            <div class="labeltxt">${self.$t('D-3CnkxFwEGtcITqH7fbB')}：</div>
            <div class="icon"><img class="mileage" src="${imgs[4]}"/></div>
            ${_mileage}${self.miledgeUnitCN}
          </div>
          <div class="col help ${showAcc}" title="ACC">
            <div class="labeltxt">ACC：</div>
            <div class="icon"><img class="acc" src="${imgs[5]}"/></div>
            ${_engine}${_accTime}
          </div>
          <div class="col help ${showOv}" title="${resistencia}">
            <div class="labeltxt">${self.$t('H4NX8-4EhG5zHLk1ytXkp')}：</div>
            <div class="icon"><img class="oil" src="${imgs[6]}"/></div>
            ${_resOv}
          </div>
          <div class="col help ${showTemp}" title="${self.$t('hs24f25MR-Kkr7mTiNgrI')}">
            <div class="labeltxt">${self.$t('hs24f25MR-Kkr7mTiNgrI')}：</div>
            <div class="icon"><img class="temp" src="${imgs[7]}"/></div>${_tempStr}</div>
          <div class="col help ${showVolt}" title="${self.$t('n_gGRUBDnHrrFFZNMBWQF')}">
            <div class="labeltxt">${self.$t('n_gGRUBDnHrrFFZNMBWQF')}：</div>
            <div class="icon"><img class="voltage" src="${imgs[8]}"/></div>${_volt}
          </div>
          <div class="col help ${showHumidity}" title="${self.$t('ewfWEdsf3NsKPX2toJVeL')}">
            <div class="labeltxt">${self.$t('ewfWEdsf3NsKPX2toJVeL')}：</div>
            <div class="icon"><img class="voltage" src="${imgs[10]}"/></div>${_humidity}
          </div>
          <div class="col help ${_showOilAndElectricity}" title="${self.$t('g2345sdfg6t3300462rgg')}">
            <div class="labeltxt">${self.$t('g2345sdfg6t3300462rgg')}：</div>
            <div class="icon"><img class="voltage" src="${imgs[11]}"/></div>${oilAndEleText}
          </div>
          <div class="col help ${_showTotalMileage}" title="${self.$t('wegasqASDE32446sgwe39')}">
            <div class="labeltxt">${self.$t('wegasqASDE32446sgwe39')}：</div>
            <div class="icon"><img class="mileage" src="${imgs[12]}"/></div>${totalMileage}${self.miledgeUnitCN}
          </div>
          <div class="col help ${_showEnduranceMiledge}" title="${self.$t('avn4Z2zY-8AmiFArM_Kzi')}">
            <div class="labeltxt">${self.$t('avn4Z2zY-8AmiFArM_Kzi')}：</div>
            <div class="icon"><img class="mileage" src="${imgs[13]}"/></div>${enduranceMiledge}${self.miledgeUnitCN}
          </div>
          <div class="col help ${_showGear}" title="${self.$t('1FnxxFoT2tdJpyAUbkQ8d')}">
            <div class="labeltxt">${self.$t('1FnxxFoT2tdJpyAUbkQ8d')}：</div>
            <div class="icon"><img class="mileage" src="${imgs[14]}"/></div>${gear}&nbsp;${self.$t('dPrshwoE0R9HYtR_pqiyB')}
          </div>
          <div class="col help ${_showChargingStatus}" title="${self.$t('vZ_NmsWGw1TljsTvgtYSc')}">
            <div class="labeltxt">${self.$t('vZ_NmsWGw1TljsTvgtYSc')}：</div>
            <div class="icon"><img class="mileage" src="${imgs[15]}"/></div>${chargingStatusText}
          </div>
          <div class="col help ${showTfCardType}" title="${self.$t('asdf2353WEEEW34463E09')}">
            <div class="labeltxt">${self.$t('asdf2353WEEEW34463E09')}：</div>
            <div class="icon"><img class="voltage" src="${imgs[17]}"/></div>
            <span>${tfCardType}</span>
            <div class="icon2 ${isAbnormal}"><img class="voltage" src="${imgs[18]}" title=${abnormalText} / ></div>
          </div>
          <div class="col help ${showTfCard1Type}" title="TF1">
            <div class="labeltxt">TF1：</div>
            <div class="icon"><img class="voltage" src="${imgs[17]}"/></div>
            <span>${tf1CardType}</span>
            <div class="icon2 ${isAbnormal}"><img class="voltage" src="${imgs[18]}"/></div>
          </div>
          <div class="col help ${showTfCard2Type}" title="TF2">
            <div class="labeltxt">TF2：</div>
            <div class="icon"><img class="voltage" src="${imgs[17]}"/></div>
            <span>${tf2CardType}</span>
            <div class="icon2 ${isAbnormal}"><img class="voltage" src="${imgs[18]}"/></div>
          </div>
          <div class="col help ${showAC}" title="AC">
            <div class="labeltxt">AC：</div>
            <div class="icon"><img class="ac" src="${imgs[23]}" /></div>
              ${_ac}
          </div>
          <div class="col help ${showDoor}" title="${self.$t('4RrCaePSVwyb6dIgHJAqX')}">
            <div class="labeltxt">${self.$t('4RrCaePSVwyb6dIgHJAqX')}：</div>
            <div class="icon"><img class="door" src="${imgs[24]}" /></div>
            ${_door}
          </div>
          <div class="col help ${showWorkMode}" title="${self.$t('BYPkXF0WNPoQnCGOTnh8A')}">
            <div class="labeltxt">${self.$t('BYPkXF0WNPoQnCGOTnh8A')}：</div>
            <div class="icon"><img class="door" src="${imgs[24]}" /></div>
            ${_workModeText}
          </div>
        </div>
        <div class="image-container ${this.carImageUrl ? '' : 'hide'}" id="carImageContainer">
            <img  src="${this.carImageUrl}">
        </div>
        </div>
        <div class="address"><img class="addr" src="${imgs[9]}"/>${_address}<span style="display:${
        self.pageType === 'track' &&
        (carData.machineType === 60 || carData.machineType === 85 || carData.machineType === 101 || carData.machineType === 126 || carData.machineType === 127)
          ? 'none'
          : 'inline-block'
      }" class="second">(<span id="monitor_second">${_second}</span>S)</span></div>
        <div class="opt ${showOperate}">
          <div class="item detail" title="${self.lang === 'cn' ? '' : self.$t('d2rfxOdgIK8sx-aXn_UjT')}">
            <span class="spantxt">${self.$t('d2rfxOdgIK8sx-aXn_UjT')}</span>
          </div>
          <div class="item location ${_showTrack}" title="${self.lang === 'cn' ? '' : self.$t('2_GOBlwzPbyr5HQDEsUOK')}">
            <span class="spantxt">${self.$t('2_GOBlwzPbyr5HQDEsUOK')}</span>
          </div>
          <div class="item play ${_showReplay}" title="${self.lang === 'cn' ? '' : self.$t('FNZ9KgOe3_nZPHLr-gemV')}">
            <span class="spantxt">${self.$t('FNZ9KgOe3_nZPHLr-gemV')}</span>
          </div>
          <div class="item command ${_showCom}" title="${self.lang === 'cn' ? '' : self.$t('nG52x-1HCtQNlZdzdGke_')}">
            <span class="spantxt">${self.$t('nG52x-1HCtQNlZdzdGke_')}</span>
          </div>
          <div class="item fence ${_showFence}" title="${self.lang === 'cn' ? '' : self.$t('nuMj_JYBMK5JmtZA19Q7D')}">
            <span class="spantxt">${self.$t('nuMj_JYBMK5JmtZA19Q7D')}</span>
            <div class="drownFence">
              <div class="subItem fence1 ${_showFenceadd}" title="${self.lang === 'cn' ? '' : self.$t('sgH0wIb6CRMhrPErfzZNo')}">
              ${self.$t('sgH0wIb6CRMhrPErfzZNo')}</div>
              <div class="subItem fence2 ${_showFenceadd}" title="${self.lang === 'cn' ? '' : self.$t('YRsd0eFEx-XpEoHM4bQOf')}">
              ${self.$t('YRsd0eFEx-XpEoHM4bQOf')}</div>
              <div class="subItem fence4 ${_showFenceList}" title="${self.lang === 'cn' ? '' : self.$t('7vdkNJd4kRZP2sQMmyxpv')}">
              ${self.$t('7vdkNJd4kRZP2sQMmyxpv')}</div>
            </div>
          </div>
          <div class="item morebtn" title="${self.lang === 'cn' ? '' : self.$t('IikV2EDWehRNB_DuPyoAi')}">
            <span class="spantxt">${self.$t('IikV2EDWehRNB_DuPyoAi')}</span>
            <div class="drownMore">
              <div class="subItem street" title="${self.lang === 'cn' ? '' : self.$t('IJW7kt6UjfglE5oKPiJ0z')}">${self.$t('IJW7kt6UjfglE5oKPiJ0z')}</div>
              <div class="subItem analysis ${_showReport}" title="${self.lang === 'cn' ? '' : '统计'}">统计</div>
              <div class="subItem share ${_showShare}" title="${self.lang === 'cn' ? '' : self.$t('_LruCTL_GmFwkLYdKRGPL')}">${self.$t(
        '_LruCTL_GmFwkLYdKRGPL'
      )}</div>
              <div class="subItem diagnosis ${_showDignosis}" title="${self.lang === 'cn' ? '' : self.$t('PxYMk1w1YywfN127GyF_L')}">${self.$t(
        'PxYMk1w1YywfN127GyF_L'
      )}</div>
            </div>
          </div>
          <div class="item street" title="${self.$t('IJW7kt6UjfglE5oKPiJ0z')}"></div>
          <div class="item analysis ${_showReport}" title="${self.$t('MENG0IZDkTNnl3WoRl_p5')}"></div>
          <div class="item share ${_showShare}" title="${self.$t('_LruCTL_GmFwkLYdKRGPL')}"></div>
          <div class="item diagnosis ${_showDignosis}" title="${self.$t('PxYMk1w1YywfN127GyF_L')}"></div>
        </div>
        <div class="wrap-renew ${showRenew}">
          <div class="serviceName">您的经销商：<span>${self.serviceData.name}</span></div>
          <div class="optRew">${self.$t('8uvQGTTGGK4YHP6RctT_K')}</div>
        </div>
        <div class="intercom-popover-corner ${this.showIntercomPop ? '' : 'hide'}"></div>
        <div class="intercom-popover ${this.showIntercomPop ? '' : 'hide'}">
          <div class="popover-tips-container">
            <img src="${accClose ? imgs[22] : imgs[21]}" alt="" />
            <p>${accClose ? this.$t('FNSsRfB5Bxlf2Olm2Bw5M') : this.$t('Pg7k0mGxvOxvRWARJpzfg', [`"${carData.machineName}"`]) + '?'}</p>
          </div>
          <div class="popover-btn-container">
            <div class="popover-btn-text">${this.$t('lg.cancel')}</div>
            <div class="popover-btn-primary">${this.$t('lg.submit')}</div>
          </div>
        </div>  
      </div>`
      return str
    },
    bindEventInfo() {
      const self = this
      let car = self.carsMap[self.nowSelectMarkerObj.carId] || self.currentTrackCar
      $('#carInfoPanel .serviceName span') // 显示经销商
        .unbind()
        .click(function() {
          self.showParentService = true
        })
      $('#carInfoPanel .opt .share') // 分享
        .unbind()
        .click(function() {
          self.shareData = JSON.parse(JSON.stringify(self.carsMap[self.nowSelectMarkerObj.carId]))
          self.shareHistoryFlag = true
        })
      $('#carInfoPanel .opt .location') // 跟踪
        .unbind()
        .click(function() {
          // window.open('../../page/map.html?page=tailAfter&carId=' + car.carId + '&lang=' + self.lang, '_blank')
          const { href } = self.$router.resolve({
            name: 'Track',
            query: {
              carId: car.carId
            }
          })
          window.open(href, '_blank')
        })
      $('#carInfoPanel .drownFence .fence1') // 圆形
        .unbind()
        .click(function() {
          // 同时关闭信息弹窗
          if (self.nowSelectMarkerObj.marker) {
            self.nowSelectMarkerObj.marker.removeInfoWindow()
            self.nowSelectMarkerObj = {
              marker: null,
              carId: ''
            }
          }
          self.drawFenCarId = car.carId
          self.drawFence('Circle')
        })
      $('#carInfoPanel .drownFence .fence2') // 多边形
        .unbind()
        .click(function() {
          // 同时关闭信息弹窗
          if (self.nowSelectMarkerObj.marker) {
            self.nowSelectMarkerObj.marker.removeInfoWindow()
            self.nowSelectMarkerObj = {
              marker: null,
              carId: ''
            }
          }
          self.drawFenCarId = car.carId
          self.drawFence('Polygon')
        })
      $('#carInfoPanel .drownFence .fence3') // 行政区域
        .unbind()
        .click(function() {
          self.drawFenCarId = car.carId
          self.showAreaDialog()
        })
      $('#carInfoPanel .drownFence .fence4') // 查看围栏
        .unbind()
        .click(function() {
          self.fenceCarId = self.nowSelectMarkerObj.carId
          self.fenceCarImei = car.imei
          self.showFence = false
          self.switchShowFence()
        })
      $('#carInfoPanel .opt .play') // 回放
        .unbind()
        .click(function() {
          console.log('回放点击', car)
          const { href } = self.$router.resolve({
            name: 'History',
            query: {
              carId: car.carId,
              machineType: car.machineType,
              userId: car.userId,
              lat: car.carStatus.latc,
              lon: car.carStatus.lonc,
              lang: self.lang,
              wiretype: car.wireless,
              carType: car.carType
            }
          })
          window.open(href, '_blank')
        })

      $('#carInfoPanel .opt .command') // 指令
        .unbind()
        .click(function() {
          self.$emit('deviceDetailFromMap', {
            carId: car.carId,
            activeName: 'command'
          })
          /* if (window.frames['OldSystemFrame'] && window.frames['OldSystemFrame'].contentWindow) {
            self.setIframeIndex(1003)
            document.getElementsByTagName('body')[0].setAttribute('class', 'el-popup-parent--hidden')
            // 调用旧系统dialog.js里的方法
            window.frames['OldSystemFrame'].contentWindow.g_Main.dialog.commanFromVuePage(self.carsMap[self.nowSelectMarkerObj.carId])
          } */
        })
      $('#carInfoPanel .opt .street') // 街景
        .unbind()
        .click(function() {
          let zoom = self.nowZoom
          if (self.mapType === 1) {
            window.open('../../page/baiduStreetView.html?lng=' + car.carStatus.lonc + '&lat=' + car.carStatus.latc, '_blank')
          } else {
            window.open(
              'https://www.google.com/maps?q=' +
                car.carStatus.latc +
                ',' +
                car.carStatus.lonc +
                '&ll=' +
                car.carStatus.latc +
                ',' +
                car.carStatus.lonc +
                '&z=' +
                zoom,
              '_blank'
            )
          }
        })
      $('#carInfoPanel .opt .detail') // 详细
        .unbind()
        .click(function() {
          self.$emit('deviceDetailFromMap', {
            carId: car.carId,
            activeName: 'detail'
          })
          // self.$parent.chooseMenuIitem(6)
          /* if (window.frames['OldSystemFrame'] && window.frames['OldSystemFrame'].contentWindow) {
            self.setIframeIndex(1003)
            document.getElementsByTagName('body')[0].setAttribute('class', 'el-popup-parent--hidden')
            // 调用旧系统dialog.js里的方法
            window.frames['OldSystemFrame'].contentWindow.g_Main.dialog.machineInfoDialog(car.carId, car.userId, 'vuepage')
          } */
        })
      $('#carInfoPanel .opt .analysis') // 统计
        .unbind()
        .click(function() {
          self.$cookies.set('carId', car.carId)
          self.$router.push({ name: 'Statistics' })
        })
      window.set_hide_iframe = function() {
        self.setIframeIndex(-1)
        document.getElementsByTagName('body')[0].removeAttribute('class')
      }
      $('#carInfoPanel .optRew') // 续费
        .unbind()
        .click(function() {
          if (window.frames['OldSystemFrame'] && window.frames['OldSystemFrame'].contentWindow) {
            self.setIframeIndex(1003)
            document.getElementsByTagName('body')[0].setAttribute('class', 'el-popup-parent--hidden')
            // 调用旧系统dialog.js里的方法
            window.frames['OldSystemFrame'].contentWindow.g_Main.dialog.searchResultDialog(self.carsMap[self.nowSelectMarkerObj.carId].machineName, 0, 2)
          }
        })
      $('.maptalks-msgBox .maptalks-close') // 关闭回显设备名称
        .unbind()
        .click(function() {
          self.carImageUrl = ''
          if (localStorage.getItem('MachineName') === 'true') {
            // 设备名称在车辆信息框关闭时回显
            if (self.nowSelectMarkerObj.marker) {
              self.carMarkerMap[self.nowSelectMarkerObj.carId].removeInfoWindow()
              self.carMarkerMap[self.nowSelectMarkerObj.carId].setInfoWindow({
                content: '<div class="map_machineNameBox" style="position:relative">' + self.carsMap[self.nowSelectMarkerObj.carId].machineName + '</div>',
                single: false,
                autoPan: false,
                custom: true,
                dx: 0,
                dy: -8
              })
              self.carMarkerMap[self.nowSelectMarkerObj.carId].openInfoWindow()
              self.nowSelectMarkerObj = {
                marker: null,
                carId: ''
              }
            }
          }
          if (this.trackingCarMarker) {
            this.trackingCarMarker.closeInfoWindow()
          }
        })
      $('#carInfoPanel .opt .diagnosis')
        .unbind()
        .click(function() {
          self.$emit('deviceDetailFromMap', {
            carId: car.carId,
            activeName: 'diagnosis'
          })
        })

      $('#carInfoPanel .topRow .videoIcon')
        .unbind()
        .click(function() {
          const { href } = self.$router.resolve({
            name: 'TimeVideo',
            query: {
              carId: car.carId,
              userId: car.userId,
              imei: car.imei
            }
          })
          window.open(href, '_blank')
        })
      $('#carInfoPanel .topRow .intercomIcon')
        .unbind()
        .click(() => {
          if (this.videoType === 1) {
            //对讲弹窗中，直接返回
            return
          }
          if (this.videoType === 0) {
            //监听弹窗中
            this.$message({
              message: this.$t('IKa5v1S4C9m3JqKSNCGDm'),
              type: 'warning'
            })
          }
          this.showIntercomPop = true
          $('#carInfoPanel .intercom-popover').removeClass('hide')
          $('#carInfoPanel .intercom-popover-corner').removeClass('hide')
        })
      $('#carInfoPanel .intercom-popover .popover-btn-text')
        .unbind()
        .click(() => {
          this.showIntercomPop = false
          $('#carInfoPanel .intercom-popover').addClass('hide')
          $('#carInfoPanel .intercom-popover-corner').addClass('hide')
        })
      $('#carInfoPanel .intercom-popover .popover-btn-primary')
        .unbind()
        .click(() => {
          this.showIntercomPop = false
          $('#carInfoPanel .intercom-popover').addClass('hide')
          $('#carInfoPanel .intercom-popover-corner').addClass('hide')
          this.handleShowIntercom(car)
        })
    },
    async carInfoLoadImage(car) {
      const { ret, data } = await _getCarImg({ carId: car.carId })
      if (ret === 1 && data) {
        this.carImageUrl = data.url
        let htmlStr = `<img src="${data.url}"/>`
        let container = document.getElementById('carImageContainer')
        let carInfo = document.getElementById('carInfoPanel')
        if (container) {
          carInfo.classList.add('hasImage')
          container.innerHTML = htmlStr
          container.classList.remove('hide')
        }
      } else {
        let container = document.getElementById('carImageContainer')
        if (container) {
          container.classList.add('hide')
        }
      }
    },
    handleShowIntercom(car) {
      this.$store.commit('video/SET_CALL_TYPE', 1)
      this.$store.commit('video/SET_CURRENT_CAR', car)
      this.$store.commit('video/CHANGE_CALL_STATUS', true)
    },
    // 绘制车辆的轨迹线
    drawCarLine: function(lineArr, color) {
      const self = this
      console.log(color)
      let line = new maptalks.LineString(lineArr, {
        symbol: {
          lineColor: color,
          lineWidth: 6,
          lineJoin: 'round', //miter, round, bevel
          lineCap: 'round', //butt, round, square
          lineDasharray: null, //dasharray, e.g. [10, 5, 5]
          lineOpacity: 1
        }
      }).addTo(self.vecLayer)
      return line
    },
    // 清除具体车辆的轨迹线
    clearCarLine: function(carId) {
      let lineArr = this.carTrailLineMap[carId]
      if (lineArr && lineArr.length > 0) {
        lineArr.forEach(item => {
          item.remove()
        })
        delete this.carTrailMap[carId]
      }
    },
    /* 根据速度生成颜色
     ** speed 速度参数；
     ** greenSpeed 安全速度
     ** redSpeed 超速速度 */
    getColorBySpeed: function(speed, greenSpeed = 100, redSpeed = 120) {
      if (speed >= redSpeed) return 'red'
      else if (speed >= greenSpeed && speed < redSpeed) return 'yellow'
      else return '#3ff419'
    },
    // 监听地图缩放
    zoomEventListen: function() {
      const self = this
      // 地图构建完
      if (self.swdMapTalk) {
        self.swdMapTalk.on('zoomend', function(param) {
          if (param) {
            self.nowZoom = param.to
          }
        })
      }
    },
    handleStreetView() {
      let zoom = this.nowZoom
      const car = this.carsMap[this.nowSelectMarkerObj.carId] || this.currentTrackCar
      const lonc = car.lonC || car.carStatus.lonc
      const latc = car.latC || car.carStatus.latc
      if (this.mapType === 1) {
        window.open('../../page/baiduStreetView.html?lng=' + lonc + '&lat=' + latc, '_blank')
      } else {
        window.open('https://www.google.com/maps?q=' + latc + ',' + lonc + '&ll=' + latc + ',' + lonc + '&z=' + zoom, '_blank')
      }
    },
    // 切换为卫星地图和正常地图切换(只适用同一类型地图之间切换)
    switchMapSatellite: function(type) {
      const self = this
      if (type === 'satellite') {
        // 点击卫星切换
        self.nowShowSatellite = !self.nowShowSatellite
      }
      let site = ''
      if (window.location.host.indexOf('gpsnow.net') != -1 || window.location.host.indexOf('localhost') != -1) {
        site = 'CN'
      } else {
        site = 'EN'
      }
      let langParam = 'EN'
      if (this.lang == 'cn') {
        langParam = 'zh-CN'
      } else if (this.lang == 'tw') {
        langParam = 'zh-TW'
      }
      let baseLayer = ''
      if (self.swdMapTalk.getLayer('addplayer')) {
        // 百度卫星和OSM地图要显示地标需要额外的加个layer,每次切换先清除
        self.swdMapTalk.removeLayer('addplayer')
      }
      // 卫星地图
      if (self.nowShowSatellite) {
        let _addplayer = ''
        switch (parseInt(self.mapType)) {
          case 1:
            baseLayer = new maptalks.TileLayer(self.$t('TrFNV69OZRAH0okN4sumS'), {
              urlTemplate:
                'https://ss{s}.bdstatic.com/8bo_dTSlR1gBo1vgoIiO_jowehsv/starpic/?qt=satepc&s=1&u=x={x};y={y};z={z};v=009;type=sate&fm=46&app=webearth2&v=009',
              subdomains: ['0', '1', '2', '3']
            })
            _addplayer = new maptalks.TileLayer('addplayer', {
              urlTemplate: 'https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&s=1&styles=sl&scaler=1&p=1&s=1&udt=20190410',
              subdomains: ['0', '1', '2']
            })
            self.swdMapTalk.addLayer(_addplayer)
            break
          case 2:
            baseLayer = new maptalks.TileLayer(self.$t('r9Z0vOw35FkxLAXFabw2R'), {
              urlTemplate: 'https://mt{s}.google.com/maps/vt?lyrs=y&hl=' + langParam + '&gl=' + site + '&x={x}&y={y}&z={z}',
              subdomains: ['0', '1', '2', '3']
            })
            break
          case 3:
            baseLayer = new maptalks.TileLayer('必应卫星地图', {
              id: 'bingSatellite',
              urlTemplate: function(x, y, z, domain) {
                //通过x,y,z计算quadkey和url
                var quadKey = self.quadTreeChange(x, y, z)
                return 'http://ecn.t' + domain + '.tiles.virtualearth.net/tiles/a' + quadKey + '.jpeg?g=7863'
              },
              subdomains: ['0', '1', '2', '3']
            })
            break
          case 4:
            baseLayer = new MapboxglLayer('mapbox', {
              glOptions: {
                style: 'mapbox://styles/mapbox/satellite-v9',
                logoPosition: 'top-left'
              }
            })
            break
          case 5:
            // osm卫星图
            baseLayer = new maptalks.TileLayer(self.$t('r9Z0vOw35FkxLAXFabw2R'), {
              urlTemplate: 'https://mt{s}.google.com/maps/vt?lyrs=y%40781&hl=' + langParam + '&gl=' + site + '&x={x}&y={y}&z={z}',
              subdomains: ['0', '1', '2', '3']
            })
            _addplayer = new maptalks.TileLayer('addplayer', {
              urlTemplate: 'https://mt{s}.google.com/maps/vt?lyrs=h%40781&hl=' + langParam + '&gl=' + site + '&x={x}&y={y}&z={z}',
              subdomains: ['0', '1', '2', '3']
            })
            self.swdMapTalk.addLayer(_addplayer)
            break
          default:
            break
        }
        // 百度卫星和OSM地图要显示地标需要额外的加个layer,每次切换先置于底层
        if (self.swdMapTalk.getLayer('addplayer')) {
          self.swdMapTalk.getLayer('addplayer').bringToBack()
        }
      } else {
        // 正常地图
        switch (parseInt(self.mapType)) {
          case 1:
            baseLayer = new maptalks.TileLayer(self.$t('A7xrOFhybj9Js9R7Oulub'), {
              urlTemplate: 'https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&s=1&styles=pl&scaler=1&p=1&s=1&udt=' + self.udt,
              subdomains: ['0', '1', '2']
            })
            break
          case 2:
            baseLayer = new maptalks.GroupTileLayer(self.$t('E2cJxJ0qcSBiNzRMKbaj0'), [
              new maptalks.TileLayer(self.$t('2yf5VqhJTX_NvDljHWexs'), {
                visible: !self.nowShowSatellite,
                urlTemplate: 'https://mt{s}.google.com/vt/lyrs=m&hl=' + langParam + '&gl=' + site + '&src=app&x={x}&y={y}&z={z}',
                subdomains: ['0', '1', '2', '3']
              })
            ])
            break
          case 3:
            baseLayer = new maptalks.TileLayer('必应电子地图', {
              id: 'bingBase',
              urlTemplate: function(x, y, z, domain) {
                //通过x,y,z计算quadkey和url
                var quadKey = self.quadTreeChange(x, y, z)
                return 'http://ecn.t' + domain + '.tiles.virtualearth.net/tiles/r' + quadKey + '.jpeg?g=7863&mkt=zh-CN&shading=hill'
              },
              subdomains: ['0', '1', '2', '3']
            })
            break
          case 4:
            baseLayer = new MapboxglLayer('mapbox', {
              glOptions: {
                style: 'mapbox://styles/mapbox/streets-v11',
                logoPosition: 'top-left'
              }
            })
            break
          case 5:
            baseLayer = new maptalks.TileLayer('base', {
              // urlTemplate: 'https://{s}.tile.thunderforest.com/transport/{z}/{x}/{y}.png?apikey=6170aad10dfd42a38d4d8c709a536f38',
              urlTemplate: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
              subdomains: ['a', 'b', 'c'],
              attribution: '&copy; <a href="http://osm.org">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/">CARTO</a>'
            })
            break
          default:
            break
        }
      }
      self.swdMapTalk.setBaseLayer(baseLayer)
      if (this.alarmId) {
        this.getAlarmPointInfo()
      }
    },
    // 放大地图
    enlargeZoom: function() {
      if (this.swdMapTalk) {
        let zoom = this.nowZoom + 1
        this.swdMapTalk.setZoom(zoom)
        this.nowZoom = this.swdMapTalk.getZoom()
        console.log('放大：' + this.nowZoom)
      }
    },
    // 缩小地图
    reduceZoom: function() {
      let zoom = this.nowZoom - 1
      this.swdMapTalk.setZoom(zoom)
      console.log('缩小：' + this.nowZoom)
      this.nowZoom = this.swdMapTalk.getZoom()
    },
    // 地址搜索栏的展示
    showSearchFun() {
      const self = this
      this.showSearch = !this.showSearch
      if (this.showSearch) {
        setTimeout(() => {
          if (self.mapType === 1) {
            self.showBaiduInputAutocomplete()
          } else {
            self.showGoogleInputAutocomplete()
            loadScript(self.mapjsFiles[0], function() {})
          }
        }, 300)
      }
    },
    // 点击搜索地址
    searchAddress: function() {
      const self = this
      let value = ''
      console.log(self.mapType)
      if (self.mapType === 1) {
        value = document.getElementById('searchInputBaidu').value
      } else {
        value = document.getElementById('searchInputGoogle').value
      }
      if (value) {
        if (value.indexOf(',') != -1 && !isNaN(value.split(',')[0]) && !isNaN(value.split(',')[1])) {
          //经纬度
          let point = {
            lng: parseFloat(value.split(',')[0]),
            lat: parseFloat(value.split(',')[1]),
            icon: self.searchPointIcon
          }
          self.showPoint(point, true, 16)
        } else {
          if (self.mapType === 1) {
            let key = 'S2FEyARWIIc1y4M9Xqt3G8NdaWloQLVb'
            let url = 'https://api.map.baidu.com/geocoding/v3/?output=json&ak=' + key + '&address=' + value
            self
              .$jsonp(url, {})
              .then(json => {
                if (json && json.status === 0) {
                  let result = json.result
                  let point = {
                    lng: result.location.lng,
                    lat: result.location.lat,
                    icon: self.searchPointIcon
                  }
                  self.showPoint(point, true, 16)
                } else {
                  self.$message({
                    message: json.msg,
                    type: 'error'
                  })
                }
              })
              .catch(error => {
                self.$message({
                  message: self.$t('LseoYpEYEbqOsg-3HO9uM'),
                  type: 'error'
                })
              })
          } else {
            self.showPoint(self.googlePoint, true, 16)
          }
        }
      }
    },
    // 百度apiinput框地址输入自动完成匹配
    showBaiduInputAutocomplete: function() {
      const self = this
      //输入地址事件处理 start
      var ac = new window.BMap.Autocomplete({
        //建立一个自动完成的对象
        input: 'searchInputBaidu',
        location: '中国'
      })
      ac.addEventListener('onconfirm', function(e) {
        let _value = e.item.value
        let res = _value.province + _value.city + _value.district + _value.street + _value.business
        self.searchAddress()
      })
    },
    // 谷歌api input框地址输入自动完成匹配
    showGoogleInputAutocomplete: function() {
      const self = this
      window.initGoogleMap = function() {
        let input = document.getElementById('searchInputGoogle')
        let autocomplete = new window.google.maps.places.Autocomplete(input)
        console.log('point', autocomplete)
        autocomplete.addListener('place_changed', function() {
          let place = autocomplete.getPlace()
          self.googlePoint = {
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
            icon: self.searchPointIcon
          }
          self.searchAddress()
        })
      }
    },
    // 设置车标的中心点
    setCenter: function(lng, lat, zoom) {
      this.swdMapTalk.setCenter([lng, lat])
      if (zoom) {
        this.swdMapTalk.setZoom(zoom)
      }
    },
    // 显示位置点
    showPoint: function(point, isCenter, zoom) {
      console.log(point)
      const self = this
      if (isCenter) {
        zoom = zoom || 10
        // this.setCenter(point.lng, point.lat, zoom)
        this.swdMapTalk.setCenter([point.lng, point.lat])
        this.swdMapTalk.setZoom(zoom)
      }
      let marker = new maptalks.Marker([point.lng, point.lat], {
        visible: true,
        editable: true,
        cursor: 'pointer',
        shadowBlur: 0,
        shadowColor: 'black',
        draggable: false,
        dragShadow: false, // display a shadow during dragging
        drawOnAxis: null, // force dragging stick on a axis, can be: x, y
        symbol: {
          markerFile: point.icon
          // 'textFaceName' : 'sans-serif',
          // 'textName' : 'MapTalks',
          // 'textFill' : '#34495e',
          // 'textHorizontalAlignment' : 'right',
          // 'textSize' : 40
        }
      })
      if (!self.vecLayer) {
        self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
      }
      setTimeout(function() {
        marker.addTo(self.vecLayer)
      }, 500)
    },
    // 控制是否显示设备名称
    handleMachineName(flag) {
      // console.log('地图', this.carMarkerMap)
      let machineNameNode = document.getElementsByClassName('maptalks-ui')[0]
      let childs = machineNameNode.childNodes
      if (childs.length > 0) {
        for (let i = childs.length - 1; i >= 0; i--) {
          machineNameNode.removeChild(childs[i])
        }
      }
      if (flag) {
        for (let key in this.carMarkerMap) {
          // this.carMarkerMap[key].removeInfoWindow()
          let infoWindow = this.carMarkerMap[key].getInfoWindow()
          if (!infoWindow) {
            this.carMarkerMap[key].openInfoWindow()
          } else if (infoWindow.getContent().indexOf('') === -1) {
            // 显示名称要把之前的弹窗关闭销毁掉
            if (this.nowSelectMarkerObj.marker) {
              this.nowSelectMarkerObj.marker.removeInfoWindow()
            }
            this.nowSelectMarkerObj = {
              marker: null,
              carId: ''
            }
            this.carMarkerMap[key].setInfoWindow({
              content: '<div class="map_machineNameBox" style="position:relative">' + this.carsMap[key].machineName + '</div>',
              single: false,
              autoPan: false,
              custom: true,
              dx: 0,
              dy: -8
            })
            this.carMarkerMap[key].openInfoWindow()
          } else if (infoWindow.getContent().indexOf('map_machineNameBox') !== -1) {
            this.carMarkerMap[key].openInfoWindow()
          }
        }
      } else {
        for (let key in this.carMarkerMap) {
          let infoWindow = this.carMarkerMap[key].getInfoWindow()
          if (!infoWindow) {
            this.carMarkerMap[key].closeInfoWindow()
          } else if (infoWindow.getContent().indexOf('map_machineNameBox') !== -1) {
            this.carMarkerMap[key].closeInfoWindow()
          }
        }
      }
    },
    // 控制显示围栏
    switchShowFence() {
      this.showFence = !this.showFence
      if (this.showFence) {
        this.fenceTitle = this.$t('gbtfYMFIkzFPpyAatuQPP')
        if (this.fenceCarId) {
          this.fenceTitle = this.carsMap[this.fenceCarId].machineName + '-' + this.$t('gbtfYMFIkzFPpyAatuQPP')
        }
        if (this.showPOIFlag) {
          this.hidePOIPanel()
        }
        if (this.showManageLine) {
          // 关闭路线管理
          this.ManageLineHandleClose()
        }
        this.getAllFenceServer()
      } else {
        this.fenceCarId = null
        this.fenceCarImei = ''
        this.showEveryFence = false
        this.cleanAllGeo()
      }
    },
    // 全部围栏pageSize发生变化
    allFenceSizeChange(val) {
      this.allFenceQuery.pageNO = 1
      this.allFenceQuery.rowCount = parseInt(val)
      this.getAllFenceServer()
    },
    // 全部围栏currentPage发生变化
    allFenceCurrentChange(val) {
      this.allFenceQuery.pageNO = parseInt(val)
      this.getAllFenceServer()
    },
    // 勾选是否显示全部围栏
    changeFenceFlag($event) {
      if ($event) {
        this.showAllFenceInMap()
      } else {
        this.cleanAllGeo()
      }
    },
    // 搜索围栏
    searchFence() {
      console.log(this.allFenceQuery.name)
      this.getAllFenceServer()
    },
    // 获取全部围栏数据
    getAllFenceServer() {
      const self = this
      let postData = {
        mapType: self.mapType,
        version: '2.2.0',
        pageNO: self.allFenceQuery.pageNO,
        rowCount: self.allFenceQuery.rowCount
      }
      if (self.allFenceQuery.name) {
        postData.name = self.allFenceQuery.name
      }
      if (self.fenceCarId) {
        postData.carId = self.fenceCarId
      }
      getAllFenceData(postData).then(res => {
        if (res.ret === 1) {
          self.allFenceData = res.data
          self.allFenceTotal = res.total
        } else {
          self.allFenceData = []
          self.allFenceTotal = 0
        }
        self.multipleFence = []
        self.cleanAllGeo()
        self.showAllFenceInMap()
      })
    },
    hideBindClose(e) {
      this.bindFenceFlag = e.close
    },
    updateFenceList(e) {
      console.log('更新')
      if (this.showFence) {
        this.getAllFenceServer()
      }
    },
    bindFence(data) {
      const self = this
      this.bindCarFenceId = data.carFenceId
      this.bindFenceFlag = true
      /* if (window.frames['OldSystemFrame'] && window.frames['OldSystemFrame'].contentWindow) {
        this.setIframeIndex(1003)
        document.getElementsByTagName('body')[0].setAttribute('class', 'el-popup-parent--hidden')
        window.frames['OldSystemFrame'].contentWindow.g_Main.dialog.relationDeviceWindow(data.carFenceId, 'vuepage')
      } */
    },
    // 删除围栏
    deleteFence(data) {
      let tip = this.$t('I8b_zoiaDScRSfe8AcYJQ', [data.name])
      if (data.carNum) {
        tip = this.$t('53WepRb0dgsGBYKlPWo_b', [data.carNum])
      }
      this.$confirm(tip, this.$t('lg.limits.Delete_fence'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      })
        .then(() => {
          deleteCarFence({
            carFenceId: data.carFenceId
          }).then(res => {
            if (res.ret === 1) {
              this.$message({
                type: 'success',
                message: this.$t('cvyH4iJuWCiLfB_Wsn4Nn')
              })
              this.getAllFenceServer()
            }
          })
        })
        .catch(() => {})
    },
    // 显示围栏
    showAllFenceInMap() {
      const self = this
      for (let i = 0; i < self.allFenceData.length; i++) {
        let item = self.allFenceData[i]
        console.log(JSON.parse(JSON.stringify(item)))
        let point = item.points
        let radius = item.radius
        let type = item.type
        let adcode = item.adcode
        if (self.swdMapTalk) {
          if (!self.showEveryFence && i > 0) {
            return false
          }
          if (type === 0) {
            // 圆
            let pointArrSplit = point.split(',')
            self.setCenter(pointArrSplit[0], pointArrSplit[1], 15)
            self.createGeo(point, radius, type)
          } else if (type === 1) {
            // 多边形
            let pointArr = point.split(';')[0].split(',')
            self.setCenter(pointArr[0], pointArr[1], 15)
            self.createGeo(point, radius, type)
          } else if (type === 2) {
            // 行政区
            self.swdMapTalk.setZoom(10)
            self.createGeo(point, radius, type, adcode)
          }
        }
      }
    },
    // 全部显示围栏按钮
    showAllFenceBtn() {
      this.showEveryFence = true
      this.getAllFenceServer()
    },
    // 围栏选中事件
    handleSelectFenceChange(val) {
      this.multipleFence = val
    },
    // 批量删除围栏
    batchDelFence() {
      let arr = this.multipleFence
      if (!arr.length) {
        this.$message({
          type: 'error',
          message: this.$t('r07mdOUMExdAaIj36JZgK')
        })
      } else {
        let tip = ''
        for (let i = 0; i < arr.length; i++) {
          console.log(arr[i])
          if (arr[i].carNum) {
            // tip += `${arr[i].name}围栏已关联${arr[i].carNum}台设备，`
            tip += this.$t('tKwyVQxLCB3j3YDFNoq3y', [arr[i].name, arr[i].carNum])
          }
        }
        tip += this.$t('fPpUDmYmZBfijNxuYo-dT')
        this.$confirm(tip, this.$t('lg.limits.Delete_fence'), {
          confirmButtonText: this.$t('lg.confirm'),
          cancelButtonText: this.$t('lg.cancel'),
          type: 'warning'
        }).then(() => {
          let carFenceIds = []
          arr.forEach(item => {
            carFenceIds.push(item.carFenceId)
          })
          carFenceIds = carFenceIds.join(',')
          console.log(carFenceIds)
          deleteBatchCarFence({ carFenceIds: carFenceIds }).then(res => {
            if (res.ret === 1) {
              this.$message({
                type: 'success',
                message: this.$t('cvyH4iJuWCiLfB_Wsn4Nn')
              })
              this.getAllFenceServer()
            } else {
              this.$message({
                message: this.$t('lg.fail'),
                type: 'error'
              })
            }
          })
        })
        // deleteBatchCarFence
      }
    },
    // 创建围栏图形
    createGeo: async function(lngLats, radius, type, areaCode) {
      const self = this
      if (!self.vecLayer) {
        self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
      }
      if (type == 0) {
        let arr = lngLats.split(',')
        for (var i = 0; i < arr.length; i++) {
          arr[i] = parseFloat(arr[i])
        }
        let circle = new maptalks.Circle(arr, radius, {
          symbol: {
            lineColor: '#f33129',
            lineWidth: 2,
            polygonFill: '#f33129',
            polygonOpacity: 0.7
          }
        })
        circle.addTo(self.vecLayer)
        self.geoObjects.push(circle)
      } else if (type === 1) {
        let polygonArr = lngLats.split(';')
        let fenceArr = []
        for (let i = 0; i < polygonArr.length; i++) {
          let arr = polygonArr[i].split(',')
          for (let j = 0; j < arr.length; j++) {
            arr[j] = parseFloat(arr[j])
          }
          fenceArr.push(arr)
        }
        let polygon = new maptalks.Polygon(fenceArr, {
          visible: true,
          editable: true,
          cursor: 'pointer',
          shadowBlur: 0,
          shadowColor: 'black',
          draggable: false,
          dragShadow: false, // display a shadow during dragging
          drawOnAxis: null, // force dragging stick on a axis, can be: x, y
          symbol: {
            lineColor: '#f33129',
            lineWidth: 2,
            polygonFill: '#f33129',
            polygonOpacity: 0.7
          }
        })
        polygon.addTo(self.vecLayer)
        self.geoObjects.push(polygon)
      } else if (type == 2) {
        let coordinates = []
        let _tempArr = []
        _tempArr = lngLats.split('|')
        for (let i = 0; i < _tempArr.length; i++) {
          let _secondArr = [0]
          let _subArr = []
          let secondArr = _tempArr[i].split(';')
          for (let j = 0; j < secondArr.length; j++) {
            _subArr.push([parseFloat(secondArr[j].split(',')[0]), parseFloat(secondArr[j].split(',')[1])])
          }
          _secondArr[0] = _subArr
          coordinates.push(_secondArr)
        }
        let result = await getAreaJSON(areaCode)
        console.log(result)
        let _coordinates = result.features[0].geometry.coordinates
        if (self.mapType === 1) {
          for (let i = 0; i < _coordinates.length; i++) {
            for (let j = 0; j < _coordinates[i].length; j++) {
              let item = _coordinates[i][j]
              for (let k = 0; k < item.length; k++) {
                item[k] = GCJ02ToBD09(item[k][0], item[k][1])
              }
            }
          }
        }
        let polygon = new maptalks.MultiPolygon(_coordinates, {
          visible: true,
          editable: true,
          cursor: 'pointer',
          shadowBlur: 0,
          shadowColor: 'black',
          draggable: false,
          dragShadow: false, // display a shadow during dragging
          drawOnAxis: null, // force dragging stick on a axis, can be: x, y
          symbol: {
            lineColor: '#f33129',
            lineWidth: 2,
            polygonFill: '#f33129',
            polygonOpacity: 0.7
          }
        })
        polygon.addTo(self.vecLayer)
        let center = result.features[0].properties.center
        self.setCenter(center[0], center[1], 10)
        self.geoObjects.push(polygon)
      }
    },
    //清空围栏
    cleanAllGeo: function() {
      if (this.geoObjects.length > 0) {
        this.vecLayer.removeGeometry(this.geoObjects)
        this.geoObjects = []
      }
      this.removeFenceEditDialog()
    },
    // 点击围栏表格的行
    fencTableClickRow(row, column, event) {
      const self = this
      self.cleanAllGeo()
      let point = row.points
      let radius = row.radius
      let type = row.type
      let adcode = row.adcode
      if (self.swdMapTalk) {
        if (type === 0) {
          // 圆
          let pointArrSplit = point.split(',')
          self.setCenter(pointArrSplit[0], pointArrSplit[1], 15)
          self.createGeo(point, radius, type)
        } else if (type === 1) {
          // 多边形
          let pointArr = point.split(';')[0].split(',')
          self.setCenter(pointArr[0], pointArr[1], 15)
          self.createGeo(point, radius, type)
        } else if (type === 2) {
          // 行政区
          self.swdMapTalk.setZoom(10)
          self.createGeo(point, radius, type, adcode)
        }
        self.removeFenceEditDialog() // 移除之前的编辑围栏的弹窗
        getCarFenceInfo({
          carFenceId: row.carFenceId,
          mapType: this.mapType
        }).then(res => {
          if (res.ret === 1) {
            // 围栏信息显示才有如下两个字段
            row.userName = res.data.userName ? res.data.userName : ''
            row.createTime = res.data.createTime ? res.data.createTime : ''
            row.showtype = 'view'
            self.showFenceEditDialog(row) // 显示编辑围栏的弹窗
          }
        })
      }
    },
    // 编辑围栏
    editRowFence(row) {
      const self = this
      self.cleanAllGeo()
      let point = row.points
      let radius = row.radius
      let type = row.type
      let adcode = row.adcode
      if (self.swdMapTalk) {
        if (type === 0) {
          // 圆
          let pointArrSplit = point.split(',')
          self.setCenter(pointArrSplit[0], pointArrSplit[1], 15)
          self.createGeo(point, radius, type)
        } else if (type === 1) {
          // 多边形
          let pointArr = point.split(';')[0].split(',')
          self.setCenter(pointArr[0], pointArr[1], 15)
          self.createGeo(point, radius, type)
        } else if (type === 2) {
          // 行政区
          self.swdMapTalk.setZoom(10)
          self.createGeo(point, radius, type, adcode)
        }
        self.removeFenceEditDialog() // 移除之前的编辑围栏的弹窗
        row.userName = ''
        row.createTime = ''
        row.showtype = ''
        self.showFenceEditDialog(row) // 显示编辑围栏的弹窗
      }
    },
    // 移除新增编辑围栏信息框
    removeFenceEditDialog: function() {
      if (this.fenceInfoWindow) {
        this.fenceInfoWindow.remove()
      }
      if (this.drawGeo) {
        this.drawGeo.endEdit()
        this.drawGeo.remove()
        this.drawGeo = null
      }
    },
    // 显示行政区选择
    showAreaDialog: async function() {
      const self = this
      let _limit = false
      self.$store.state.permissions.forEach(item => {
        if (item.perms === 'fence:add') {
          _limit = true
        }
      })
      if (!_limit) {
        self.$message({
          message: self.$t('zCKyujELRDbjx5vAbAD3r'),
          type: 'error'
        })
        return
      }
      self.areaChoseFlag = true
      // 不是区域查车
      self.isRegionalCarSearch = false
      let _proviceData = await getAreaData({ level: 1 })
      if (_proviceData.ret === 1) {
        this.provinceData = _proviceData.data
      } else {
        this.provinceData = []
      }
      console.log(_proviceData)
    },
    // 省发生变化
    provinceChange($event) {
      this.cityCode = ''
      this.districCode = ''
      getAreaData({ parent: $event.adcode }).then(res => {
        if (res.ret === 1) {
          this.cityData = res.data
        } else {
          this.cityData = []
        }
      })
    },
    // 市发生变化
    cityChange($event) {
      this.districCode = ''
      getAreaData({ parent: $event.adcode }).then(res => {
        if (res.ret === 1) {
          this.districtData = res.data
        } else {
          this.districtData = []
        }
      })
    },
    // 隐藏行政区选择
    hideAreaChose: function() {
      this.areaChoseFlag = false
    },
    confirmAreaChose: async function() {
      let p_code = this.provinceCode ? this.provinceCode.adcode : ''
      let c_code = this.cityCode ? this.cityCode.adcode : ''
      let d_code = this.districCode ? this.districCode.adcode : ''
      if (!p_code) {
        this.$message({
          showClose: true,
          message: '请选择省份',
          type: 'error'
        })
        return
      } else {
        if (!c_code && p_code !== '710000') {
          // this.$message({
          //   showClose: true,
          //   message: '请选择市',
          //   type: 'error'
          // })
          // return
          let urlCode = p_code
          let result = await getAreaJSON(urlCode)
          // 区域查车
          if (this.isRegionalCarSearch) {
            this.areaChoseFlag = false
            let points = ''
            result.features[0].geometry.coordinates[0][0].forEach((item, index) => {
              result.features[0].geometry.coordinates[0][0].length - 1 === index
                ? (points += item[0] + ',' + item[1])
                : (points += item[0] + ',' + item[1] + ';')
            })
            // 关闭左侧监控列表
            if (this.$parent.showLeftBox) {
              this.$parent.showLeftBox = false
            }
            this.$emit('frameCheck', points)
            return
          }
          console.log(result)
          this.drawPoint = result.features
          let areaName = this.provinceCode.name
          if (c_code) {
            areaName += '-' + this.cityCode.name
          }
          if (d_code) {
            areaName += '-' + this.districCode.name
          }
          this.drawFence('MultiPolygon', areaName, urlCode)
        } else {
          let urlCode = c_code
          if (d_code) {
            urlCode = d_code
          }
          if (p_code === '710000') {
            urlCode = p_code
          }
          let result = await getAreaJSON(urlCode)
          // 区域查车
          if (this.isRegionalCarSearch) {
            this.areaChoseFlag = false
            let points = ''
            result.features[0].geometry.coordinates[0][0].forEach((item, index) => {
              result.features[0].geometry.coordinates[0][0].length - 1 === index
                ? (points += item[0] + ',' + item[1])
                : (points += item[0] + ',' + item[1] + ';')
            })
            // 关闭左侧监控列表
            if (this.$parent.showLeftBox) {
              this.$parent.showLeftBox = false
            }
            this.$emit('frameCheck', points)
            return
          }
          console.log(result)
          this.drawPoint = result.features
          let areaName = this.provinceCode.name
          if (c_code) {
            areaName += '-' + this.cityCode.name
          }
          if (d_code) {
            areaName += '-' + this.districCode.name
          }
          this.drawFence('MultiPolygon', areaName, urlCode)
        }
      }
    },
    drawFence: function(type, areaName, urlCode) {
      const self = this
      let _limit = false
      self.$store.state.permissions.forEach(item => {
        if (item.perms === 'fence:add') {
          _limit = true
        }
      })
      if (!_limit) {
        self.$message({
          message: self.$t('zCKyujELRDbjx5vAbAD3r'),
          type: 'error'
        })
        return
      }
      self.removeFenceEditDialog()
      if (type == 'Polygon' || type == 'Circle') {
        self.swdMapTalk.setCursor('crosshair')
        let drawTool = new maptalks.DrawTool({
          mode: type,
          once: true,
          symbol: {
            lineColor: '#f33129',
            lineWidth: 2,
            polygonFill: '#f33129',
            polygonOpacity: 0.4
          }
        }).addTo(self.swdMapTalk)
        // 初始化时没有如果没有 vecLayer 需要加上
        if (!self.vecLayer) {
          self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
        }
        //新增按键 Ctrl+Z 撤销/前进到上一步绘制
        self.drawTool = drawTool
        document.addEventListener('keydown', this.keyDownEvent)
        drawTool.on('drawend', function(param) {
          //删除按钮绑定事件
          document.removeEventListener('keydown', this.keyDownEvent)
          self.drawTool = null
          self.swdMapTalk.setCursor('auto')
          self.vecLayer.addGeometry(param.geometry)
          self.geoObjects.push(param.geometry)
          // 多边形
          if (type == 'Polygon') {
            // 存储圆或多边形的点
            let polygonPoint = (self.drawPoint = param.geometry.getCoordinates()[0])
            let polygonArr = []
            for (let i = 0; i < polygonPoint.length; i++) {
              let arr = [polygonPoint[i].x, polygonPoint[i].y]
              polygonArr.push(arr)
            }
            self.cleanAllGeo()
            let polygon = (self.drawGeo = new maptalks.Polygon([polygonArr], {
              visible: true,
              editable: true,
              cursor: 'pointer',
              shadowBlur: 0,
              shadowColor: 'black',
              draggable: true,
              dragShadow: false, // display a shadow during dragging
              drawOnAxis: null, // force dragging stick on a axis, can be: x, y
              symbol: {
                lineColor: '#f33129',
                lineWidth: 2,
                polygonFill: '#f33129',
                polygonOpacity: 0.4
              }
            }))
            polygon.addTo(self.vecLayer)
            polygon.startEdit()
            polygon.on('shapeChange', function(event) {
              self.drawPoint = event.target._coordinates
            })
            self.showFenceEditDialog({
              type: 1,
              name: '',
              inSwitch: true,
              outSwitch: true,
              points: self.drawPoint[0].x + ',' + self.drawPoint[0].y,
              radius: '',
              pushSubFlag: false
            })
          } else if (type == 'Circle') {
            self.drawPoint = param.geometry.getCoordinates()
            self.cleanAllGeo()
            let radius = (self.drawCycleRadius = parseInt(param.geometry._radius))
            self.drawGeo = new maptalks.Circle(self.drawPoint, radius, {
              symbol: {
                lineColor: '#f33129',
                lineWidth: 2,
                polygonFill: '#f33129',
                polygonOpacity: 0.4
              }
            })
            let circle = self.drawGeo
            circle.addTo(self.vecLayer)
            circle.startEdit()
            circle.on('shapeChange', function(event) {
              self.drawCycleRadius = parseInt(event.target._radius)
              $('#m_circel_radius').html(self.drawCycleRadius)
            })
            self.showFenceEditDialog({
              type: 0,
              name: '',
              inSwitch: true,
              outSwitch: true,
              points: self.drawPoint.x + ',' + self.drawPoint.y,
              radius: radius,
              pushSubFlag: false
            })
          }
        })
      } else if (type == 'MultiPolygon') {
        let _features = self.drawPoint
        let properties = _features[0].properties
        let coordinates = _features[0].geometry.coordinates
        let _coordinates = coordinates
        //如果选择的地图模式百度 围栏数据需转换成百度
        if (self.mapType == 1) {
          for (var i = 0; i < _coordinates.length; i++) {
            for (var j = 0; j < _coordinates[i].length; j++) {
              var item = _coordinates[i][j]
              for (var k = 0; k < item.length; k++) {
                item[k] = GCJ02ToBD09(item[k][0], item[k][1])
              }
            }
          }
        }
        self.drawPoint = coordinates
        self.cleanAllGeo()
        var polygon = (self.drawGeo = new maptalks.MultiPolygon(_coordinates, {
          visible: true,
          editable: true,
          cursor: 'pointer',
          shadowBlur: 0,
          shadowColor: 'black',
          draggable: false,
          dragShadow: false,
          drawOnAxis: null,
          symbol: {
            lineColor: '#f33129',
            lineWidth: 2,
            polygonFill: '#f33129',
            polygonOpacity: 0.4
          },
          properties: {
            id: properties.id,
            index: 1,
            properties: properties
          }
        }))
        polygon.addTo(self.vecLayer)
        self.areaChoseFlag = false
        this.showFenceEditDialog({
          adcode: urlCode,
          type: 2,
          inSwitch: true,
          outSwitch: true,
          pushSubFlag: false,
          points: { x: properties.center[0], y: properties.center[1] },
          name: areaName,
          areaName: areaName
        })
        // self.showFenceDialog(type, points, extendStr, urlCode)
      }
    },
    //显示编辑围栏弹窗
    showFenceEditDialog: async function(row) {
      console.log('围栏编辑', row)
      const self = this
      // createTime userName
      self.fenceEditInfo = row
      let viewHide = row.showtype ? 'hide' : ''
      let viewShow = row.showtype ? '' : 'hide'
      let radiusHtml = ''
      let inputWidth = ''
      let areaFenceHtml = ''
      let offsetX, offsetY, fixPoint
      let showInfoCircle = 'hide'
      let showInfoArea = 'hide'
      let checkInStr = row.inSwitch ? 'checked="true"' : ''
      let checkOutStr = row.outSwitch ? 'checked="true"' : ''
      // 离开断油电 --start
      let PowerOffSrt = row.isSupportControlPoil == 1 ? 'checked="true"' : '' // 断油电checkbox值
      let showPowerOff = '' // 断油电开关显隐
      //  进入断油电 --start
      let PowerOnSrt = row.isSupportControlPoil == 2 ? 'checked="true"' : '' //进入断油电checkbox值
      let showPowerOn = '' //进入断电油开关显隐
      //  进入断油电 --end
      // 出围栏断油电设置鉴权
      let res = await _userAuthConfigGetInfo()
      if (res.ret == 1 && res.data) {
        let { configItem } = res.data
        if (configItem.length > 1) {
          showPowerOff = ''
          showPowerOn = ''
        } else {
          switch (Number(configItem)) {
            case 1:
              showPowerOff = ''
              showPowerOn = 'hide'
              break
            case 2:
              showPowerOff = 'hide'
              showPowerOn = ''
              break
            default:
              showPowerOff = 'hide'
              showPowerOn = 'hide'
              PowerOffSrt = ''
              PowerOnSrt = ''
          }
        }
      } else {
        showPowerOff = 'hide'
        showPowerOn = 'hide'
        PowerOffSrt = ''
        PowerOnSrt = ''
      }
      // 离开断油电 --end
      let checkFenceType = row.fenceType === 1 ? 'checked="true"' : ''
      let checkSubFlag = row.pushSubFlag ? 'checked="true"' : ''
      let subFlagdis = checkFenceType ? '' : 'disabled'
      let modifyarea = 'hide'
      if (!row.carFenceId) {
        modifyarea = ''
      }
      let _showTime = row.userName && row.createTime ? '' : 'hide'
      let editing = row.showtype ? '' : 'editing'
      let _createTime = ''
      if (row.createTime) {
        _createTime = TimeUTCToLocal(row.createTime)
      }
      let _shaapStr = ''
      if (row.type === 0) {
        // 圆形
        showInfoCircle = ''
        offsetX = 200
        offsetY = 80
        let _arr = row.points.split(',')
        fixPoint = { x: _arr[0], y: _arr[1] }
        _shaapStr = self.$t('sgH0wIb6CRMhrPErfzZNo')
      } else if (row.type == 1) {
        // 多边形
        offsetX = 190
        offsetY = 120
        let _arr = row.points.split(';')[0].split(',')
        fixPoint = { x: _arr[0], y: _arr[1] }
        _shaapStr = self.$t('YRsd0eFEx-XpEoHM4bQOf')
      } else if (row.type == 2) {
        // 行政区域
        offsetX = 100
        offsetY = -50
        showInfoArea = ''
        _shaapStr = self.$t('xhq87s8s1ds1hdh1dhddh')
        if (row.carFenceId) {
          let _pointArr = row.points.split('_')[0].split(';')
          _pointArr = _pointArr.map(item => {
            return {
              lonc: item.split(',')[0],
              latc: item.split(',')[1]
            }
          })
          let _res = self.getCenterPoint(_pointArr)
          fixPoint = {
            x: _res.lon,
            y: _res.lat
          }
        } else {
          fixPoint = row.points
        }
      }
      self.setCenter(fixPoint.x, fixPoint.y)
      // Xckj用户定制
      let isxckj = false
      let showXckj = isxckj ? '' : 'hide'
      let text = `<div id="fenceBoxModal">
        <div class="fenceHeader"><span>${
          row.userName ? self.$t('LsRzknqAtdhScwNBoSsHK') : self.$t('nd_7J9mswRt2IfBGNjIWa')
        }</span><div class="close" onclick="mapWindow.close()"></div></div>
        <div class="fenceBody ${editing}">
          <div class="fenceName">
            <div class="label">${self.$t('KQBFZQ0Voyfhg6fQXT_Rn')}：</div>
            <input placeholder="${self.$t('NHp9pI1Qr5rbpe8r-4Nd4')}" class="fence_name ${viewHide}" value="${row.name}" id="m_fence_name"/>
            <span class="txt ${viewShow}">${row.name}</span>
          </div>
          <div class="fenceName ${viewShow}">
            <div class="label">${self.$t('zhjiZcfXH8sIv6yxSkkwq')}：</div>
            <span class="txt">${_shaapStr}</span>
          </div>
          <div class="radiusTxt ${showInfoCircle} ${viewHide}"><div class="label">${self.$t(
        'q_gUoHM-7bSFFC0IuHOmI'
      )}：</div><span id="m_circel_radius">${MeterToKmMi(row.radius, true)}</span><span>${
        self.miledgeUnitEN == 'km' ? self.$t('Csx-q_UsjB2o0oRRZf1qz') : self.$t('_Mb6dEr1YRXYFPEh11ycQ')
      }</span></div>
          <div class="areaBlock ${showInfoArea}"><div class="label">区域：</div><span>${
        row.areaName
      }</span><span class="${modifyarea}" id="modify_fence_area" onclick="mapWindow.modifyArea()">修改</span></div>
          <div class="fenceName ${_showTime}">
            <div class="label">${self.$t('lg.createTime')}：</div>
            <span class="txt">${_createTime}</span>
          </div>
          <div class="fenceName mg0 ${viewShow}">
            <div class="label">创建人：</div>
            <span class="txt">${row.userName}</span>
          </div>
          <div class="fenceCheck ${viewHide}">
            <label class="chose"><input type="checkbox" name="checkIn" id="m_checkIn" ${checkInStr} ><span class="check-mark"></span><span>${self.$t(
        'W7-XDEPApmz5QAAGPLqQQ'
      )}</span></label>
            <label class="chose"><input type="checkbox" name="checkOut" id="m_checkOut" ${checkOutStr} ><span class="check-mark"></span><span>${self.$t(
        'UyLRnzSqYp4MB2K4_aOaE'
      )}</span></label>
      <label class="chose ${showPowerOff}"><input type="checkbox" name="powerOff" id="m_powerOff" ${PowerOffSrt}> <span class="check-mark"></span><span>${self.$t(
        'k7tmRHnc8k29NMS7xFv1O'
      )}</span></label>
       <label class="chose ${showPowerOn}"><input type="checkbox" name="powerOn" id="m_powerOn" ${PowerOnSrt}><span class="check-mark"></span><span>${self.$t(
        'QmAH9PaQGsFNjcHUM3n5d'
      )}</span></label>
          </div>
          <div class="fenceCheck ${showXckj} ${viewHide}">
            <label class="chose"><input type="checkbox" name="checkType" id="m_checkFenceType" onclick="mapWindow.changeFenceType()" ${checkFenceType}><span>${self.$t(
        'Oy3u-IJ6TzbP9vNvGYzw7'
      )}</span></label>
            <label class="chose"><input type="checkbox" name="checkSubFlag" id="m_checkpushSubFlag" ${subFlagdis}  ${checkSubFlag}><span>接受下级报警</span></label>
          </div>
        </div>
        <div class="fenceBottom">
          <div class="btn cancel ${viewHide}" onclick="mapWindow.cancel()">${self.$t('lg.cancel')}</div>
          <div class="btn confirm ${viewShow}" onclick="mapWindow.cancel()">${self.$t('lg.confirm')}</div>
          <div class="btn confirm ${viewHide}" onclick="mapWindow.confirm()">${self.$t('lg.confirm')}</div>
        </div>
      </div>`
      let options = {
        autoPan: false,
        // autoOpenOn: 'click', //set to null if not to open window when clicking on map
        single: false,
        custom: true,
        dx: offsetX,
        dy: offsetY,
        content: text
      }
      let infoWindow = (self.fenceInfoWindow = new maptalks.ui.InfoWindow(options))
      infoWindow.addTo(self.swdMapTalk).show(fixPoint)
      window.mapWindow = {
        close: function() {
          self.removeFenceEditDialog()
        },
        modifyArea: function() {
          self.areaChoseFlag = true
          self.isRegionalCarSearch = false
        },
        cancel: function() {
          self.drawFenCarId = null
          self.removeFenceEditDialog()
        },
        changeFenceType: function() {
          // 只有选中车队围栏才可以选择是否报警通知下级
          if ($('#m_checkFenceType').is(':checked')) {
            $('#m_checkpushSubFlag').removeAttr('disabled')
          } else {
            $('#m_checkpushSubFlag').attr('disabled', true)
            $('#m_checkpushSubFlag').attr('checked', false)
          }
        },
        confirm: function() {
          let type = self.fenceEditInfo.type
          let areaName = self.fenceEditInfo.areaName
          let adcode = self.fenceEditInfo.adcode
          let name = $('#m_fence_name').val()
          let radius = $('#m_circel_radius').text()
          radius = radius ? parseInt(radius) : 20
          let inSwitch = $('#m_checkIn').is(':checked')
          let outSwitch = $('#m_checkOut').is(':checked')
          let fenceType = $('#m_checkFenceType').is(':checked') ? 1 : 0
          let pushSubFlag = $('#m_checkpushSubFlag').is(':checked') ? true : false
          let points = row.points
          // 断油电开关
          let isPowerOff = $('#m_powerOff').is(':checked')
          // console.log(self.fenceEditInfo)
          if (!name) {
            self.$message({
              message: self.$t('NHp9pI1Qr5rbpe8r-4Nd4'),
              type: 'error'
            })
            return
          }
          if (!inSwitch && !outSwitch) {
            self.$message({
              message: self.$t('Y1eS06Ae8KWuwsunOTGsV'),
              type: 'error'
            })
            return
          }
          let postData = {}
          if (row.carFenceId) {
            // 更新围栏
            postData = {
              name: name,
              inSwitch: inSwitch,
              outSwitch: outSwitch,
              fenceType: fenceType,
              pushSubFlag: pushSubFlag,
              isSupportControlPoil: isPowerOff ? 1 : 0 // 离开断油电
            }
            // 编辑
            postData.carFenceId = row.carFenceId
            // 是否开启离开断油电判断
            console.log(isPowerOff)
            if (isPowerOff) {
              // 断油电免责确认
              self.openPasswordCheckDialog('update', postData)
            } else {
              self.updateFenceServer(postData)
            }
          } else {
            // 新增围栏
            if (type === 0) {
              postData = {
                type: type,
                radius: radius,
                name: name,
                points: points,
                mapType: self.mapType,
                inSwitch: inSwitch,
                outSwitch: outSwitch
              }
            } else if (type === 1) {
              let _points = []
              self.drawPoint.forEach(item => {
                _points.push(item.x + ',' + item.y)
              })
              if (self.drawPoint.length < 4) {
                self.$message({
                  message: self.$t('Y9bbn5LDAbEo9O9LPdGcH'),
                  type: 'error'
                })
                return
              }
              _points = _points.join(';')
              postData = {
                type: type,
                points: _points,
                name: name,
                mapType: self.mapType,
                inSwitch: inSwitch,
                outSwitch: outSwitch
              }
            } else if (type === 2) {
              let _points = ''
              for (let i = 0; i < self.drawPoint.length; i++) {
                let tempArr = []
                let _tempItem = self.drawPoint[i][0]
                for (let k = 0; k < _tempItem.length; k++) {
                  tempArr[k] = [_tempItem[k][0], _tempItem[k][1]]
                }
                _points += tempArr.join(';')
                if (i < self.drawPoint.length - 1) {
                  _points += '_'
                }
              }
              postData = {
                type: type,
                points: _points,
                name: name,
                areaName: areaName,
                adcode: adcode,
                mapType: self.mapType,
                inSwitch: inSwitch,
                outSwitch: outSwitch
              }
            }
            // 新增
            // 是否开启断油电判断
            postData.isSupportControlPoil = isPowerOff ? 1 : 0 // 离开断油电
            if (isPowerOff) {
              // 断油电免责确认
              self.openPasswordCheckDialog('add', postData)
            } else {
              self.addFenceServer(postData)
            }
          }
        }
      }
    },
    // 更新围栏
    updateFenceServer(data) {
      carFenceUpdate(data).then(res => {
        if (res.ret === 1) {
          this.getAllFenceServer()
          this.removeFenceEditDialog()
          this.$message({
            message: this.$t('lg.success'),
            type: 'success'
          })
        } else {
          this.$message({
            message: this.$t('lg.fail'),
            type: 'error'
          })
        }
      })
    },
    // 新增围栏
    addFenceServer(data) {
      carFenceAdd(data).then(res => {
        if (res.ret === 1) {
          if (this.drawFenCarId) {
            this.bindCarToFence({
              carFenceId: res.data.carFenceId,
              carIds: this.drawFenCarId
            })
          } else {
            this.getAllFenceServer()
            this.removeFenceEditDialog()
            this.$message({
              message: this.$t('6dWutEmqMZnfcebUwMWcH'),
              type: 'success'
            })
          }
        } else {
          this.$message({
            message: this.$t('lg.fail'),
            type: 'error'
          })
        }
      })
    },
    // 新增围栏后绑定到设备
    bindCarToFence(postData) {
      boundFenceCar(postData).then(res => {
        if (res.ret === 1) {
          this.drawFenCarId = null
          this.getAllFenceServer()
          this.removeFenceEditDialog()
          this.$message({
            message: this.$t('6dWutEmqMZnfcebUwMWcH'),
            type: 'success'
          })
        } else {
          this.$message({
            message: this.$t('lg.fail'),
            type: 'error'
          })
        }
      })
    },
    // 获取兴趣点
    getPOIList() {
      const self = this
      getPlaceGroup({
        mapType: self.mapType,
        // POI 上级可以查看下级的兴趣点，传多一个POI
        targetUserId: self.targetUserId
      }).then(res => {
        if (res.ret === 1) {
          let arr = res.data
          arr = arr.map(item => {
            item.open = false
            return item
          })
          self.poiList = arr
          self.poiCount = 0
          for (let i = 0; i < this.poiList.length; i++) {
            self.poiCount = self.poiCount + self.poiList[i].places.length
            self.poiList[i].places.map(item => {
              self.showSinglePoint({
                name: item.name,
                point: {
                  lng: item.lon,
                  lat: item.lat,
                  iconType: item.iconType ? item.iconType : 2
                }
              })
            })
          }
        }
      })
    },
    // 渲染单个兴趣点
    showSinglePoint(placePoint) {
      const self = this
      let interestPoint = {
        point: {
          lat: placePoint.point.lat,
          lon: placePoint.point.lng
        }
      }
      let selectIconImg = '/images/map/' + placePoint.point.iconType + '.png'
      if (!isDev) {
        selectIconImg = `${packagePath}` + selectIconImg
      }
      interestPoint.point.icon = selectIconImg //图标地址
      //  兴趣点信息框
      let infoWindow = {
        title: '',
        content: '<div class="poiMarker">' + placePoint.name + '</div>',
        // 'width': 300,
        // 'minHeight': 50,
        single: false, //是否只显示一个信息框
        autoPan: false,
        custom: true //自定义信息框
      }
      interestPoint.infoWindow = infoWindow
      let interestMarker = self.newInterestMarker(interestPoint)
      // 打开信息框
      interestMarker.openInfoWindow()
      self.placePointOverlays.push(interestMarker)
    },
    // 创建兴趣点
    newInterestMarker(placePoint) {
      const self = this
      let point = placePoint.point
      let infoWindow = placePoint.infoWindow
      if (!self.vecLayer) {
        self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
      }
      let marker = new maptalks.Marker([point.lon, point.lat], {
        visible: true,
        editable: true,
        cursor: 'pointer',
        shadowBlur: 0,
        shadowColor: 'black',
        draggable: false,
        dragShadow: false, // display a shadow during dragging
        drawOnAxis: null, // force dragging stick on a axis, can be: x, y
        symbol: {
          markerFile: point.icon
          // 'textFaceName' : 'sans-serif',
          // 'textName' : 'MapTalks',
          // 'textFill' : '#34495e',
          // 'textHorizontalAlignment' : 'right',
          // 'textSize' : 40
        }
      }).addTo(this.vecLayer)
      marker.setInfoWindow(infoWindow)
      return marker
    },
    // 移除全部兴趣点
    removeAllPlacePoints() {
      const self = this
      for (let i = 0; i < self.placePointOverlays.length; i++) {
        self.removeSinglePlacePoint(self.placePointOverlays[i])
      }
    },
    // 是否显示全部兴趣点
    switchChangePOI(e) {
      if (e) {
        this.getPOIList()
      } else {
        this.removeAllPlacePoints()
      }
    },
    // 移除单个兴趣点
    removeSinglePlacePoint(placePoint) {
      const self = this
      self.removeMarker(placePoint)
    },
    // 移除marker
    removeMarker(point) {
      point.remove()
    },
    // 显示POI面板
    showPOIPanel() {
      this.showPOIFlag = !this.showPOIFlag
      if (this.showPOIFlag) {
        this.varshowpoi = true
        if (this.showFence) {
          this.switchShowFence()
        }
        if (this.showManageLine) {
          // 关闭路线管理
          this.ManageLineHandleClose()
        }
        let obj = this.$refs.poiComponentRef
        this.groupOptions = {
          className: 'os-theme-dark os-theme-swd',
          callbacks: {
            onScroll: function() {}
          }
        }
        this.getPOIList()
      } else {
        this.hidePOIPanel()
      }
    },
    // 隐藏POI面板
    hidePOIPanel() {
      this.showPOIFlag = false
      this.removeAllPlacePoints()
    },
    // 展开POI分组
    openPoiGroup(index) {
      let item = this.poiList[index]
      item.open = !item.open
    },
    // 删除POI分组
    deletePOIGroup(item, index) {
      console.log(item)
      if (item.places && item.places.length) {
        this.$alert(this.$t('CUalv8dHu0RQdAaF3Wj-v'), this.$t('OJAZ-TwNVqi3Bh7hztuLC'), {
          confirmButtonText: this.$t('lg.confirm'),
          type: 'error',
          callback: action => {}
        })
      } else {
        this.$confirm(this.$t('zeWy2Egw7v6c3sDhGAX9c', [item.name]), this.$t('OJAZ-TwNVqi3Bh7hztuLC'), {
          confirmButtonText: this.$t('lg.confirm'),
          cancelButtonText: this.$t('lg.cancel'),
          type: 'warning'
        }).then(() => {
          console.log(item)
          deleteGroupPOI({
            placeGroupId: item.placeGroupId
          }).then(res => {
            if (res.ret === 1) {
              this.removeAllPlacePoints()
              this.poiList.splice(index, 1)
              this.$message({
                message: this.$t('lg.success'),
                type: 'success'
              })
            } else {
              this.$message({
                message: this.$t('lg.fail'),
                type: 'error'
              })
            }
          })
        })
      }
    },
    deletePOIItem(data, index, inx) {
      this.$confirm(this.$t('JFQKJUrmNVUCRlOW-U6Qu') + " '" + data.name + "'", this.$t('OJAZ-TwNVqi3Bh7hztuLC'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      }).then(() => {
        deleteItemPOI({
          placeId: data.placeId
        }).then(res => {
          if (res.ret === 1) {
            this.removeAllPlacePoints()
            this.poiList[index].places.splice(inx, 1)
            this.$message({
              message: this.$t('lg.success'),
              type: 'success'
            })
          } else {
            this.$message({
              message: this.$t('lg.fail'),
              type: 'error'
            })
          }
        })
      })
    },
    // 选择兴趣点
    selectItemPOI(data) {
      this.removeAllPlacePoints()
      this.showSinglePoint({
        name: data.name,
        point: {
          lng: data.lon,
          lat: data.lat,
          iconType: data.iconType ? data.iconType : 2
        }
      })
      this.swdMapTalk.setCenter([data.lon, data.lat])
    },
    // 搜索POI
    searchPOIByName() {
      const self = this
      console.log(this.poiparam)
      let param = self.poiparam
      if (param) {
        param = param.trim()
      }
      self.removeAllPlacePoints()
      if (param) {
        searchPlaceGroup({
          name: self.poiparam,
          // 定制用户可在子用户下查询
          targetUserId: self.targetUserId
        }).then(res => {
          if (res.ret === 1) {
            let arr = res.data
            arr = arr.map(item => {
              item.open = true
              return item
            })
            self.poiList = arr
            for (let i = 0; i < this.poiList.length; i++) {
              self.poiList[i].places.map(item => {
                self.showSinglePoint({
                  name: item.name,
                  point: {
                    lng: item.lon,
                    lat: item.lat,
                    iconType: item.iconType ? item.iconType : 2
                  }
                })
              })
            }
          }
        })
      } else {
        self.getPOIList()
      }
    },
    // 显示添加分组或者点
    showAddPOIModal(type) {
      const self = this
      self.poiAddOrEditData = JSON.parse(JSON.stringify(self.poiAddOrEditDataBak))
      if (type === 1) {
        // 添加组
        self.showAddOrEditPOI = true
        self.poiAddOrEditData.title = self.$t('kAKDzRgzgRbGj7TWa8W8z')
        self.poiAddOrEditData.type = 1
        self.poiAddOrEditData.flag = 'add'
      } else if (type === 2) {
        // 添加点
        self.showAddOrEditPOI = true
        self.poiAddOrEditData.placeGroupId = 0
        self.poiAddOrEditData.title = self.$t('I8lfTbBiYbCyvuPzjkf_Z')
        self.poiAddOrEditData.iconType = 1
        self.poiAddOrEditData.type = 2
        self.poiAddOrEditData.lon = self.currentDefaultPoint._coordinates.x
        self.poiAddOrEditData.lat = self.currentDefaultPoint._coordinates.y
        self.poiAddOrEditData.flag = 'add'
      } else if (type === 3) {
        // 批量添加
        self.showAddOrEditPOI = true
        self.poiAddOrEditData.title = self.$t('I8lfTbBiYbCyvuPzjkf_Z')
        self.poiAddOrEditData.type = 3
        self.poiAddOrEditData.flag = 'add'
        this.poiUploadFileUrl = ''
        let handFile = document.getElementById('poi-upload-file')
        if (handFile) {
          handFile.value = ''
        }
      }
    },
    // 隐藏添加分组或者点
    hideAddPOIModal() {
      if (this.currentDefaultPoint) {
        this.currentDefaultPoint.remove()
        this.currentDefaultPoint = null
      }
      this.showAddOrEditPOI = false
    },
    // 点击编辑兴趣点分组
    editPOIRowClick(item, index) {
      const self = this
      self.poiAddOrEditData = JSON.parse(JSON.stringify(self.poiAddOrEditDataBak))
      self.poiAddOrEditData.placeGroupId = item.placeGroupId
      self.poiAddOrEditData.type = 1
      self.poiAddOrEditData.title = self.$t('GOXwqzj3woUoTJ5EcWIfb')
      self.poiAddOrEditData.remark = item.remark
      self.poiAddOrEditData.name = item.name
      self.showAddOrEditPOI = true
      self.poiAddOrEditData.flag = 'edit'
    },
    // 点击编辑兴趣点
    editPOISubRowClick(item, index, inx) {
      const self = this
      self.poiAddOrEditData = JSON.parse(JSON.stringify(self.poiAddOrEditDataBak))
      self.poiAddOrEditData.placeGroupId = item.placeGroupId ? item.placeGroupId : 0
      self.poiAddOrEditData.placeId = item.placeId
      self.poiAddOrEditData.iconType = item.iconType
      self.poiAddOrEditData.type = 2
      self.poiAddOrEditData.title = self.$t('OraWJ32a3r19pXlnEz7LI')
      self.poiAddOrEditData.remark = item.remark
      self.poiAddOrEditData.name = item.name
      self.showAddOrEditPOI = true
      self.poiAddOrEditData.flag = 'edit'
    },
    // 添加分组或者点
    addPostPOI() {
      const self = this
      if (!self.poiAddOrEditData.name) {
        self.$message({
          message: self.$t('CmWMoOrWpZU6sTGtkB4QL'),
          type: 'error'
        })
        return
      }
      if (self.poiAddOrEditData.type === 1) {
        if (self.poiAddOrEditData.flag === 'add') {
          let postData = {
            name: self.poiAddOrEditData.name,
            sequence: self.poiAddOrEditData.sequence,
            remark: self.poiAddOrEditData.remark
          }
          addGroupPOI(postData).then(res => {
            if (res.ret === 1) {
              self.$message({
                message: self.$t('lg.success'),
                type: 'success'
              })
              self.getPOIList()
              self.showAddOrEditPOI = false
            } else {
              self.$message({
                message: self.$t('lg.fail'),
                type: 'error'
              })
            }
          })
        } else {
          let postData = {
            name: self.poiAddOrEditData.name,
            sequence: self.poiAddOrEditData.sequence,
            remark: self.poiAddOrEditData.remark,
            placeGroupId: self.poiAddOrEditData.placeGroupId
          }
          updateGroupPOI(postData).then(res => {
            if (res.ret === 1) {
              self.$message({
                message: self.$t('lg.success'),
                type: 'success'
              })
              self.poiList = self.poiList.map(item => {
                if (item.placeGroupId === self.poiAddOrEditData.placeGroupId) {
                  item.name = self.poiAddOrEditData.name
                  item.remark = self.poiAddOrEditData.remark
                }
                return item
              })
              self.showAddOrEditPOI = false
            } else {
              self.$message({
                message: self.$t('lg.fail'),
                type: 'error'
              })
            }
          })
        }
      } else if (self.poiAddOrEditData.type === 2) {
        if (self.poiAddOrEditData.flag === 'add') {
          let postData = {
            name: self.poiAddOrEditData.name,
            sequence: self.poiAddOrEditData.sequence,
            remark: self.poiAddOrEditData.remark,
            lon: self.poiAddOrEditData.lon,
            lat: self.poiAddOrEditData.lat,
            iconType: self.poiAddOrEditData.iconType,
            mapType: self.mapType
          }
          if (self.poiAddOrEditData.placeGroupId) {
            postData.placeGroupId = self.poiAddOrEditData.placeGroupId
          }
          addItemPOI(postData).then(res => {
            if (res.ret === 1) {
              self.$message({
                message: self.$t('lg.success'),
                type: 'success'
              })
              self.currentDefaultPoint.remove()
              self.currentDefaultPoint = null
              self.getPOIList()
              self.showAddOrEditPOI = false
            } else {
              self.$message({
                message: self.$t('lg.fail'),
                type: 'error'
              })
            }
          })
        } else {
          let postData = {
            name: self.poiAddOrEditData.name,
            remark: self.poiAddOrEditData.remark,
            placeId: self.poiAddOrEditData.placeId,
            placeGroupId: self.poiAddOrEditData.placeGroupId
          }
          updateItemPOI(postData).then(res => {
            if (res.ret === 1) {
              self.$message({
                message: self.$t('lg.success'),
                type: 'success'
              })
              self.showAddOrEditPOI = false
              self.getPOIList()
            } else {
              self.$message({
                message: self.$t('lg.fail'),
                type: 'error'
              })
            }
          })
        }
      }
    },
    // 初始化-绘制我的地点
    initPlacePoint() {
      const self = this
      const drawTool = new maptalks.DrawTool({
        mode: 'Point'
      }).addTo(self.swdMapTalk)
      // 初始化时没有如果没有 vecLayer 需要加上
      if (!self.vecLayer) {
        self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
      }
      drawTool.on('drawend', function(param) {
        self.vecLayer.addGeometry(param.geometry)
        self.currentDefaultPoint = param.geometry
        drawTool.disable()
        self.showAddPOIModal(2)
      })
    },
    // 选择POI图标类型
    selectIconType(index) {
      this.poiAddOrEditData.iconType = index + 1
    },
    // 选择上传文件发生变化
    upLoadFileChange(e) {
      let file = document.querySelector('#poi-upload-file').files[0]
      this.poiUploadFileUrl = file.name
    },
    // 批量更新兴趣点
    submitUploadPOI() {
      let file = document.querySelector('#poi-upload-file').files[0]
      if (!file) {
        this.$message({
          message: this.$t('zSwW84PPwEdSY7KXU0_NP'),
          type: 'error'
        })
        return false
      } else {
        let _name = file.name
        if (_name.split('.')[1] !== 'xls') {
          this.$message({
            message: this.$t('hnmI2xuLjvPN3UZNk3qVe'),
            type: 'error'
          })
          return false
        }
      }
      let formdata = new FormData()
      formdata.append('file', file)
      addBatchItemPOI(formdata).then(res => {
        if (res.ret === 1) {
          this.$message({
            message: this.$t('lg.success'),
            type: 'success'
          })
          this.poiUploadFileUrl = ''
          let handFile = document.getElementById('poi-upload-file')
          handFile.value = ''
          this.showAddOrEditPOI = false
          this.getPOIList()
        } else {
          this.$message({
            message: this.$t('FgXEzLIfSgW9hXdZU2BkT'),
            type: 'error'
          })
        }
      })
    },
    // 绘制教程
    showDrawSteps() {
      console.log('绘制提示')
      this.showDrawFlag = true
    },
    // 判断坐标点是否在地图可视范围内
    isGetBounds: function(lon, lat) {
      var view = this.swdMapTalk.getExtent() //获取当前地图的显示范围
      if (lon > view.xmin && lon < view.xmax && lat > view.ymin && lat < view.ymax) {
        return true // 在范围内
      } else {
        return false ////在范围外
      }
    },
    /* 绘制轨迹
    greenSpeed: 安全速度
    redSpeed：  超速速度
     */
    drawHistoryLine: function(car, historys, setStopTime, isShowPointIcon, greenSpeed = 100, redSpeed = 120) {
      const self = this
      // 如果已经有轨迹，先清除
      if (self.historyMarkers.length > 0) {
        self.cleanHistoryLine()
      }
      if (!self.vecLayer) {
        self.vecLayer = new maptalks.VectorLayer('vector').addTo(self.swdMapTalk)
      }
      // 保存下轨迹数据先
      if (historys.length == 0) return
      // 谷歌地图需要判断设备是在国内还是在国外
      /* eslint-disable */
      // let isInChina = ptInPolygon(historys[0].lonc, historys[0].latc) //坐标点是否在中国的判断算法
      /* eslint-enable */
      // 判断坐标点是否在中国,不在中国则采用原始的国际标准坐标点
      /* if (isInChina != 'cn') {
        for (let i = 0; i < historys.length; i++) {
          historys[i].lonc = historys[i].lon
          historys[i].latc = historys[i].lat
        }
      } */
      let pArr = historys
      self.historyData = pArr // 保存轨迹
      // 有超过1个点
      if (pArr.length > 1) {
        // 遍历所有轨迹，生成需要的标签
        let lngLats = []
        let color = null
        // 绘制点
        for (let i = 0; i < pArr.length; i++) {
          let item = pArr[i]
          lngLats.push(self.newLngLat(item.lonc, item.latc)) // 添加到数组构成线
          let col = self.getColorBySpeed(item.speed, greenSpeed, redSpeed) // 根据速度取出当前颜色
          if (color) {
            if (col !== color) {
              // 如果颜色变化
              let polyline = self.newLine(lngLats, color)
              polyline.addTo(self.vecLayer)
              self.historyMarkers.push(polyline)
              lngLats = [] // 清空轨迹点
              lngLats.push(self.newLngLat(item.lonc, item.latc)) // 添加当前点到数组
              color = col // 更新当前颜色
            }
          } else {
            // 如果没有初始颜色，则取个起点颜色
            color = col
          }
          if (isShowPointIcon) {
            //  三年待机无线设备有此功能
            let dir = IcondirationToText(item.dir)
            let pointOpt = {
              lng: item.lonc,
              lat: item.latc,
              icon: '/images/map/history/dir/' + dir + '.png' // 图标
            }
            if (!isDev) {
              pointOpt.icon = `${packagePath}` + pointOpt.icon
            }
            let pointDir = self.newStopMarker(pointOpt)
            self.historyMarkers.push(pointDir)
          }
        }

        // 绘制轨迹
        if (lngLats.length >= 2) {
          //画线
          let polyline = self.newLine(lngLats, color)
          polyline.addTo(self.vecLayer)
          self.historyMarkers.push(polyline)
        }
        for (let i = 0; i < pArr.length; i++) {
          let item = pArr[i]
          // 后端更正 realStopTime 才是停留时间
          // if (item.isStop && item.stopTime / 60 >= setStopTime) {
          // 后端更正 realStopTime 才是停留时间
          if (item.isStop && item.realStopTime / 60 >= setStopTime && i !== pArr.length - 1) {
            // 判断停车点停车时间大于或等于停留标识的值才显示
            // 画停车点
            let opt = {
              lng: item.lonc, // 位置
              lat: item.latc, // 位置
              icon: '/images/map/history/stopicon.png', // 图标
              markerWidth: 26,
              markerHeight: 33
            }
            if (!isDev) {
              opt.icon = `${packagePath}` + opt.icon
            }
            let stopMarker = self.newStopMarker(opt)
            //IE兼容模式 把 (xx-xx-xx xx:xx:xx)  格式改为 (xx/xx/xx/ xx:xx:xx) 所以匹配replace(/-/g,  "/") 把-替换为/
            let stamp = Date.parse(new Date(item.pointDt.replace(/-/g, '/'))) / 1000 - (new Date().getTimezoneOffset() / 60) * 60 * 60
            item.pointDt = DateToFormatString(stamp * 1000) //13位时间戳转为标准时间格式
            // 12月28日-停留点的开始时间改为 上一个item的pointDt,结束时间改为下一个item的ponitDt,
            // 第一个item不可能为停留点 ，最后一个item若为停留点，结束时间为该item的pointDt
            let preItem = null
            let nextItem = null
            if (i !== pArr.length - 1) {
              preItem = JSON.parse(JSON.stringify(pArr[i - 1]))
              nextItem = JSON.parse(JSON.stringify(pArr[i + 1]))
            } else {
              preItem = JSON.parse(JSON.stringify(pArr[i - 1]))
              nextItem = JSON.parse(JSON.stringify(item))
            }
            let preStamp = Date.parse(new Date(preItem.pointDt.replace(/-/g, '/'))) / 1000 - (new Date().getTimezoneOffset() / 60) * 60 * 60
            preItem.pointDt = DateToFormatString(preStamp * 1000)
            if (i !== pArr.length - 1) {
              let nextStamp = Date.parse(new Date(nextItem.pointDt.replace(/-/g, '/'))) / 1000 - (new Date().getTimezoneOffset() / 60) * 60 * 60
              nextItem.pointDt = DateToFormatString(nextStamp * 1000)
            }
            self.showStopInfoBox(stopMarker, item, car, preItem, nextItem, i)
            // 12月28日-停留点更改
            self.historyMarkers.push(stopMarker)
          }
        }
      }
      // 绘制结束点
      if (pArr.length > 1) {
        let last = pArr[pArr.length - 1]
        let opt = {
          lng: last.lonc, // 位置
          lat: last.latc, // 位置
          //icon: self.lang == 'cn' ? '/images/map/history/endpos.png' : '/images/map/history/endpos_en.png' // 图标
          icon: self.lang == 'cn' ? '/images/map/history/p_end_cn.png' : '/images/map/history/p_end_en.png',
          markerWidth: 26,
          markerHeight: 33,
          zIndex: 99
        }
        if (!isDev) {
          opt.icon = `${packagePath}` + opt.icon
        }
        let lastMarker = self.newStopMarker(opt)
        self.historyMarkers.push(lastMarker)
      }
      // 绘制起始点
      if (pArr.length > 0) {
        let first = pArr[0] //第一个点
        let opt = {
          lng: first.lonc, // 位置
          lat: first.latc, // 位置
          //icon: self.lang == 'cn' ? '/images/map/history/startpos.png' : '/images/map/history/startpos_en.png' // 图标
          icon: self.lang == 'cn' ? '/images/map/history/p_start_cn.png' : '/images/map/history/p_start_en.png',
          markerWidth: 26,
          markerHeight: 33,
          zIndex: 99
        }
        if (!isDev) {
          opt.icon = `${packagePath}` + opt.icon
        }
        let firstMarker = self.newStopMarker(opt)
        self.historyMarkers.push(firstMarker)
      }
    },
    // 历史回放-停车信息窗 car 主要是备用解析地址的时候用到carId，目前无用
    showStopInfoBox: function(marker, data, car, preItem, nextItem, index) {
      const self = this
      // 后端更正 realStopTime 才是停留时间
      let startTime = Date.parse(new Date(data.pointDt.replace(/-/g, '/'))) / 1000 - data.realStopTime
      let _stopTime = parseSeconds(data.realStopTime)
      let _startTime = new Date(startTime * 1000).Format('yyyy-MM-dd hh:mm:ss')
      // 12月26日-停留时间规则更改
      let html = `<div>
          <p>${self.$t('XbOlArDdJLP3YlKnbKEQ0')}：${_stopTime} </p>
          <p>${self.$t('Zbkw-Z-GkLdk92q2GqQb9')}：${preItem.pointDt} </p>
          <p>${self.$t('tjh3v6c9w4iWKRhEsxCel')}：${nextItem.pointDt} </p>
          <p>${self.$t('lg.address')}：<span id="${'mInfo_stopAddress' + index}" class="dataLoading">${this.$t('zdWLkJqxc7V4XODM1RAbn') + '...'}</span> </p>
        </div>`
      let infoWindow = {
        // title: '',
        content: '<div>' + html + '</div>',
        width: 220,
        // minHeight: 50,
        single: false, //是否只显示一个信息框
        autoPan: false,
        custom: false //自定义信息框
        // animation: 'fade'
      }
      self.newInfoWindow(marker, infoWindow)
      marker.on('click', function() {
        marker.openInfoWindow()
        let ele = document.querySelector('#mInfo_stopAddress' + index)
        let classLists = ele.classList
        if (classLists.contains('dataLoading')) {
          loadAddress({ lon: data.lon, lat: data.lat, lonC: data.lonc, latC: data.latc, businessType: 31, carId: car.carId })
            .then(address => {
              $('#mInfo_stopAddress' + index).html(address)
              $('#mInfo_stopAddress' + index).removeClass('dataLoading')
            })
            .catch(() => {
              $('#mInfo_stopAddress' + index).html(self.$t('iWYURze5gw6IuatRIrbE9'))
            })
        }
      })
    },
    // 创建marker信息框
    newInfoWindow: function(marker, infoWindow) {
      marker.setInfoWindow(infoWindow)
      return marker
    },
    slidePlayMarker(car, index, intervalValue, autoPlay) {
      this.markerPlayPause = true //暂停播放animate
      if (this.playMarker) {
        this.playMarker.remove()
      }
      let historys = this.historyData
      let carType
      let icon = ''
      let changeDir = false
      if (car.carType) {
        carType = car.carType
      } else {
        carType = 3 //默认小汽车类型
      }

      if ([1, 2, 16, 22, 31, 32, 33, 34].includes(Number(carType))) {
        //无方向类型图标
        icon = `/images/map/machine/${carType}/green.png`
      } else {
        //有方向类型图标
        changeDir = true
        icon = `/images/map/machine/${carType}/green_0.png`
      }
      if (!isDev) {
        icon = `${packagePath}` + icon
      }

      let opt = {
        lng: historys[index].lonc,
        lat: historys[index].latc, // 位置
        icon: icon, // 获取图标, // 图标
        // icon: icon // 获取图标, // 图标
        //h5页面轨迹跟踪不需要一直在汽车图标头顶顶一个信息窗口，如果有需要可以把这个方法反注释回来
        label: '' + this.getSimpleCarTip(historys[index], car) + ''
      }
      this.playMarker = new maptalks.Marker([opt.lng, opt.lat], {
        visible: true,
        editable: true,
        cursor: 'pointer',
        shadowBlur: 0,
        shadowColor: 'black',
        draggable: false,
        dragShadow: false, // display a shadow during dragging
        drawOnAxis: null, // force dragging stick on a axis, can be: x, y
        zIndex: 100,
        symbol: {
          markerFile: opt.icon,
          markerDx: 0,
          markerDy: 10
        }
      }).addTo(this.vecLayer)

      if (changeDir) {
        let angle
        if (index < historys.length - 1) {
          //非最后的点，方向由当前点和下一个点构成
          angle = this.getAngle(historys[index].lonc, historys[index].latc, historys[index + 1].lonc, historys[index + 1].latc)
        } else {
          //滑到最后一个点了，方向由前面的点和当前点构成
          angle = this.getAngle(historys[index - 1].lonc, historys[index - 1].latc, historys[index].lonc, historys[index].latc)
        }
        let symbol = this.playMarker.getSymbol()
        symbol.markerRotation = 360 - angle.toFixed(4) // marker rotation in degree, clock-wise
        this.playMarker.setSymbol(symbol)
      }
      let infoWindow = {
        title: '',
        content: opt.label,
        // 'width': 300,
        // 'minHeight': 50,
        single: true, //是否只显示一个信息框
        autoPan: false,
        custom: false, //自定义信息框
        dx: 0,
        dy: -10
      }
      this.playMarker.setInfoWindow(infoWindow)
      this.playMarker.openInfoWindow()
      // 调整窗口位置
      this.lockWindowByLngLat(historys[index].lonc, historys[index].latc)

      if (this.slidePlayMarkerTimer) {
        clearTimeout(this.slidePlayMarkerTimer)
      }
      if (autoPlay && historys[index + 1]) {
        //拖拽后自动播放？
        this.slidePlayMarkerTimer = setTimeout(() => {
          this.moveTo1(car, index, intervalValue)
        }, 300)
      }
    },
    moveTo1(car, index, intervalValue) {
      this.markerPlayPause = false
      let historys = this.historyData
      if (!historys[index + 1]) {
        return
      }
      let carType
      let icon = ''
      let changeDir = false
      if (car.carType) {
        carType = car.carType
      } else {
        carType = 3 //默认小汽车类型
      }
      if ([1, 2, 16, 22, 33, 34].includes(Number(carType))) {
        //无方向类型图标
        icon = `/images/map/machine/${carType}/green.png`
      } else {
        //有方向类型图标
        changeDir = true
        icon = `/images/map/machine/${carType}/green_0.png`
      }
      if (!isDev) {
        icon = `${packagePath}` + icon
      }

      let opt = {
        lng: historys[index].lonc,
        lat: historys[index].latc, // 位置
        icon: icon, // 获取图标, // 图标
        // icon: icon // 获取图标, // 图标
        //h5页面轨迹跟踪不需要一直在汽车图标头顶顶一个信息窗口，如果有需要可以把这个方法反注释回来
        // 需要可以把这个方法反注释回来
        label: '' + this.getSimpleCarTip(historys[index], car) + ''
      }
      // 如果还没创建播放点，则创建
      if (this.playMarker == null) {
        this.playMarker = this.newDirMarker(opt)
        let symbol = this.playMarker.getSymbol()
        symbol.markerDx = 0
        symbol.markerDy = 10
        this.playMarker.setSymbol(symbol)
        this.playMarker.openInfoWindow()
        // self.currentIndex = index // 存储当前所在帧
      }
      // if (index === 0) {
      //   this.playMarker.setCoordinates([historys[index].lonc, historys[index].latc])
      // }
      this.currentCar = car
      this.currentIndex = index
      this.playIntervalValue = intervalValue
      this.markerAnimate(changeDir)
    },
    markerAnimate(changeDir) {
      this.currentIndex++
      this.$emit('updatePlayIndex', this.currentIndex - 1)
      let historys = this.historyData
      let current = historys[this.currentIndex - 1]
      let next = historys[this.currentIndex]
      if (!next) {
        this.allMileage = historys[this.currentIndex].mileage
        return
      }
      // 调整窗口位置
      this.lockWindowByLngLat(current.lonc, current.latc)
      let angle = this.getAngle(current.lonc, current.latc, next.lonc, next.latc)
      let dx = subtract(next.lonc, current.lonc)
      let dy = subtract(next.latc, current.latc)
      this.playMarker.setCoordinates([current.lonc, current.latc])

      let infoWindow = this.playMarker.getInfoWindow()
      let label = '' + this.getSimpleCarTip(next, this.currentCar) + ''
      if (infoWindow) {
        infoWindow.setContent(label)
      }
      if (changeDir) {
        this.playMarker.updateSymbol({
          markerRotation: 360 - angle.toFixed(4) // marker rotation in degree, clock-wise
        })
      }

      const intervalMaps = {
        1: { stepCount: 170, duration: 3500 },
        2: { stepCount: 140, duration: 3000 },
        3: { stepCount: 70, duration: 1400 },
        4: { stepCount: 45, duration: 1100 },
        5: { stepCount: 20, duration: 480 },
        6: { stepCount: 6, duration: 120 },
        7: { stepCount: 3, duration: 50 },
        8: { stepCount: 1, duration: 10 }
      }
      let duration = intervalMaps[this.playIntervalValue]?.duration || 480

      this.playMarker.animate(
        {
          translate: [dx, dy]
        },
        {
          duration: duration,
          easing: 'linear',
          //let map focus on the marker
          focus: false
        },
        frame => {
          // console.log(frame.state.playState)
          if (frame.state.playState === 'finished' && !this.markerPlayPause && this.currentIndex < this.historyData.length - 1) {
            this.markerAnimate(changeDir)
          } else if (frame.state.playState === 'finished' && this.currentIndex === this.historyData.length - 1) {
            //最后一个点位补全
            this.$emit('updatePlayIndex', this.currentIndex)
          }
        }
      )
    },
    pauseRemoveMarker() {
      this.markerPlayPause = true
    },
    bindCarTipEvent() {
      if ($) {
        console.log($('.simpleCarTip .close').length)
        $('.simpleCarTip .close').click(function() {
          console.log('111111111111')
        })
      }
    },
    getSimpleCarTip(pos, car) {
      // console.log('pos---', pos)
      const self = this
      let historys = self.historyData
      let isWire = car.wiretype

      let html = ''
      let showTime = 'hide'
      let stopTime = ''
      // 后端更正 realStopTime 才是停留时间
      // if (pos.stopTime) {
      //   showTime = ''
      //   stopTime = parseSeconds(pos.stopTime)
      // }
      if (pos.realStopTime) {
        showTime = ''
        stopTime = parseSeconds(pos.realStopTime)
      }
      let time = ''
      time = TimeUTCToLocal(pos.pointDt)
      this.allMileage = pos.mileage
      let imgArr = self.replayImgs
      if (!isDev) {
        imgArr = self.replayImgs.map(item => {
          item = `${packagePath}` + item
          return item
        })
      }

      if (self.lang === 'cn' || self.lang === 'tw') {
        html = `
        <div class="simpleCarTip">
          <div class="carname">${car.machineName}</div>
          <div class="row">
            <div class="col-45">${self.$t('dlW7RH6fkSItw_Mtz2Ykw') + ' '}${MeterToKmMi(pos.speed * 1000)}${self.miledgeUnitEN}/h</div>
            <div class="col-55">${self.$t('lg.time') + ' '}${time}</div>
          </div>
          <div class="row">
            <div class="col-45">${self.$t('D-3CnkxFwEGtcITqH7fbB') + ' '}${MeterToKmMi(parseFloat(self.allMileage)) + self.miledgeUnitCN}</div>
            <div class="col-55 col-4">${self.$t('asduiijoh_jkakjsdui15') + ' '}${pos.latc + ',' + pos.lonc}</div>
          </div>
        </div>
      `
      } else {
        html = `
          <div class="simpleCarTip">
            <div class="carname">${car.machineName}</div>
            <div class="row">
              <div class="col"><img class="img1" src="${imgArr[0]}"/>${MeterToKmMi(pos.speed * 1000)}${self.miledgeUnitEN}/h</div>
              <div class="col"><img class="img2" src="${imgArr[1]}"/>${time}</div>
            </div>
            <div class="row">
              <div class="col"><img class="img3" src="${imgArr[2]}"/>${MeterToKmMi(parseFloat(self.allMileage)) + self.miledgeUnitCN}</div>
              <div class="col"><img class="img4" src="${imgArr[4]}"/>${pos.latc + ',' + pos.lonc}</div>
            </div>
          </div>
        `
      }
      return html
    },
    // 轨迹回放刷新Makert上的信息
    refreshMarker(marker, opt) {
      const self = this
      if (marker) {
        if (opt.lng && opt.lat) {
          // 如果指明了位置，就刷新位置
          self.updateMarkerPosition(marker, opt.lng, opt.lat)
        }
        if (opt.icon) {
          // 如果指明了图标
          marker.setSymbol({
            markerFile: opt.icon
          })
        }
        if (opt.label) {
          let infoWindow = marker.getInfoWindow()
          if (infoWindow) {
            setTimeout(function() {
              infoWindow.setContent(opt.label)
            }, 50)
          }
        }
      }
    },
    // 锁定视窗
    lockWindowByLngLat(lng, lat) {
      let boolean = this.isGetBounds(lng, lat)
      if (!boolean) {
        this.swdMapTalk.setCenter([lng, lat])
      }
    },
    getIcon(dir) {
      let _dir = 0
      if (isNaN(dir) || dir < 0 || dir > 360) {
        _dir = 0
      } else {
        _dir = Math.round(dir / 45) % 8
      }
      return '/images/dir/' + _dir + '.png'
    },
    // 获取角度
    getAngle: function(x1, y1, x2, y2) {
      let dx = x2 - x1
      let dy = y2 - y1
      let rad = Math.atan(Math.abs(dx / dy))
      let angle = (rad * 180.0) / Math.PI
      if (dx > 0 && dy <= 0)
        // 二象限
        return 180 - angle
      else if (dx <= 0 && dy < 0)
        // 三象限
        return 180 + angle
      else if (dx < 0 && dy >= 0)
        // 四象限
        return 360 - angle
      else return angle
    },
    // 绘制线
    newLine: function(lineArr, color) {
      let fileName = null
      color === '#3ff419' ? (fileName = 'green') : (fileName = color)
      let lineUrl = '/images/map/history/' + fileName + '_road.png'
      if (!isDev) {
        lineUrl = `${packagePath}/images/map/history/` + fileName + '_road.png'
      }
      let line = new maptalks.LineString(lineArr, {
        symbol: {
          linePatternFile: lineUrl,
          lineWidth: 9,
          lineJoin: 'round', //miter, round, bevel
          lineCap: 'round', //butt, round, square
          lineDasharray: null, //dasharray, e.g. [10, 5, 5]
          'lineOpacity ': 1
        }
      })
      return line
    },
    // 根据经度，纬度创建位置点
    newLngLat: function(lng, lat) {
      return {
        x: lng,
        y: lat
      }
    },
    // 清空轨迹图层
    cleanHistoryLine() {
      const self = this
      let overlays = self.historyMarkers
      for (let i = 0; i < overlays.length; i++) {
        self.removeMarker(overlays[i])
      }
      // 清除历史轨迹
      self.historyMarkers = []
      self.historyData = []
      // 如果创建了播放标记
      if (self.playMarker) {
        self.removeMarker(self.playMarker)
        self.playMarker = null
      }
      if (this.requestAnimationFrameId) {
        cancelAnimationFrame(this.requestAnimationFrameId)
      }
    },
    tableRowClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        return 'cellName'
      }
    },
    tableHeaderClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        return 'cellName'
      }
    },
    // 检查是否定制用户
    checkPOICustomUser() {
      let userId = this.$cookies.get('userId')
      this.loginUserId = userId
      isPOICustomUserCheck({ userId: userId }).then(res => {
        if (res.ret && res.data && res.data.poiSub == 1) {
          this.isPOICustomUer = true
        }
      })
    },
    // 车辆信息弹窗异步解析地址
    carInfoLoadAddress(marker, car) {
      let self = this
      let infoWindow = marker.getInfoWindow()
      // if (infoWindow.getContent().indexOf('loading...') !== -1) {
      // console.log('执行车辆信息弹窗异步解析地址吗？', infoWindow.getContent().indexOf(this.$t('3DYXD-6GdyKqIdA4QkIPq')) !== -1)
      if (infoWindow.getContent().indexOf(this.$t('3DYXD-6GdyKqIdA4QkIPq')) !== -1) {
        // console.log('地址解释---')
        // let selectedCar = self.carsMap[car.carId]
        let _carStatus = car.carStatus
        // 进行地址解析--替换内容
        loadAddress({
          lon: _carStatus.lon,
          lat: _carStatus.lat,
          lonC: _carStatus.lonc,
          latC: _carStatus.latc,
          businessType: 12,
          carId: car.carId
        }).then(address => {
          let addr = this.$t('3DYXD-6GdyKqIdA4QkIPq')
          this.$emit('trackPageUpdeteLocation', address)
          let html = infoWindow.getContent().replace(addr, address)
          infoWindow.setContent(html)
          self.bindEventInfo() // 绑定弹窗里的相关点击事件
        })
      }
    },
    shareUrlSuccess(url) {
      this.shareHistoryUrl = url
      this.shareHistoryResultFlag = true
    },
    // 报警铃声切换
    toggleAlarmSound(e) {
      this.audioBtnStatus = e
      if (!e) {
        this.$refs.audio.pause() //暂停
      }
      // 设置用户偏好
      addUserPerFence({
        token: this.$cookies.get('token'),
        userId: this.$cookies.get('userId'),
        userName: this.$cookies.get('userName'),
        perfenceId: 59,
        perfenceString: this.audioBtnStatus ? 1 : 0
      }).then(res => {
        console.log(res)
      })
    },
    // 播放报警铃声
    playAudio() {
      if (this.$refs.audio && this.audioBtnStatus) {
        this.$refs.audio.play()
      }
    },
    // 获取报警铃声--用户偏好
    queryUserAlarmAudioPerFence() {
      queryUserPerFence({
        userId: this.$cookies.get('userId'),
        perfenceId: 59
      }).then(res => {
        if (res.ret == 1) {
          if (res.data.perfenceString == 1) {
            this.audioBtnStatus = true
          } else {
            this.audioBtnStatus = false
          }
        }
      })
    },
    /*
     type  1:新增 2：编辑
     data  1:描点 2：轨迹 3：导航
     */
    toManageLine(type, data) {
      let params = null
      if (type === 1) {
        params = {
          type: data,
          isUpdate: false
        }
      } else if (type === 2) {
        params = data
        params.isUpdate = true
      }
      const { href } = this.$router.resolve({
        name: 'LineSetting',
        query: params
      })
      window.open(href, '_blank')
    },
    // 画框查车
    searchCarNumber() {
      const self = this
      // 指针设置
      self.swdMapTalk.setCursor('crosshair')
      if (self.distanceInfoWindow) {
        self.distanceInfoWindow.remove()
      }
      // 初始化时没有如果没有 vecLayer 需要加上
      if (!this.vecLayer) {
        this.vecLayer = new maptalks.VectorLayer('vector').addTo(this.swdMapTalk)
      }
      self.drawTool = new maptalks.DrawTool({
        mode: 'Rectangle',
        once: true,
        symbol: {
          lineColor: '#3370ff',
          lineWidth: 2,
          polygonFill: '#3370ff',
          polygonOpacity: 0.4
        }
      }).addTo(self.swdMapTalk)
      self.swdMapTalk.setCursor('pointer')
      self.drawTool.on('drawstart', function(event) {
        let options = {
          autoPan: false,
          single: true,
          custom: true,
          dx: 50,
          dy: 40,
          content:
            '<div class="distanceTip" style="box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);border-radius: 6px;white-space:nowrap;background:#fff;padding:8px 12px;font-size: 14px;font-weight: 400;" >' +
            self.$t('wegasqASDE32446sgwe42') +
            '</div>',
          animation: 'none'
        }
        let infoWindow = (self.distanceInfoWindow = new maptalks.ui.InfoWindow(options))
        infoWindow.addTo(self.swdMapTalk).show(event.coordinate)
      })
      self.drawTool.on('mousemove', function(event) {
        self.distanceInfoWindow.show(event.coordinate)
      })
      self.drawTool.on('drawend', function(param) {
        self.swdMapTalk.setCursor('auto')
        self.vecLayer.clear()
        self.distanceInfoWindow.remove()
        self.vecLayer.bringToFront()
        if (self.$parent.showLeftBox) {
          self.$parent.showLeftBox = false
        }
        let points = ''
        param.geometry._coordinates.forEach((item, index) => {
          param.geometry._coordinates.length - 1 === index ? (points += item.x + ',' + item.y) : (points += item.x + ',' + item.y + ';')
        })
        self.$emit('frameCheck', points)
      })
    },
    // 区域查车
    showRegionalDialog() {
      // 清空画框查车工具
      if (this.drawTool) {
        this.swdMapTalk.setCursor('auto')
        this.drawTool.disable()
        this.drawTool = null
      }
      this.showAreaDialog()
      this.isRegionalCarSearch = true
    },
    // 改变路线管理模态框
    changeManageLineModal() {
      this.showManageLine = !this.showManageLine
      if (this.showManageLine) {
        // 关闭围栏modal
        this.showFence = true
        this.switchShowFence()
        // 关闭POI
        this.showPOIFlag = true
        this.showPOIPanel()
        // 初始查询
        this.searchLineByName()
      } else {
        this.ManageLineHandleClose()
      }
    },
    // 根据线路名称查询线路
    searchLineByName(val = 1) {
      this.manageLineTableData.pageIndex = val
      let params = {
        mapType: parseInt(this.mapType),
        name: this.currentLineName,
        pageIndex: this.manageLineTableData.pageIndex,
        pageSize: this.manageLineTableData.pageSize
      }
      queryPathLine(params).then(res => {
        if (res.ret) {
          this.manageLineTableData.data = res.data
          this.manageLineTableData.total = res.total
        } else {
          this.manageLineTableData.data = []
          this.manageLineTableData.total = 0
          // this.$message.error(this.$t('dFkTJxdatpPvSNfTqBOLk'))
        }
      })
    },
    toSettingAlarm() {
      const { href } = this.$router.resolve({
        name: 'LineAlarmSetting',
        query: null
      })
      window.open(href, '_blank')
    },
    // 关闭线路管理 clearLineVecLayer
    ManageLineHandleClose() {
      this.clearLineVecLayer()
      this.showManageLine = false
    },
    clearLineVecLayer() {
      // 清空路线层
      this.swdMapTalk.removeLayer(this.lineVecLayer)
      this.lineVecLayer = null
    },
    selectLine(e) {
      if (e.length === 0) {
        this.clearLineVecLayer()
        return
      }
      let self = this
      if (!this.lineVecLayer) {
        this.lineVecLayer = new maptalks.VectorLayer('lineVector').addTo(this.swdMapTalk)
      }
      e.forEach(item => {
        self.drawTrackLine(item)
      })
    },
    // 编辑线路
    editLine(row) {
      this.toManageLine(2, row)
    },
    // 删除线路
    deleteLine(row) {
      const self = this
      if (row.bound) {
        this.$confirm(this.$t('wegasqASDE32446sgwe10'), '', {
          confirmButtonText: this.$t('lg.confirm'),
          cancelButtonText: this.$t('lg.cancel'),
          type: 'warning',
          showClose: false
        })
          .then(() => {
            deletePathLine(row.id).then(res => {
              if (res.ret) {
                this.$message.success(this.$t('cvyH4iJuWCiLfB_Wsn4Nn'))
                this.searchLineByName()
              } else {
                if (res.code != '30080') {
                  this.$message.error(res.msg)
                }
              }
            })
          })
          .catch(() => {})
      } else {
        deletePathLine(row.id).then(res => {
          if (res.ret) {
            this.$message.success(this.$t('cvyH4iJuWCiLfB_Wsn4Nn'))
            this.searchLineByName()
          } else {
            if (res.code != '30080') {
              this.$message.error(res.msg)
            }
          }
        })
      }
    },
    // 绘制线路管理下的 轨迹
    drawTrackLine(data) {
      const self = this
      if (!this.lineVecLayer) {
        this.lineVecLayer = new maptalks.VectorLayer('lineVector').addTo(this.swdMapTalk)
      }
      let points = []
      data.pointsC.split(';').forEach(item1 => {
        points.push({ x: item1.split(',')[0], y: item1.split(',')[1] })
      })
      // 有起始点，则先绘制起始点
      if (points.length > 0) {
        let first = points[0] //第一个点
        let icon = ''
        let label = this.setCustomWindow(data.startAddress)
        this.lang === 'cn' ? (icon = require('@/assets/img/map/startAddress_cn.png')) : (icon = require('@/assets/img/map/startAddress_en.png'))
        let opt = {
          lng: first.x, // 位置
          lat: first.y, // 位置
          icon: icon,
          markerWidth: 26,
          markerHeight: 33,
          label: label,
          id: first.x + first.y.toString()
        }
        let infoWindow = {
          title: '',
          content: label,
          single: false, //是否只显示一个信息框
          autoPan: false,
          custom: true,
          dx: 0,
          dy: -8
        }
        this.startAndEndMarker.startMaker = this.newStopMarker(opt, 'lineVecLayer')
      }
      // 有超过1个点
      if (points.length > 1) {
        // 生成结束点
        let last = points[points.length - 1]
        let icon = ''
        let label = this.setCustomWindow(data.endAddress)
        this.lang === 'cn' ? (icon = require('@/assets/img/map/endAddress_cn.png')) : (icon = require('@/assets/img/map/endAddress_en.png'))
        let opt = {
          lng: last.x, // 位置
          lat: last.y, // 位置
          icon: icon,
          markerWidth: 26,
          markerHeight: 33,
          label: label,
          id: last.x + last.y.toString()
        }
        let infoWindow = {
          title: '',
          content: label,
          single: false, //是否只显示一个信息框
          autoPan: false,
          custom: true,
          dx: 0,
          dy: -8
        }
        this.startAndEndMarker.endMaker = this.newStopMarker(opt, 'lineVecLayer')
        this.swdMapTalk.setCenterAndZoom([parseFloat(points[0].x), parseFloat(points[0].y)], 15)
        new maptalks.LineString(points, {
          symbol: {
            lineWidth: data.lineWidth,
            lineJoin: 'round', //miter, round, bevel
            lineCap: 'round', //butt, round, square
            lineDasharray: null, //dasharray, e.g. [10, 5, 5]
            'lineOpacity ': 1,
            lineColor: data.lineColor
          }
        }).addTo(self.lineVecLayer)
      }
      this.startAndEndMarker.startMaker.on('click', function() {
        this.timer = setTimeout(() => {
          if ($ && $('#customWindowModal .close').length) {
            $('#customWindowModal .close').click(function() {
              self.startAndEndMarker.startMaker.closeInfoWindow()
            })
          }
          clearTimeout(self.timer)
        }, 500)
      })
      this.startAndEndMarker.endMaker.on('click', function() {
        this.timer = setTimeout(() => {
          if ($ && $('#customWindowModal .close').length) {
            $('#customWindowModal .close').click(function() {
              self.startAndEndMarker.endMaker.closeInfoWindow()
            })
          }
          clearTimeout(self.timer)
        }, 500)
      })
    },
    // 标注点位 data: 图片路径
    markPoint(data, type = 'lineVecLayer', params) {
      const self = this
      self.swdMapTalk.setCursor('pointer')
      const icon = data
      self.swdMapTalk.once('click', function(param) {
        self.swdMapTalk.setCursor('auto')
        let opt = {
          lng: param.coordinate.x, // 位置
          lat: param.coordinate.y, // 位置
          icon: icon,
          markerWidth: 26,
          markerHeight: 33,
          id: icon
        }
        self.$emit('getNavigationCoordinate', param.coordinate, params)
        self.newStopMarker(opt, type)
      })
    },
    // 删除图形
    // type: 'lineVecLayer'路线管理层
    // deleteIndex 删除的图片下标
    // allCount
    deleteMarker(id, type = 'lineVecLayer', deleteIndex, allCount) {
      let marker = null
      // 路线管理
      if (type === 'lineVecLayer') {
        if (!this.lineVecLayer) {
          return
        }
        marker = this.lineVecLayer.getGeometryById(id)
        if (marker) {
          this.removeMarker(marker)
        }
        // 途经点变更
        for (let i = deleteIndex; i < allCount; i++) {
          let wayPoint = this.lineVecLayer.getGeometryById(require(`@/assets/img/map/wayPoint/${i + 1}.png`))
          if (i !== 0 && wayPoint) {
            wayPoint.updateSymbol({ markerFile: require(`@/assets/img/map/wayPoint/${i}.png`) })
            wayPoint.setId(require(`@/assets/img/map/wayPoint/${i}.png`))
          }
        }
      } else {
        if (!this.vecLayer) {
          return
        }
        marker = this.vecLayer.getGeometryById(id)
      }
      if (marker) {
        this.removeMarker(marker)
      }
    },
    // 封装自定义简单信息框
    setCustomWindow(content, title = '') {
      let _htmlstr = `<div id="customWindowModal">
                   <div class="customWindowHeader">
                     <span>${title}</span><div class="close"></div>
                   </div>
                   <div class="customWindowBody">
                     <div class="col"><span class="content">${content}</span></div>
                   </div>
                 </div>`
      return _htmlstr
    },
    // 线路管理，输入框正向解析地址，打点
    setLineVecLayerPoint(param) {
      let option = {
        lng: param.coordinate.x, // 经度
        lat: param.coordinate.y, // 纬度
        icon: param.icon,
        markerWidth: 26,
        markerHeight: 33,
        id: param.icon
      }
      this.swdMapTalk.setCenter([param.coordinate.x, param.coordinate.y])
      this.newStopMarker(option, 'lineVecLayer')
    },
    // 打开免责声明弹框
    openPasswordCheckDialog(type, data) {
      this.fenceInfo = { type: type, detail: data }
      this.showPasswordCheck = true
    },
    // 免责声明密码校验成功
    VerifySuccess() {
      let { type, detail } = this.fenceInfo
      console.log('this.fenceInfo', this.fenceInfo)
      switch (type) {
        case 'update':
          this.updateFenceServer(detail)
          break
        case 'add':
          this.addFenceServer(detail)
          break
      }
    },
    keyDownEvent(event) {
      let e = event || window.event || arguments.callee.caller.arguments[0]
      let keyCode = e.keyCode || e.which || e.charCode
      if (keyCode == 90 && e.ctrlKey) {
        //ctrl+Z 撤销
        this.drawTool && this.drawTool.undo && this.drawTool.undo()
      }
      if (keyCode == 86 && e.ctrlKey) {
        //ctrl+V 前进
        this.drawTool && this.drawTool.redo && this.drawTool.redo()
      }
    }
  },
  beforeDestroy() {
    if (this.alarmTimer) {
      clearInterval(this.alarmTimer)
      this.alarmTimer = null
    }
    if (this.dirRefreshTimer) {
      clearTimeout(this.dirRefreshTimer)
      this.dirRefreshTimer = null
    }

    if (this.alarmNoteTimeout) {
      clearInterval(this.alarmNoteTimeout)
      this.alarmNoteTimeout = null
    }
    if (this.activeCarTimeout) {
      clearTimeout(this.activeCarTimeout)
      this.activeCarTimeout = null
    }
    if (this.alarmTimeout) {
      clearTimeout(this.alarmTimeout)
      this.alarmTimeout = null
    }
    document.removeEventListener('keydown', this.keyDownEvent)
  },
  destroyed() {
    window.maptalks = null
    window.mapboxgl = null
  }
}
</script>

<style lang="scss" scoped>
$controlColor: #262626;
.fixed {
  position: fixed;
}
#fenceControl {
  position: absolute;
  width: 385px;
  height: 645px;
  right: 60px;
  top: 124px;
  z-index: 99;
  border-radius: 5px;
  background-color: #ffffff;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.15);
  .fence-header {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    height: 56px;
    line-height: 56px;
    padding: 0 12px;
    box-sizing: border-box;
    color: #333333;
    font-size: 16px;
    font-weight: bold;
    z-index: 99;
    /* cursor: move; */
    border-bottom: 1px solid #f6f6f6;
    .close {
      position: absolute;
      right: 9px;
      top: 12px;
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
      img {
        display: inline-block;
        width: 14px;
        height: 14px;
      }
    }
  }
  .fence-content {
    position: relative;
    height: 100%;
    padding-top: 56px;
    box-sizing: border-box;
    font-size: 14px;
  }
  .fence-btn {
    display: flex;
    align-items: center;
    margin-top: 24px;
    margin-bottom: 20px;
    font-size: 14px;
    padding: 0 12px;
    color: #333333;
    font-weight: 400;
    .btn-label {
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    .info {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    .btn-f {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      cursor: pointer;
      background-color: #ebf3ff;
      border-radius: 4px;
      width: 36px;
      height: 26px;
      box-sizing: border-box;
      &:hover {
        border: 1px solid $primary;
      }
      .circle {
        width: 16px;
        height: 16px;
      }
      .polygon {
        width: 16px;
        height: 15px;
      }
      .area {
        width: 12px;
        height: 14px;
      }
    }
  }
  .fence-search {
    margin: 0 12px;
    ::v-deep .el-input__inner {
      padding-left: 12px;
    }
    .fence-s {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      color: $primary;
      cursor: pointer;
      .svg-icon {
        width: 18px;
        height: 18px;
      }
      img {
        width: 18px;
        height: 18px;
      }
    }
  }
  .fence-tabs {
    display: flex;
    flex-direction: row;
    margin: 0 10px;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    color: $primary;
    .btn {
      margin-right: 24px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
    }
    .fence-label-show {
      position: absolute;
      right: 10px;
    }
  }
  .table-height {
    ::v-deep .el-table__body-wrapper {
      &::-webkit-scrollbar {
        width: 7px;
        height: 7px;
        background-color: #fff;
        cursor: pointer;
        border-radius: 8px;
      }
      &::-webkit-scrollbar-thumb {
        border-style: dashed;
        background-color: rgba(157, 165, 183, 0.4);
        border-color: transparent;
        border-width: 2px;
        background-clip: padding-box;
      }
    }
    ::v-deep .el-table-column--selection {
      .cell {
        text-align: center;
      }
    }
    ::v-deep .el-table__header-wrapper {
      th {
        font-size: 14px;
        font-weight: 500;
        color: #333333;
      }
    }
    ::v-deep .el-table {
      .cellName {
        .cell {
          padding-left: 0px;
          padding-right: 0px;
        }
      }
    }
    .col-hidden {
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .el-tooltip {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      img {
        display: inline-block;
        margin-right: 5px;
      }
      .circle {
        height: 14px;
        width: 14px;
      }
      .polygon {
        width: 14px;
        height: 13px;
      }
      .area {
        width: 11px;
        height: 12px;
      }
    }
    .typeimg {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .fence-table {
    margin: 0 12px;
    ::v-deep .el-table__header-wrapper {
      th {
        background-color: #fafafa;
      }
    }
    .monitor-pagina {
      margin-top: 4px;
      margin-left: 0;
      text-align: center;
      ::v-deep .el-pagination__jump {
        margin-left: 0;
      }
    }
  }
}
.poiModal {
  position: absolute;
  right: 60px;
  top: 124px;
  z-index: 99;
  width: 385px;
  background: #ffffff;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  .poiheader {
    position: relative;
    display: flex;
    align-items: center;
    height: 56px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    padding: 0 12px;
    .close {
      position: absolute;
      right: 9px;
      top: 12px;
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
      img {
        display: inline-block;
        width: 14px;
        height: 14px;
      }
    }
  }
  .poicenter {
    padding: 24px 32px 28px 32px;
    border-bottom: 1px solid #f6f6f6;
    border-top: 1px solid #f6f6f6;
    .row {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      margin-bottom: 24px;
      ::v-deep .el-input__inner {
        width: 216px;
      }
      ::v-deep .el-textarea__inner {
        width: 216px;
      }
    }
    .iconType {
      display: flex;
      flex-direction: row;
      .iconImg {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        cursor: pointer;
        &:hover {
          background-color: #f1f1f1;
        }
        &.active {
          background-color: #f1f1f1;
        }
      }
    }
    ::v-deep .el-select__caret.el-input__icon.el-icon-arrow-up::before {
      content: '\e6e1';
    }
    .label {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;
      height: 32px;
    }
    .upload_row {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 32px;
      ::v-deep .el-input__inner {
        width: 200px;
      }
      ::v-deep .el-input {
        display: none;
        opacity: 0;
        width: 200px;
      }
      .upBtn {
        display: flex;
        height: 32px;
        padding: 0 16px;
        align-items: center;
        background-color: $primary;
        border-radius: 4px;

        font-weight: 400;
        color: #ffffff;
        cursor: pointer;
        img {
          width: 12px;
          height: 13px;
          margin-right: 3px;
        }
      }
      .filename {
        height: 32px;
        margin-left: 4px;
      }
      .upWrap {
        display: flex;
        flex-direction: row;
      }
      .downBtn {
        line-height: 32px;
        display: inline-block;
        color: $primary;
        font-size: 12px;
        img {
          width: 10px;
          height: 10px;
          margin-left: 4px;
        }
      }
    }
    .uploadtip {
      font-size: 12px;
      color: #c5c5c5;
      margin: 7px 0px 21px 0px;
    }
    .infotip {
      padding: 20px 22px;
      background: #e6f7ff;
      border-radius: 4px;
      border: 1px solid #91d5ff;
      color: #666666;

      .u_row {
        vertical-align: bottom;
        line-height: 22px;
        img {
          display: inline-block;
          vertical-align: bottom;
        }
      }
    }
  }
  .poibody {
    padding: 12px 12px 0 12px;
    border-bottom: 1px solid #f6f6f6;
    border-top: 1px solid #f6f6f6;
    .poisearch {
      .poi-s {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 32px;
        height: 32px;
        cursor: pointer;
        img {
          width: 18px;
          height: 18px;
        }
      }
    }
    .operate {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin: 11px 0;
      .btnrow {
        display: flex;
        flex-direction: row;
      }
      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        padding: 0 14px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid $primary;

        font-weight: 400;
        font-size: 12px;
        cursor: pointer;
        &.one {
          background-color: $primary;
          color: #ffffff;
          margin-right: 17px;
        }
        &.more {
          background-color: #ffffff;
          color: $primary;
        }
        img {
          width: 11px;
          height: 11px;
          margin-right: 6px;
        }
      }
    }
    .groups {
      height: 440px;
      font-size: 12px;
      padding-left: 12px;

      font-weight: 400;
      color: #333333;
      .groupItem {
        padding-right: 10px;
        .item-opera {
          display: flex;
          align-items: center;
          .svg-icon {
            display: none;
            margin-left: 12px;
            cursor: pointer;
            &:hover {
              color: $primary;
            }
          }
        }
      }
      .groupName {
        display: flex;
        height: 36px;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f6f6f6;
        cursor: pointer;
        .item-opera {
          position: relative;
        }
        .drown {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          width: 20px;
          cursor: pointer;
          transform: rotateZ(-90deg);
          transition: transform 0.3s;
          &.open {
            transform: rotateZ(0deg);
          }
          img {
            width: 14px;
            height: 14px;
          }
        }
        &:hover {
          .svg-icon {
            display: block;
          }
        }
      }
      .subItem {
        display: flex;
        height: 36px;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding-left: 13px;
        border-bottom: 1px solid #f6f6f6;
        cursor: pointer;
        &:hover {
          .svg-icon {
            display: block;
          }
        }
      }
    }
  }
  .poibottom {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 52px;
    padding: 0 16px;
    .cancel {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30px;
      padding: 0 14px;
      box-sizing: border-box;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      background-color: #ffffff;

      font-weight: 400;
      font-size: 12px;
      cursor: pointer;
      color: #666666;
      margin-right: 8px;
    }
    .poiaddgroup {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30px;
      padding: 0 14px;
      box-sizing: border-box;
      border-radius: 4px;
      border: 1px solid $primary;
      background-color: $primary;

      font-weight: 400;
      font-size: 12px;
      cursor: pointer;
      color: #ffffff;
      img {
        width: 11px;
        height: 11px;
        margin-right: 6px;
      }
    }
  }
}
::v-deep .drawTipModal {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
  .drawheader {
    position: relative;
    display: flex;
    align-items: center;
    height: 56px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    padding: 0 24px;
    .close {
      position: absolute;
      top: 20px;
      right: 24px;
      width: 16px;
      height: 16px;
      background-image: url('~@/assets/img/close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
  }
  .drawbody {
    border-top: 1px solid #f6f6f6;
    padding: 16px 24px;
  }
  .drawItem {
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0px;
    }
    .drawName {
      font-size: 14px;

      font-weight: 400;
      line-height: 20px;
      margin-bottom: 14px;
      color: #333333;
    }
    .drawSteps {
      .step {
        position: relative;
        font-size: 12px;

        font-weight: 400;
        color: #666666;
        line-height: 22px;
        padding-left: 16px;
        padding-bottom: 10px;
        &:first-child {
          &::before {
            height: 100%;
            top: 11px;
          }
        }
        &:last-child {
          &::before {
            height: 100%;
            top: -15px;
          }
        }
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          width: 2px;
          border-radius: 2px;
          background-color: #f6f6f6;
        }
        img {
          position: absolute;
          width: 10px;
          height: 10px;
          left: -4px;
          top: 7px;
        }
      }
    }
  }
}
.map-container {
  position: relative;
  height: 100%;
  width: 100%;
}

#mapPanel {
  position: relative;
  height: 100%;
  width: 100%;
}
#mapBarControl {
  position: absolute;
  display: flex;
  flex-direction: column;
  top: 122px;
  right: 28px;
  z-index: 99;
  width: 40px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.15);
  &.showTxt {
    .barItem {
      height: 60px;
      .txt {
        display: block;
        color: #000000;
      }
      &:hover {
        ::v-deep .svg__fill {
          fill: $primary;
        }
        ::v-deep .svg__stroke {
          stroke: $primary;
        }
        .txt {
          color: $primary;
        }
      }
    }
  }
  .barItem {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 40px;
    cursor: pointer;
    .icon_tools_box {
      text-align: center;
      display: inline-block;
    }
    .txt {
      display: none;
      margin-top: 8px;
      font-size: 12px;
      color: #666666;
      font-weight: 400;
    }
    img {
      display: block;
      width: 17px;
      height: 17px;
    }
    &:hover {
      color: $primary;
      ::v-deep .icon-svg {
        g {
          // fill: $primary;
          stroke: $primary;
        }
      }
      ::v-deep .icon-svg-map {
        g {
          fill: $primary;
        }
      }
      ::v-deep .icon-svg-satellite {
        g {
          fill: $primary;
          stroke: $primary;
        }
      }
      ::v-deep .icon-svg-fence {
        g {
          fill: $primary;
        }
      }
      ::v-deep .icon-svg-line {
        path {
          fill: $primary;
        }
      }
      ::v-deep .icon-svg-poi {
        text {
          fill: $primary;
        }
        tspan {
          fill: $primary;
        }
      }
      ::v-deep .icon-svg-tool {
        g {
          stroke: $primary;
        }
      }
      ::v-deep .icon-svg-more {
        path {
          fill: $primary;
        }
      }
      ::v-deep .icon-svg-kefu {
        path {
          fill: $primary;
        }
      }
      ::v-deep .icon-svg-msg {
        path {
          fill: $primary;
        }
      }
    }
    ::v-deep .icon-svg-map.active {
      g {
        fill: $primary;
      }
    }
    ::v-deep .icon-svg-satellite.active {
      g {
        fill: $primary;
        stroke: $primary;
      }
    }
    ::v-deep .icon-svg-fence.active {
      g {
        fill: $primary;
      }
    }
    ::v-deep .icon-svg-line.active {
      path {
        fill: $primary;
      }
    }
    ::v-deep .icon-svg-poi.active {
      text {
        fill: $primary;
      }
      tspan {
        fill: $primary;
      }
    }
    ::v-deep .icon-svg-tool.active {
      g {
        stroke: $primary;
      }
    }
    ::v-deep .icon-svg-more.active {
      path {
        fill: $primary;
      }
    }
    .icon {
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 100% 100%;
    }
    .icon_map {
      width: 19px;
      height: 17px;
      background-image: url('~@/assets/img/icon/defaultmap.png');
    }
    .icon_satellite {
      width: 20px;
      height: 20px;
      background-image: url('~@/assets/img/icon/satellite.png');
      &.active {
        background-image: url('~@/assets/img/icon/satellite2.png');
      }
    }
    .icon_fence {
      width: 18px;
      height: 15px;
      background-image: url('~@/assets/img/monitor/fence.png');
      &.active {
        background-image: url('~@/assets/img/monitor/fence_1.png');
      }
    }
    /*.icon_traffic {*/
    /*  width: 13px;*/
    /*  height: 23px;*/
    /*  background-image: url('~@/assets/img/icon/traffic.png');*/
    /*  &.active {*/
    /*    background-image: url('~@/assets/img/icon/traffic2.png');*/
    /*  }*/
    /*}*/
    .icon_line {
      width: 20px;
      height: 19px;
      background-image: url('~@/assets/img/icon/manage_line.png');
      &.active {
        background-image: url('~@/assets/img/icon/manage_line_hover.png');
      }
    }
    .icon_tools {
      width: 19px;
      height: 19px;
      text-align: center;
      display: inline-block;
      background-image: url('~@/assets/img/icon/tools.png');
      &.active {
        background-image: url('~@/assets/img/icon/tools_hover.png');
      }
    }
    /*.icon_ruler {*/
    /*  width: 21px;*/
    /*  height: 20px;*/
    /*  background-image: url('~@/assets/img/icon/ruler.png');*/
    /*  &.active {*/
    /*    background-image: url('~@/assets/img/icon/ruler2.png');*/
    /*  }*/
    /*}*/
    .icon_poi {
      width: 21px;
      height: 10px;
      background-image: url('~@/assets/img/icon/poi.png');
      &.active {
        background-image: url('~@/assets/img/icon/poi2.png');
      }
    }
    .icon_drown {
      width: 15px;
      height: 14px;
      background-image: url('~@/assets/img/icon/drown.png');
      transform: rotateZ(-180deg);
      transition: transform 0.5s;
      &.rolate {
        transform: rotateZ(0deg);
      }
    }
    &.type:hover {
      .mapTypeMenu {
        display: block;
      }
    }
    &:hover {
      .icon_map {
        background-image: url('~@/assets/img/icon/defaultmap2.png');
      }
      .icon_satellite {
        background-image: url('~@/assets/img/icon/satellite2.png');
      }
      .icon_fence {
        background-image: url('~@/assets/img/monitor/fence_1.png');
      }
      .icon_line {
        background-image: url('~@/assets/img/icon/manage_line_hover.png');
      }
      .icon_tools {
        background-image: url('~@/assets/img/icon/tools_hover.png');
      }
      /*.icon_traffic {*/
      /*  background-image: url('~@/assets/img/icon/traffic2.png');*/
      /*}*/
      /*.icon_ruler {*/
      /*  background-image: url('~@/assets/img/icon/ruler2.png');*/
      /*}*/
      .icon_poi {
        background-image: url('~@/assets/img/icon/poi2.png');
      }
      .icon_drown {
        background-image: url('~@/assets/img/icon/drown2.png');
      }
    }
    .mapTypeMenu {
      display: none;
      position: absolute;
      right: 40px;
      top: 0;
      min-width: 94px;
      font-size: 12px;
      padding-right: 7px;
      padding-left: 15px;
      box-sizing: border-box;
      z-index: 10;
      &::before {
        position: absolute;
        content: '';
        right: 0;
        top: 14px;
        width: 0;
        height: 0;
        border-left: 7px solid #ffffff;
        border-top: 7px solid transparent;
        border-bottom: 7px solid transparent;
      }
      .menu {
        background-color: #ffffff;
        border-radius: 4px;
        box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.15);
        overflow: hidden;
      }
      .mapitem {
        padding: 0 16px;
        cursor: pointer;
        line-height: 34px;
        width: 100%;
        box-sizing: border-box;
        user-select: none;
        color: #333333;
        font-size: 14px;
        white-space: nowrap;
        &.active {
          color: $primary;
          background-color: #f5faff;
        }
        &:hover {
          color: $primary;
          background-color: #f5faff;
        }
      }
    }
  }
  .show-type-menu {
    display: block !important ;
  }
  .hide-type-menu {
    display: none !important;
  }
}
#mapSearchControl {
  display: flex;
  justify-content: center;
  position: absolute;
  right: 28px;
  top: 70px;
  width: 40px;
  background: #ffffff;
  border-radius: 8px;
  padding: 10px 6px;
  box-sizing: border-box;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.15);
  font-size: 18px;
  color: #999999;
  cursor: pointer;
  // 线路管理页面下隐藏按钮，搜索输入框常驻
  &.isLineSettingMode {
    display: none;
  }
  &:hover {
    ::v-deep .svg__fill {
      fill: $primary;
    }
  }
}
#mapRobotControl {
  position: absolute;
  right: 28px;
  top: 370px;
  width: 40px;
  background: #ffffff;
  border-radius: 8px;
  padding: 14px 6px;
  box-sizing: border-box;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.15);
  font-size: 12px;
  color: $controlColor;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  transition: all 0.3s;
  div {
    width: 100%;
    &:hover {
      color: $primary;
      ::v-deep .svg__fill {
        fill: $primary;
      }
    }
  }
  .desc-text {
    display: none;
  }
  &.showTxt {
    .desc-text {
      display: block;
    }
    div {
      height: 40px;
    }
  }
}
.mapRobotControl--move {
  transform: translateY(200px);
}
#mapSearchInput {
  z-index: 100;
  position: absolute;
  display: flex;
  flex-direction: row;
  align-items: center;
  right: 70px;
  top: 70px;
  width: 248px;
  background: #ffffff;
  border-radius: 8px;
  height: 40px;
  padding-left: 6px;
  box-sizing: border-box;
  box-shadow: 1px 3px 9px 0px rgba(0, 0, 0, 0.1);
  // 线路管理页面下，地址搜索框位置调整
  &.isLineSettingMode {
    left: 366px;
    top: 20px;
  }
  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    font-size: 18px;
    color: $primary;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.05);
    margin-left: 1px;
    cursor: pointer;
    .nav_img:hover {
      content: url('~@/assets/img/monitor/location_1.png');
    }
    img {
      width: 18px;
      height: 18px;
    }
    // 线路管理页面隐藏 线路管理导航按钮
    &.isLineSettingMode {
      display: none;
    }
  }
  #searchInputBaidu {
    border: none;
    outline: none;
    width: 200px;
    height: 36px;
    line-height: 36px;
  }
  #searchInputGoogle {
    border: none;
    outline: none;
    width: 200px;
    height: 36px;
    line-height: 36px;
  }
}
#mapZoomControl {
  position: absolute;
  right: 28px;
  bottom: 50px;
  width: 40px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.15);
  z-index: 10;
  .addZoom,
  .descZoom {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    cursor: pointer;
    user-select: none;
    &:hover {
      ::v-deep .svg__fill {
        fill: $primary;
      }
    }
  }
  .addZoom {
    &::after {
      content: '';
      position: absolute;
      left: 10px;
      bottom: 0;
      width: 20px;
      height: 1px;
      background-color: #dfdfdf;
    }
  }
}
#alarmBtn {
  position: absolute;
  right: 28px;
  bottom: 74px;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #3f7efd;
  cursor: pointer;
  z-index: 10;
  img {
    position: relative;
    display: block;
    width: 21px;
    margin: 0 auto;
    height: 27px;
    top: 6px;
  }
  .alarmCount {
    position: absolute;
    width: 7px;
    height: 7px;
    //line-height: 23px;
    right: 9px;
    top: 7px;
    background-color: #ff6648;
    border-radius: 50%;
    color: #ffffff;
    font-size: 12px;
    text-align: center;
  }
}
#alarmTooltip {
  position: absolute;
  background-color: #ffffff;
  border-radius: 6px;
  height: 55px;
  line-height: 55px;
  right: 60px;
  bottom: 67px;
  color: #262626;
  font-size: 16px;
  font-weight: 500;
  padding: 0 24px;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  .alarm-message-icon {
    vertical-align: sub;
    width: 22px;
    height: 22px;
    color: $primary;
    padding-right: 5px;
  }
  .el-icon-close {
    padding-left: 27px;
    &:hover {
      color: $primary;
    }
  }
}
::v-deep .parent-service-modal {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
  .service-content {
    position: relative;
    padding: 20px 24px 30px 24px;
    .close {
      position: absolute;
      top: 16px;
      right: 24px;
      width: 16px;
      height: 16px;
      background-image: url('~@/assets/img/close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    .content-top {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      margin-right: 20px;
      .user-avatar {
        height: 46px;
        width: 46px;
        border-radius: 50%;
      }
      .right {
        display: flex;
        flex-direction: column;
        margin-left: 20px;
        .row {
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          margin-bottom: 12px;
          font-size: 14px;
          line-height: 14px;
          font-weight: 400;
          color: #333333;
          .label {
            min-width: 60px;
            text-align: right;
          }
          .txt {
            display: flex;
            flex: 1;
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    .content-bottom {
      margin-top: 27px;
      .tips {
        font-size: 12px;
        font-weight: 400;
        color: #3f7efd;
        padding: 14px 12px;
        border-radius: 8px;
        background-color: #f7fafe;
      }
    }
  }
}
::v-deep .monitor-bind-fence {
  border-radius: 2px;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
::v-deep .monitor-area-chose {
  border-radius: 2px;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
  .fenceHeader {
    position: relative;
    height: 56px;
    border-bottom: 1px solid #f6f6f6;
    padding: 0 24px;
    box-sizing: border-box;
    font-weight: 500;
    color: #333333;
    font-size: 16px;
    line-height: 56px;
    .close {
      position: absolute;
      top: 20px;
      right: 24px;
      width: 16px;
      height: 16px;
      background-image: url('~@/assets/img/close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
  }
  .fenceBody {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 24px 32px;
    border-bottom: 1px solid #f6f6f6;
    font-size: 14px;
    color: #666666;
    cursor: default;
    .el-input__inner {
      width: 147px;
    }
    .el-icon-arrow-up:before {
      content: '\e6e1';
    }
  }
  .fenceBottom {
    position: relative;
    height: 52px;
    line-height: 52px;
    text-align: right;
    padding: 0 16px;
    .btn {
      display: inline-block;
      border: 1px solid #d9d9d9;
      font-size: 14px;
      padding: 0 16px;
      height: 32px;
      line-height: 32px;
      box-sizing: border-box;
      margin-left: 8px;
      letter-spacing: 4px;
      border-radius: 4px;
      cursor: pointer;
      user-select: none;
    }
    .cancel {
      color: #666666;
    }
    .confirm {
      color: #ffffff;
      background-color: $primary;
      border: 1px solid $primary;
    }
  }
}
::v-deep .alarm-table-modal {
  border-radius: 8px;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0px 10px;
  }
  .alarm-header {
    position: absolute;
    border-bottom: 1px solid #f6f6f6;
    padding: 0 14px;
    box-sizing: border-box;
    font-weight: 500;
    color: #333333;
    font-size: 16px;
    line-height: 56px;
    right: 0;
    top: 5px;
    .audio-icon {
      color: #817878;
      position: absolute;
      top: 21px;
      right: 61px;
      cursor: pointer;
      opacity: 0.5;
      &:hover {
        color: $primary;
      }
    }
    .close {
      position: absolute;
      top: 20px;
      right: 24px;
      width: 16px;
      height: 16px;
      background-image: url('~@/assets/img/close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
  }
  .alarm-body {
    .el-table td,
    .el-table th.is-leaf {
      border-bottom: none;
    }
    .el-table__header-wrapper {
      th {
        // background-color: #f7fafe;
        // color: #333333;
        font-weight: 400;
      }
    }
    .el-table--striped .el-table__body tr.el-table__row--striped td {
      // background: #f7fafe;
    }
    .el-table__body-wrapper {
      &::-webkit-scrollbar {
        width: 7px;
        height: 7px;
        background-color: #fff;
        cursor: pointer;
        border-radius: 8px;
      }
      &::-webkit-scrollbar-thumb {
        border-style: dashed;
        background-color: rgba(157, 165, 183, 0.4);
        border-color: transparent;
        border-width: 2px;
        background-clip: padding-box;
      }
    }
    .address {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .addrbtn {
      // color: $primary;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .alarm-bottom {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 20px;
    justify-content: right;
  }
}

.manage-line-modal {
  position: absolute;
  right: 60px;
  top: 124px;
  width: 385px;
  background: #ffffff;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  margin-top: 0px !important;
  ::v-deep .manage-line-body {
    .search-line {
      .search-img {
        width: 18px;
        background-size: 18px;
      }
      .el-input__suffix {
        display: flex;
        align-items: center;
        margin-right: 10px;
        cursor: pointer;
      }
    }
    .btn-tools {
      margin-top: 10px;
      margin-bottom: 20px;
      .add-line {
        background: #3370ff;
        .button-text {
          color: #ffffff;
        }
      }
      .search-svg {
        margin-right: 4px;
        width: 14px;
        height: 14px;
        /*margin-bottom: 2px;*/
      }
    }
    .el-table {
      height: 510px;
    }
    .line-pagination {
      margin-top: 20px;
      text-align: end;
    }
  }
}
.out_fence {
  height: 12px;
  min-width: 12px;
}
</style>

<style lang="scss">
@import './map.css';
@import './battery.scss';

.simpleCarTip {
  /* min-width: 327px;
  min-height: 104px; */
  font-size: 12px;
  border-radius: 8px;
  padding: 15px;
  /* padding: 10px 30px 15px 30px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0px 12px 48px 16px rgba(0, 0, 0, 0.03), 0px 9px 28px 0px rgba(0, 0, 0, 0.05), 0px 6px 16px -8px rgba(0, 0, 0, 0.08); */
  white-space: nowrap;
  width: 280px;
  box-sizing: border-box;
  .close {
    position: absolute;
    top: 15px;
    right: 20px;
    width: 16px;
    height: 16px;
    background-image: url('~@/assets/img/close.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: transform 0.3s;
  }
  .carname {
    line-height: 22px;
    font-size: 12px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 14px;
  }
  .hide {
    display: none !important;
  }
  .row {
    position: relative;
    display: flex;
    flex-direction: row;
    line-height: 14px;
    color: #666666;
    margin-bottom: 15px;
    &:last-child {
      margin-bottom: 0;
    }
    .col {
      display: flex;
      width: 50%;
      align-items: center;
    }
    .col-45 {
      display: flex;
      width: 45%;
      align-items: center;
    }
    .col-55 {
      display: flex;
      width: 55%;
      align-items: center;
    }
    .col-4 {
      margin-left: -11px;
    }
    img {
      display: inline-block;
      margin-right: 3px;
    }
    .img1 {
      height: 14px;
      width: 14px;
    }
    .img2 {
      width: 14px;
      height: 13px;
    }
    .img3 {
      width: 14px;
      height: 14px;
    }
    .img4 {
      margin-left: -2px;
      width: 15px;
      height: 15px;
    }
  }
}
.stopMarkerTip {
  color: #666666;
  p {
    line-height: 14px;
  }
}
.maptalks-attribution {
  padding: 2px 4px;
  font-size: 10px;
}
.google_logo {
  position: absolute;
  right: 2px;
  bottom: 0px;
  z-index: 1000;
}
.el-popover.tools_popper {
  padding: 0px !important;
  min-width: 96px;
  .tools_box {
    font-size: 14px;
    font-weight: 400;
    color: #7f7f7f;
    line-height: 34px;
    border-radius: 6px 6px 0px 0px;
    div {
      padding: 0px 16px;
      cursor: pointer;
    }
    div:hover {
      color: $hover;
      background: #ebf3ff;
    }
  }
}
</style>
<style>
.track__container {
  display: flex;
  justify-content: flex-end !important;
}
</style>
